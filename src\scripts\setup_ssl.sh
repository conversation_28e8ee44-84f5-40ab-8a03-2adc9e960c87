#!/bin/bash
# Arena Doviz Complete SSL Certificate Setup Script

set -euo pipefail

# Configuration
DOMAIN="${1:-arena-doviz.com}"
EMAIL="${2:-<EMAIL>}"
WEBROOT="/var/www/arena-doviz"
NGINX_CONF="/etc/nginx/sites-available/arena-doviz"
SSL_DIR="/etc/ssl/arena-doviz"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
    exit 1
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   error "This script must be run as root"
fi

# Validate domain
if [[ ! "$DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
    error "Invalid domain format: $DOMAIN"
fi

log "Setting up SSL certificate for domain: $DOMAIN"
log "Contact email: $EMAIL"

# Install Certbot if not present
if ! command -v certbot &> /dev/null; then
    log "Installing Certbot..."
    
    if command -v apt-get &> /dev/null; then
        # Debian/Ubuntu
        apt-get update
        apt-get install -y certbot python3-certbot-nginx
    elif command -v yum &> /dev/null; then
        # RHEL/CentOS
        yum install -y epel-release
        yum install -y certbot python3-certbot-nginx
    elif command -v dnf &> /dev/null; then
        # Fedora
        dnf install -y certbot python3-certbot-nginx
    else
        error "Unsupported package manager. Please install Certbot manually."
    fi
    
    log "Certbot installed successfully"
else
    log "Certbot is already installed"
fi

# Create SSL directory
mkdir -p "$SSL_DIR"
chmod 700 "$SSL_DIR"

# Create webroot directory
mkdir -p "$WEBROOT"
chown -R www-data:www-data "$WEBROOT" 2>/dev/null || chown -R nginx:nginx "$WEBROOT" 2>/dev/null || true

# Create temporary Nginx configuration for certificate validation
TEMP_NGINX_CONF="/etc/nginx/sites-available/arena-doviz-temp"
cat > "$TEMP_NGINX_CONF" << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    location /.well-known/acme-challenge/ {
        root $WEBROOT;
    }
    
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}
EOF

# Enable temporary configuration
if [[ -d "/etc/nginx/sites-enabled" ]]; then
    ln -sf "$TEMP_NGINX_CONF" "/etc/nginx/sites-enabled/arena-doviz-temp"
    rm -f "/etc/nginx/sites-enabled/arena-doviz" 2>/dev/null || true
fi

# Test Nginx configuration
if ! nginx -t; then
    error "Nginx configuration test failed"
fi

# Reload Nginx
systemctl reload nginx || error "Failed to reload Nginx"

log "Temporary Nginx configuration activated"

# Obtain SSL certificate
log "Obtaining SSL certificate from Let's Encrypt..."

if certbot certonly \
    --webroot \
    --webroot-path="$WEBROOT" \
    --email "$EMAIL" \
    --agree-tos \
    --no-eff-email \
    --domains "$DOMAIN,www.$DOMAIN" \
    --non-interactive; then
    
    log "SSL certificate obtained successfully"
else
    error "Failed to obtain SSL certificate"
fi

# Copy certificates to our SSL directory
CERT_DIR="/etc/letsencrypt/live/$DOMAIN"
cp "$CERT_DIR/fullchain.pem" "$SSL_DIR/arena-doviz.crt"
cp "$CERT_DIR/privkey.pem" "$SSL_DIR/arena-doviz.key"
cp "$CERT_DIR/chain.pem" "$SSL_DIR/arena-doviz-chain.crt"

# Set proper permissions
chmod 644 "$SSL_DIR/arena-doviz.crt"
chmod 600 "$SSL_DIR/arena-doviz.key"
chmod 644 "$SSL_DIR/arena-doviz-chain.crt"

log "SSL certificates copied to $SSL_DIR"

# Update Nginx configuration to use SSL
cp "nginx.conf.production" "$NGINX_CONF"

# Replace placeholder paths in Nginx config
sed -i "s|/etc/ssl/certs/arena-doviz.crt|$SSL_DIR/arena-doviz.crt|g" "$NGINX_CONF"
sed -i "s|/etc/ssl/private/arena-doviz.key|$SSL_DIR/arena-doviz.key|g" "$NGINX_CONF"
sed -i "s|/etc/ssl/certs/arena-doviz-chain.crt|$SSL_DIR/arena-doviz-chain.crt|g" "$NGINX_CONF"
sed -i "s|arena-doviz.com|$DOMAIN|g" "$NGINX_CONF"

# Enable production Nginx configuration
if [[ -d "/etc/nginx/sites-enabled" ]]; then
    rm -f "/etc/nginx/sites-enabled/arena-doviz-temp"
    ln -sf "$NGINX_CONF" "/etc/nginx/sites-enabled/arena-doviz"
fi

# Test Nginx configuration
if ! nginx -t; then
    error "Nginx SSL configuration test failed"
fi

# Reload Nginx with SSL configuration
systemctl reload nginx || error "Failed to reload Nginx with SSL configuration"

log "Production Nginx configuration with SSL activated"

# Create certificate renewal script
RENEWAL_SCRIPT="/usr/local/bin/arena-doviz-ssl-renewal.sh"
cat > "$RENEWAL_SCRIPT" << 'EOF'
#!/bin/bash
# Arena Doviz SSL Certificate Renewal Script

set -euo pipefail

DOMAIN="arena-doviz.com"
SSL_DIR="/etc/ssl/arena-doviz"
CERT_DIR="/etc/letsencrypt/live/$DOMAIN"

# Renew certificate
if certbot renew --quiet; then
    # Copy renewed certificates
    cp "$CERT_DIR/fullchain.pem" "$SSL_DIR/arena-doviz.crt"
    cp "$CERT_DIR/privkey.pem" "$SSL_DIR/arena-doviz.key"
    cp "$CERT_DIR/chain.pem" "$SSL_DIR/arena-doviz-chain.crt"
    
    # Set permissions
    chmod 644 "$SSL_DIR/arena-doviz.crt"
    chmod 600 "$SSL_DIR/arena-doviz.key"
    chmod 644 "$SSL_DIR/arena-doviz-chain.crt"
    
    # Reload Nginx
    systemctl reload nginx
    
    echo "SSL certificate renewed successfully"
else
    echo "SSL certificate renewal failed"
    exit 1
fi
EOF

chmod +x "$RENEWAL_SCRIPT"

# Create systemd service for SSL renewal
cat > "/etc/systemd/system/arena-doviz-ssl-renewal.service" << EOF
[Unit]
Description=Arena Doviz SSL Certificate Renewal
After=network.target

[Service]
Type=oneshot
ExecStart=$RENEWAL_SCRIPT
User=root
StandardOutput=journal
StandardError=journal
EOF

# Create systemd timer for SSL renewal
cat > "/etc/systemd/system/arena-doviz-ssl-renewal.timer" << EOF
[Unit]
Description=Arena Doviz SSL Certificate Renewal Timer
Requires=arena-doviz-ssl-renewal.service

[Timer]
# Run twice daily
OnCalendar=*-*-* 00,12:00:00
# Randomize start time by up to 1 hour
RandomizedDelaySec=3600
# Run on boot if missed
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Enable and start the renewal timer
systemctl daemon-reload
systemctl enable arena-doviz-ssl-renewal.timer
systemctl start arena-doviz-ssl-renewal.timer

log "SSL certificate renewal automation configured"

# Create SSL monitoring script
MONITOR_SCRIPT="/usr/local/bin/arena-doviz-ssl-monitor.sh"
cat > "$MONITOR_SCRIPT" << 'EOF'
#!/bin/bash
# Arena Doviz SSL Certificate Monitoring Script

DOMAIN="arena-doviz.com"
ALERT_EMAIL="<EMAIL>"
CERT_FILE="/etc/ssl/arena-doviz/arena-doviz.crt"

# Check certificate expiration
if [[ -f "$CERT_FILE" ]]; then
    EXPIRY_DATE=$(openssl x509 -enddate -noout -in "$CERT_FILE" | cut -d= -f2)
    EXPIRY_TIMESTAMP=$(date -d "$EXPIRY_DATE" +%s)
    CURRENT_TIMESTAMP=$(date +%s)
    DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))
    
    if [[ $DAYS_UNTIL_EXPIRY -lt 30 ]]; then
        echo "WARNING: SSL certificate for $DOMAIN expires in $DAYS_UNTIL_EXPIRY days"
        
        if command -v mail >/dev/null 2>&1; then
            echo "SSL certificate for $DOMAIN expires in $DAYS_UNTIL_EXPIRY days.
            
Certificate Details:
- Domain: $DOMAIN
- Expiry Date: $EXPIRY_DATE
- Days Until Expiry: $DAYS_UNTIL_EXPIRY

Please ensure automatic renewal is working properly." | \
            mail -s "SSL Certificate Expiry Warning - $DOMAIN" "$ALERT_EMAIL"
        fi
    else
        echo "SSL certificate for $DOMAIN is valid for $DAYS_UNTIL_EXPIRY more days"
    fi
else
    echo "ERROR: SSL certificate file not found: $CERT_FILE"
fi
EOF

chmod +x "$MONITOR_SCRIPT"

# Add SSL monitoring to crontab
(crontab -l 2>/dev/null; echo "0 6 * * * $MONITOR_SCRIPT") | crontab -

log "SSL certificate monitoring configured"

# Test SSL configuration
log "Testing SSL configuration..."

if curl -s -I "https://$DOMAIN" | grep -q "HTTP/2 200"; then
    log "SSL configuration test passed"
else
    warn "SSL configuration test failed - please check manually"
fi

# Display SSL certificate information
log "SSL Certificate Information:"
openssl x509 -in "$SSL_DIR/arena-doviz.crt" -text -noout | grep -E "(Subject:|Issuer:|Not Before:|Not After:)"

# Security recommendations
log "SSL Setup Complete!"
log ""
log "Security Recommendations:"
log "1. Test your SSL configuration: https://www.ssllabs.com/ssltest/"
log "2. Enable HSTS preloading: https://hstspreload.org/"
log "3. Monitor certificate expiration with external tools"
log "4. Regularly update Certbot and Nginx"
log "5. Consider using Certificate Transparency monitoring"
log ""
log "SSL Certificate Files:"
log "- Certificate: $SSL_DIR/arena-doviz.crt"
log "- Private Key: $SSL_DIR/arena-doviz.key"
log "- Chain: $SSL_DIR/arena-doviz-chain.crt"
log ""
log "Automatic Renewal:"
log "- Service: arena-doviz-ssl-renewal.service"
log "- Timer: arena-doviz-ssl-renewal.timer"
log "- Script: $RENEWAL_SCRIPT"
log "- Monitor: $MONITOR_SCRIPT"

exit 0
