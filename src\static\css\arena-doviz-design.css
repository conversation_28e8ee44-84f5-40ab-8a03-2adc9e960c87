/**
 * Arena Doviz Design System
 * Sharp/rectangular design with specific color scheme
 * 
 * Colors:
 * - Primary: #000d28 (dark navy)
 * - Secondary: #6a0000 (dark red) 
 * - Tertiary: #013121 (dark green)
 * - Background: #ffffff (white)
 */

/* ===== GLOBAL OVERRIDES ===== */

/* Remove ALL rounded corners */
*,
*::before,
*::after,
.btn,
.card,
.form-control,
.form-select,
.input-group-text,
.modal-content,
.modal-header,
.modal-body,
.modal-footer,
.alert,
.badge,
.dropdown-menu,
.nav-tabs .nav-link,
.pagination .page-link,
.progress,
.toast,
.tooltip,
.popover {
    border-radius: 0 !important;
}

/* Root color variables */
:root {
    --arena-primary: #000d28;
    --arena-secondary: #6a0000;
    --arena-tertiary: #013121;
    --arena-white: #ffffff;
    --arena-light-gray: #f8f9fa;
    --arena-border: #dee2e6;
}

/* ===== NAVIGATION & HEADER ===== */

/* Main navigation */
.navbar {
    background-color: var(--arena-primary) !important;
    border-bottom: 2px solid var(--arena-secondary);
}

.navbar-brand,
.navbar-nav .nav-link {
    color: var(--arena-white) !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: var(--arena-light-gray) !important;
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-toggler {
    border-color: var(--arena-white);
}

/* Dropdown menus */
.dropdown-menu {
    background-color: var(--arena-white);
    border: 1px solid var(--arena-primary);
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--arena-primary);
    color: var(--arena-white);
}

/* ===== BUTTONS ===== */

/* Primary buttons */
.btn-primary {
    background-color: var(--arena-primary) !important;
    border-color: var(--arena-primary) !important;
    color: var(--arena-white) !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: #001a3d !important;
    border-color: #001a3d !important;
}

/* Secondary/danger buttons */
.btn-secondary,
.btn-danger {
    background-color: var(--arena-secondary) !important;
    border-color: var(--arena-secondary) !important;
    color: var(--arena-white) !important;
}

.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active,
.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active {
    background-color: #8b0000 !important;
    border-color: #8b0000 !important;
}

/* Success buttons */
.btn-success {
    background-color: var(--arena-tertiary) !important;
    border-color: var(--arena-tertiary) !important;
    color: var(--arena-white) !important;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active {
    background-color: #014a2b !important;
    border-color: #014a2b !important;
}

/* Outline buttons */
.btn-outline-primary {
    color: var(--arena-primary) !important;
    border-color: var(--arena-primary) !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
    background-color: var(--arena-primary) !important;
    border-color: var(--arena-primary) !important;
    color: var(--arena-white) !important;
}

.btn-outline-secondary {
    color: var(--arena-secondary) !important;
    border-color: var(--arena-secondary) !important;
}

.btn-outline-secondary:hover,
.btn-outline-secondary:focus,
.btn-outline-secondary:active {
    background-color: var(--arena-secondary) !important;
    border-color: var(--arena-secondary) !important;
    color: var(--arena-white) !important;
}

.btn-outline-success {
    color: var(--arena-tertiary) !important;
    border-color: var(--arena-tertiary) !important;
}

.btn-outline-success:hover,
.btn-outline-success:focus,
.btn-outline-success:active {
    background-color: var(--arena-tertiary) !important;
    border-color: var(--arena-tertiary) !important;
    color: var(--arena-white) !important;
}

/* ===== CARDS ===== */

.card {
    border: 1px solid var(--arena-border);
    background-color: var(--arena-white);
}

.card-header {
    background-color: var(--arena-light-gray);
    border-bottom: 1px solid var(--arena-border);
    color: var(--arena-primary);
    font-weight: 600;
}

.card-title {
    color: var(--arena-primary);
    margin-bottom: 0;
}

/* ===== FORMS ===== */

.form-control,
.form-select {
    border: 1px solid var(--arena-border);
    background-color: var(--arena-white);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--arena-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 13, 40, 0.25);
}

.form-label {
    color: var(--arena-primary);
    font-weight: 500;
}

/* Input groups */
.input-group-text {
    background-color: var(--arena-light-gray);
    border: 1px solid var(--arena-border);
    color: var(--arena-primary);
}

/* ===== TABLES ===== */

.table {
    background-color: var(--arena-white);
}

.table thead th {
    background-color: var(--arena-primary);
    color: var(--arena-white);
    border-bottom: 2px solid var(--arena-primary);
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(0, 13, 40, 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(248, 249, 250, 0.5);
}

/* ===== BADGES ===== */

.badge.bg-primary {
    background-color: var(--arena-primary) !important;
}

.badge.bg-secondary {
    background-color: var(--arena-secondary) !important;
}

.badge.bg-success {
    background-color: var(--arena-tertiary) !important;
}

.badge.bg-danger {
    background-color: var(--arena-secondary) !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.badge.bg-info {
    background-color: var(--arena-primary) !important;
}

/* ===== ALERTS ===== */

.alert-primary {
    background-color: rgba(0, 13, 40, 0.1);
    border-color: var(--arena-primary);
    color: var(--arena-primary);
}

.alert-danger {
    background-color: rgba(106, 0, 0, 0.1);
    border-color: var(--arena-secondary);
    color: var(--arena-secondary);
}

.alert-success {
    background-color: rgba(1, 49, 33, 0.1);
    border-color: var(--arena-tertiary);
    color: var(--arena-tertiary);
}

/* ===== MODALS ===== */

.modal-header {
    background-color: var(--arena-primary);
    color: var(--arena-white);
    border-bottom: 1px solid var(--arena-border);
}

.modal-title {
    color: var(--arena-white);
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-footer {
    border-top: 1px solid var(--arena-border);
}

/* ===== PAGINATION ===== */

.page-link {
    color: var(--arena-primary);
    border-color: var(--arena-border);
}

.page-link:hover,
.page-link:focus {
    color: var(--arena-white);
    background-color: var(--arena-primary);
    border-color: var(--arena-primary);
}

.page-item.active .page-link {
    background-color: var(--arena-primary);
    border-color: var(--arena-primary);
    color: var(--arena-white);
}

/* ===== BREADCRUMBS ===== */

.breadcrumb {
    background-color: var(--arena-light-gray);
}

.breadcrumb-item a {
    color: var(--arena-primary);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--arena-secondary);
}

.breadcrumb-item.active {
    color: var(--arena-primary);
}

/* ===== PROGRESS BARS ===== */

.progress {
    background-color: var(--arena-light-gray);
}

.progress-bar {
    background-color: var(--arena-primary);
}

.progress-bar.bg-success {
    background-color: var(--arena-tertiary) !important;
}

.progress-bar.bg-danger {
    background-color: var(--arena-secondary) !important;
}

/* ===== TABS ===== */

.nav-tabs .nav-link {
    color: var(--arena-primary);
    border: 1px solid transparent;
}

.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
    border-color: var(--arena-border);
    color: var(--arena-secondary);
}

.nav-tabs .nav-link.active {
    color: var(--arena-white);
    background-color: var(--arena-primary);
    border-color: var(--arena-primary);
}

/* ===== ACCORDION ===== */

.accordion-button {
    background-color: var(--arena-light-gray);
    color: var(--arena-primary);
}

.accordion-button:not(.collapsed) {
    background-color: var(--arena-primary);
    color: var(--arena-white);
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(0, 13, 40, 0.25);
}

/* ===== LIST GROUPS ===== */

.list-group-item {
    border: 1px solid var(--arena-border);
}

.list-group-item.active {
    background-color: var(--arena-primary);
    border-color: var(--arena-primary);
    color: var(--arena-white);
}

.list-group-item:hover {
    background-color: rgba(0, 13, 40, 0.05);
}

/* ===== SIDEBAR ===== */

.sidebar {
    background-color: var(--arena-primary);
    color: var(--arena-white);
}

.sidebar .nav-link {
    color: var(--arena-white);
}

.sidebar .nav-link:hover,
.sidebar .nav-link:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--arena-white);
}

.sidebar .nav-link.active {
    background-color: var(--arena-secondary);
    color: var(--arena-white);
}

/* ===== DASHBOARD SPECIFIC ===== */

.dashboard-card {
    border-left: 4px solid var(--arena-primary);
}

.dashboard-card.success {
    border-left-color: var(--arena-tertiary);
}

.dashboard-card.danger {
    border-left-color: var(--arena-secondary);
}

.dashboard-stat-number {
    font-weight: 700;
    /* Color is inherited from parent card - don't override */
}

/* Dashboard card text overrides */
.dashboard-card .card-title,
.dashboard-card .dashboard-stat-number {
    color: var(--arena-primary) !important;
    font-weight: 600;
}

.dashboard-card .text-muted {
    color: #6c757d !important;
}

.dashboard-card i {
    opacity: 0.8;
}

/* ===== DATATABLES OVERRIDES ===== */

.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid var(--arena-border);
}

.dataTables_wrapper .dataTables_length select:focus,
.dataTables_wrapper .dataTables_filter input:focus {
    border-color: var(--arena-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 13, 40, 0.25);
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    color: var(--arena-primary) !important;
    border: 1px solid var(--arena-border);
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--arena-primary) !important;
    color: var(--arena-white) !important;
    border-color: var(--arena-primary) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--arena-primary) !important;
    color: var(--arena-white) !important;
    border-color: var(--arena-primary) !important;
}

/* ===== CHART.JS OVERRIDES ===== */

.chart-container {
    background-color: var(--arena-white);
    border: 1px solid var(--arena-border);
    padding: 1rem;
}

/* ===== CUSTOM COMPONENTS ===== */

/* Status indicators */
.status-completed {
    color: var(--arena-tertiary) !important;
}

.status-pending {
    color: #ffc107 !important;
}

.status-cancelled,
.status-rejected {
    color: var(--arena-secondary) !important;
}

.status-draft {
    color: #6c757d !important;
}

/* Transaction amount displays */
.amount-display {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.amount-positive {
    color: var(--arena-tertiary);
}

.amount-negative {
    color: var(--arena-secondary);
}

/* Currency symbols */
.currency-symbol {
    font-weight: 700;
    color: var(--arena-primary);
}

/* Action buttons in tables */
.btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin: 0 0.125rem;
}

/* Loading states */
.loading-overlay {
    background-color: rgba(0, 13, 40, 0.1);
}

.loading-spinner {
    border-color: var(--arena-primary);
    border-right-color: transparent;
}

/* Error states */
.error-message {
    color: var(--arena-secondary);
    background-color: rgba(106, 0, 0, 0.1);
    border: 1px solid var(--arena-secondary);
    padding: 0.75rem;
}

/* Success states */
.success-message {
    color: var(--arena-tertiary);
    background-color: rgba(1, 49, 33, 0.1);
    border: 1px solid var(--arena-tertiary);
    padding: 0.75rem;
}

/* ===== FORM VALIDATION ===== */

.form-control.is-valid {
    border-color: var(--arena-tertiary);
}

.form-control.is-valid:focus {
    border-color: var(--arena-tertiary);
    box-shadow: 0 0 0 0.2rem rgba(1, 49, 33, 0.25);
}

.form-control.is-invalid {
    border-color: var(--arena-secondary);
}

.form-control.is-invalid:focus {
    border-color: var(--arena-secondary);
    box-shadow: 0 0 0 0.2rem rgba(106, 0, 0, 0.25);
}

.valid-feedback {
    color: var(--arena-tertiary);
}

.invalid-feedback {
    color: var(--arena-secondary);
}

/* ===== TOOLTIPS & POPOVERS ===== */

.tooltip-inner {
    background-color: var(--arena-primary);
    color: var(--arena-white);
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--arena-primary);
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: var(--arena-primary);
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: var(--arena-primary);
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: var(--arena-primary);
}

.popover {
    border-color: var(--arena-primary);
}

.popover-header {
    background-color: var(--arena-primary);
    color: var(--arena-white);
    border-bottom-color: var(--arena-primary);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 768px) {
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn-action {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}
