"""
Signal handlers for Arena Doviz Transactions app.
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Transaction, BalanceEntry
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Transaction)
def transaction_post_save(sender, instance, created, **kwargs):
    """Handle transaction post-save signal."""
    if created:
        logger.info(f"New transaction created: {instance.transaction_number}")
    else:
        logger.info(f"Transaction updated: {instance.transaction_number}")


@receiver(post_delete, sender=Transaction)
def transaction_post_delete(sender, instance, **kwargs):
    """Handle transaction post-delete signal."""
    logger.info(f"Transaction deleted: {instance.transaction_number}")


@receiver(post_save, sender=BalanceEntry)
def balance_entry_post_save(sender, instance, created, **kwargs):
    """Handle balance entry post-save signal."""
    customer_info = instance.customer.customer_code if instance.customer else "Company"
    if created:
        logger.info(f"New balance entry created for customer {customer_info}")
    else:
        logger.info(f"Balance entry updated for customer {customer_info}")


@receiver(post_delete, sender=BalanceEntry)
def balance_entry_post_delete(sender, instance, **kwargs):
    """Handle balance entry post-delete signal."""
    customer_info = instance.customer.customer_code if instance.customer else "Company"
    logger.info(f"Balance entry deleted for customer {customer_info}")
