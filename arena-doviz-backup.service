[Unit]
Description=Arena Doviz Database Backup Service
Documentation=https://github.com/arena-doviz/exchange-accounting
After=postgresql.service
Wants=postgresql.service

[Service]
Type=oneshot
User=arena-doviz
Group=arena-doviz
WorkingDirectory=/opt/arena-doviz
EnvironmentFile=/opt/arena-doviz/.env.production

# Backup script
ExecStart=/opt/arena-doviz/scripts/backup_database.sh

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/backups/arena-doviz /var/log/arena-doviz

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=arena-doviz-backup
