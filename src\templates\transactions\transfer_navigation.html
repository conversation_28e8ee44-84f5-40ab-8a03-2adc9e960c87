{% extends 'base.html' %}
{% load i18n static %}

{% block title %}{{ page_title }} - Arena Doviz{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/transactions.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="bi bi-send"></i>
                {{ page_title }}
            </h1>
            <p class="text-muted mb-0">{% trans "Choose the type of money transfer you want to process" %}</p>
        </div>
    </div>

    <!-- Transfer Type Cards -->
    <div class="row">
        <!-- Internal Transfer -->
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <i class="bi bi-arrow-left-right text-primary" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="card-title">{% trans "Internal Transfer" %}</h5>
                    <p class="card-text text-muted">
                        {% trans "Transfer money between customers within the system. Quick and secure internal transactions." %}
                    </p>
                    <div class="mt-auto">
                        <a href="{% url 'transactions_web:internal_transfer_list' %}" class="btn btn-outline-primary me-2">
                            <i class="bi bi-list"></i> {% trans "View List" %}
                        </a>
                        <a href="{% url 'transactions_web:internal_transfer_add' %}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> {% trans "New Transfer" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- External Transfer -->
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <i class="bi bi-bank text-success" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="card-title">{% trans "External Transfer" %}</h5>
                    <p class="card-text text-muted">
                        {% trans "Transfer money to external banks and financial institutions. Includes bank details and routing information." %}
                    </p>
                    <div class="mt-auto">
                        <a href="{% url 'transactions_web:external_transfer_list' %}" class="btn btn-outline-primary me-2">
                            <i class="bi bi-list"></i> {% trans "View List" %}
                        </a>
                        <a href="{% url 'transactions_web:external_transfer_add' %}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> {% trans "New Transfer" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- International Transfer -->
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <i class="bi bi-globe text-warning" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="card-title">{% trans "International Transfer" %}</h5>
                    <p class="card-text text-muted">
                        {% trans "Cross-border money transfers with compliance requirements. Includes SWIFT and international banking details." %}
                    </p>
                    <div class="mt-auto">
                        <a href="{% url 'transactions_web:international_transfer_list' %}" class="btn btn-outline-primary me-2">
                            <i class="bi bi-list"></i> {% trans "View List" %}
                        </a>
                        <a href="{% url 'transactions_web:international_transfer_add' %}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> {% trans "New Transfer" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-graph-up"></i>
                        {% trans "Transfer Statistics" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-primary mb-1" id="internal-count">-</h4>
                                <small class="text-muted">{% trans "Internal Transfers" %}</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success mb-1" id="external-count">-</h4>
                                <small class="text-muted">{% trans "External Transfers" %}</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-warning mb-1" id="international-count">-</h4>
                                <small class="text-muted">{% trans "International Transfers" %}</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info mb-1" id="total-count">-</h4>
                            <small class="text-muted">{% trans "Total Transfers" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load transfer statistics
    loadTransferStats();
});

function loadTransferStats() {
    // This would typically load from an API endpoint
    // For now, showing placeholder values
    document.getElementById('internal-count').textContent = '0';
    document.getElementById('external-count').textContent = '0';
    document.getElementById('international-count').textContent = '0';
    document.getElementById('total-count').textContent = '0';
}
</script>
{% endblock %}
