{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ transaction_type.name }} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-{{ icon_class|default:'arrow-left-right' }}"></i>
                {{ transaction_type.name }}
            </h1>
            <div>
                <a href="{% url 'transactions_web:list' %}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-list"></i>
                    {% trans "All Transactions" %}
                </a>
                <a href="{% url 'transactions_web:type_add' transaction_type.code %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i>
                    {% trans "New" %} {{ transaction_type.name }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Type Description -->
{% if transaction_type.description %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            {{ transaction_type.description }}
        </div>
    </div>
</div>
{% endif %}

<!-- Filters Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {% trans "Filters" %}
                </h6>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">{% trans "All Statuses" %}</option>
                            <option value="draft">{% trans "Draft" %}</option>
                            <option value="pending">{% trans "Pending" %}</option>
                            <option value="approved">{% trans "Approved" %}</option>
                            <option value="completed">{% trans "Completed" %}</option>
                            <option value="cancelled">{% trans "Cancelled" %}</option>
                            <option value="rejected">{% trans "Rejected" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="customer" class="form-label">{% trans "Customer" %}</label>
                        <select class="form-select" id="customer" name="customer">
                            <option value="">{% trans "All Customers" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="location" class="form-label">{% trans "Location" %}</label>
                        <select class="form-select" id="location" name="location">
                            <option value="">{% trans "All Locations" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="currency" class="form-label">{% trans "Currency" %}</label>
                        <select class="form-select" id="currency" name="currency">
                            <option value="">{% trans "All Currencies" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">{% trans "Date From" %}</label>
                        <input type="date" class="form-control" id="date_from" name="date_from">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">{% trans "Date To" %}</label>
                        <input type="date" class="form-control" id="date_to" name="date_to">
                    </div>
                    <div class="col-md-6">
                        <label for="search" class="form-label">{% trans "Search" %}</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" name="search" placeholder="{% trans 'Transaction number, description, reference...' %}">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="bi bi-search"></i>
                            </button>
                            <button class="btn btn-outline-secondary" type="button" id="clear-filters">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {{ transaction_type.name }} {% trans "List" %}
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i>
                        {% trans "Refresh" %}
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                        <i class="bi bi-download"></i>
                        {% trans "Export" %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="transactions-table">
                        <thead>
                            <tr>
                                <th>{% trans "Transaction #" %}</th>
                                <th>{% trans "Customer" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Location" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Modal -->
<div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalLabel">{% trans "Transaction Action" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="action-form">
                    <input type="hidden" id="action-transaction-id" name="transaction_id">
                    <input type="hidden" id="action-type" name="action_type">
                    
                    <div class="mb-3">
                        <label for="action-notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="action-notes" name="notes" rows="3" placeholder="{% trans 'Optional notes for this action...' %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="confirm-action">{% trans "Confirm" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% url 'javascript-catalog' %}"></script>
<script>
// Global variables
let transactionsTable;
const transactionTypeCode = '{{ transaction_type.code }}';

// Authentication headers
function getAuthHeaders() {
    return {
        'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
        'Content-Type': 'application/json',
        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
    };
}

// Show alert message
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at the top of content
    $('.row').first().before(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// Load filter options
function loadFilterOptions() {
    // Load customers
    $.ajax({
        url: '/api/v1/customers/customers/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const customerSelect = $('#customer');
            customerSelect.empty().append('<option value="">{% trans "All Customers" %}</option>');
            data.results.forEach(function(customer) {
                customerSelect.append(`<option value="${customer.id}">${customer.first_name} ${customer.last_name}</option>`);
            });
        }
    });

    // Load locations
    $.ajax({
        url: '/api/v1/locations/locations/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const locationSelect = $('#location');
            locationSelect.empty().append('<option value="">{% trans "All Locations" %}</option>');
            data.results.forEach(function(location) {
                locationSelect.append(`<option value="${location.id}">${location.name}</option>`);
            });
        }
    });

    // Load currencies
    $.ajax({
        url: '/api/v1/currencies/currencies/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const currencySelect = $('#currency');
            currencySelect.empty().append('<option value="">{% trans "All Currencies" %}</option>');
            data.results.forEach(function(currency) {
                currencySelect.append(`<option value="${currency.id}">${currency.code} - ${currency.name}</option>`);
            });
        }
    });
}

// Initialize DataTable
function initializeTransactionsTable() {
    const columns = [
        { data: 'transaction_number', name: 'transaction_number' },
        { data: 'customer_name', name: 'customer__first_name' },
        { data: 'display_amount', name: 'from_amount' },
        { 
            data: 'status_display', 
            name: 'status',
            render: function(data, type, row) {
                const statusClass = {
                    'draft': 'secondary',
                    'pending': 'warning',
                    'approved': 'info',
                    'completed': 'success',
                    'cancelled': 'danger',
                    'rejected': 'danger'
                };
                return `<span class="badge bg-${statusClass[row.status] || 'secondary'}">${data}</span>`;
            }
        },
        { data: 'location_name', name: 'location__name' },
        {
            data: 'created_at',
            name: 'created_at',
            render: function(data, type, row) {
                return new Date(data).toLocaleString();
            }
        },
        {
            data: null,
            orderable: false,
            render: function(data, type, row) {
                return generateActionButtons(row);
            }
        }
    ];

    transactionsTable = $('#transactions-table').DataTable({
        responsive: true,
        processing: true,
        serverSide: true,
        order: [[5, 'desc']], // Order by created_at
        ajax: {
            url: '/api/v1/transactions/transactions/',
            type: 'GET',
            headers: {
                'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
            },
            data: function(d) {
                // Add transaction type filter
                const filters = {
                    transaction_type_code: transactionTypeCode
                };

                // Add other filter parameters
                const formData = new FormData($('#filter-form')[0]);
                for (let [key, value] of formData.entries()) {
                    if (value) {
                        filters[key] = value;
                    }
                }

                return Object.assign({
                    page: Math.floor(d.start / d.length) + 1,
                    page_size: d.length,
                    search: d.search.value,
                    ordering: d.order.length > 0 ?
                        (d.order[0].dir === 'desc' ? '-' : '') + columns[d.order[0].column].name :
                        '-created_at'
                }, filters);
            },
            dataSrc: function(json) {
                json.recordsTotal = json.count;
                json.recordsFiltered = json.count;
                return json.results;
            },
            error: function(xhr, error, thrown) {
                console.error('DataTable AJAX error:', error, thrown);
                showAlert('danger', '{% trans "Error loading transactions" %}');
            }
        },
        columns: columns,
        language: {
            processing: '{% trans "Loading..." %}',
            search: '{% trans "Search:" %}',
            lengthMenu: '{% trans "Show _MENU_ entries" %}',
            info: '{% trans "Showing _START_ to _END_ of _TOTAL_ entries" %}',
            infoEmpty: '{% trans "No entries found" %}',
            infoFiltered: '{% trans "(filtered from _MAX_ total entries)" %}',
            paginate: {
                first: '{% trans "First" %}',
                last: '{% trans "Last" %}',
                next: '{% trans "Next" %}',
                previous: '{% trans "Previous" %}'
            }
        }
    });
}

// Generate action buttons for each row
function generateActionButtons(row) {
    let buttons = `
        <div class="btn-group" role="group">
            <a href="/transactions/${row.id}/" class="btn btn-sm btn-outline-primary" title="{% trans 'View Details' %}">
                <i class="bi bi-eye"></i>
            </a>
    `;

    if (row.status === 'draft' || row.status === 'rejected') {
        buttons += `
            <a href="/transactions/${row.id}/edit/" class="btn btn-sm btn-outline-secondary" title="{% trans 'Edit' %}">
                <i class="bi bi-pencil"></i>
            </a>
        `;
    }

    if (row.can_be_approved) {
        buttons += `
            <button class="btn btn-sm btn-outline-success" onclick="showActionModal('${row.id}', 'approve')" title="{% trans 'Approve' %}">
                <i class="bi bi-check-circle"></i>
            </button>
        `;
    }

    if (row.can_be_cancelled) {
        buttons += `
            <button class="btn btn-sm btn-outline-danger" onclick="showActionModal('${row.id}', 'cancel')" title="{% trans 'Cancel' %}">
                <i class="bi bi-x-circle"></i>
            </button>
        `;
    }

    buttons += '</div>';
    return buttons;
}

// Show action modal
function showActionModal(transactionId, actionType) {
    $('#action-transaction-id').val(transactionId);
    $('#action-type').val(actionType);
    $('#actionModalLabel').text(`{% trans "Confirm" %} ${actionType.charAt(0).toUpperCase() + actionType.slice(1)}`);
    $('#actionModal').modal('show');
}

// Perform transaction action
function performTransactionAction() {
    const transactionId = $('#action-transaction-id').val();
    const actionType = $('#action-type').val();
    const notes = $('#action-notes').val();
    
    $.ajax({
        url: `/api/v1/transactions/transactions/${transactionId}/${actionType}/`,
        method: 'POST',
        headers: getAuthHeaders(),
        data: JSON.stringify({ notes: notes }),
        success: function(data) {
            $('#actionModal').modal('hide');
            transactionsTable.ajax.reload();
            showAlert('success', data.message || '{% trans "Action completed successfully" %}');
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || '{% trans "Action failed" %}';
            showAlert('danger', error);
        }
    });
}

// Export transactions
function exportTransactions() {
    const filters = {};
    const formData = new FormData($('#filter-form')[0]);
    for (let [key, value] of formData.entries()) {
        if (value) {
            filters[key] = value;
        }
    }
    filters.transaction_type_code = transactionTypeCode;
    filters.export = 'csv';

    const queryString = new URLSearchParams(filters).toString();
    window.open(`/api/v1/transactions/transactions/?${queryString}`, '_blank');
}

// Initialize page
$(document).ready(function() {
    loadFilterOptions();
    initializeTransactionsTable();

    // Event handlers
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        transactionsTable.ajax.reload();
    });

    $('#clear-filters').on('click', function() {
        $('#filter-form')[0].reset();
        transactionsTable.ajax.reload();
    });

    $('#refresh-btn').on('click', function() {
        transactionsTable.ajax.reload();
    });

    $('#export-btn').on('click', function() {
        exportTransactions();
    });

    $('#confirm-action').on('click', function() {
        performTransactionAction();
    });
});
</script>
{% endblock %}
