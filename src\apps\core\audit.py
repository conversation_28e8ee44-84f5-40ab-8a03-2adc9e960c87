"""
Comprehensive audit logging system for Arena Doviz.
Tracks user activities, transaction operations, and system events.
"""

import json
import logging
from typing import Dict, Any, Optional, List
from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone
from django.conf import settings
from .models import EncryptedAuditLog
from .encryption import mask_sensitive_value

logger = logging.getLogger(__name__)
User = get_user_model()


class AuditLogger:
    """
    Central audit logging service for Arena Doviz system.
    Provides comprehensive tracking of user activities and system events.
    """
    
    # Sensitive fields that should be masked in audit logs
    SENSITIVE_FIELDS = [
        'password', 'token', 'secret', 'key', 'pin', 'ssn', 'credit_card',
        'phone_number', 'email', 'address', 'notes', 'description',
        'reference_number', 'tracking_code', 'delivery_address',
        'whatsapp_group_id', 'employee_id', 'last_login_ip'
    ]
    
    def __init__(self):
        """Initialize audit logger."""
        self.enabled = getattr(settings, 'AUDIT_LOGGING_ENABLED', True)
        self.encrypt_sensitive_data = getattr(settings, 'AUDIT_ENCRYPT_SENSITIVE', True)
        self.mask_sensitive_fields = getattr(settings, 'AUDIT_MASK_SENSITIVE', True)
    
    def log_user_action(self, user, action_type: str, table_name: str, record_id: int,
                       old_values: Dict = None, new_values: Dict = None,
                       ip_address: str = None, user_agent: str = None,
                       success: bool = True, error_message: str = '') -> Optional[EncryptedAuditLog]:
        """
        Log a user action with comprehensive details.
        
        Args:
            user: User who performed the action
            action_type: Type of action (create, update, delete, etc.)
            table_name: Name of the affected table
            record_id: ID of the affected record
            old_values: Dictionary of old field values
            new_values: Dictionary of new field values
            ip_address: User's IP address
            user_agent: User's browser user agent
            success: Whether the action was successful
            error_message: Error message if action failed
            
        Returns:
            EncryptedAuditLog instance or None if logging failed
        """
        if not self.enabled:
            return None
        
        try:
            # Mask sensitive data if enabled
            if self.mask_sensitive_fields:
                old_values = self._mask_sensitive_data(old_values) if old_values else None
                new_values = self._mask_sensitive_data(new_values) if new_values else None
            
            # Create audit log entry
            audit_log = EncryptedAuditLog.log_action(
                action_type=action_type,
                table_name=table_name,
                record_id=record_id,
                user_id=user.id if user else None,
                old_values=old_values,
                new_values=new_values,
                ip_address=ip_address,
                user_agent=user_agent,
                success=success,
                error_message=error_message
            )
            
            logger.debug(f"User action logged: {action_type} on {table_name}[{record_id}] by user {user.id if user else 'system'}")
            return audit_log
            
        except Exception as e:
            logger.error(f"Failed to log user action: {str(e)}")
            return None
    
    def log_system_event(self, event_type: str, description: str, 
                        metadata: Dict = None, success: bool = True,
                        error_message: str = '') -> Optional[EncryptedAuditLog]:
        """
        Log a system event.
        
        Args:
            event_type: Type of system event
            description: Description of the event
            metadata: Additional metadata about the event
            success: Whether the event was successful
            error_message: Error message if event failed
            
        Returns:
            EncryptedAuditLog instance or None if logging failed
        """
        if not self.enabled:
            return None
        
        try:
            # Mask sensitive data in metadata
            if self.mask_sensitive_fields and metadata:
                metadata = self._mask_sensitive_data(metadata)
            
            audit_log = EncryptedAuditLog.log_action(
                action_type=EncryptedAuditLog.ActionType.SYSTEM,
                table_name='system',
                record_id=0,
                user_id=None,
                old_values=None,
                new_values={'event_type': event_type, 'description': description, 'metadata': metadata},
                ip_address=None,
                user_agent=None,
                success=success,
                error_message=error_message
            )
            
            logger.debug(f"System event logged: {event_type} - {description}")
            return audit_log
            
        except Exception as e:
            logger.error(f"Failed to log system event: {str(e)}")
            return None
    
    def log_transaction_event(self, user, transaction_id: int, event_type: str,
                             old_values: Dict = None, new_values: Dict = None,
                             ip_address: str = None, user_agent: str = None) -> Optional[EncryptedAuditLog]:
        """
        Log a transaction-related event.
        
        Args:
            user: User who performed the action
            transaction_id: ID of the transaction
            event_type: Type of transaction event
            old_values: Old transaction values
            new_values: New transaction values
            ip_address: User's IP address
            user_agent: User's browser user agent
            
        Returns:
            EncryptedAuditLog instance or None if logging failed
        """
        return self.log_user_action(
            user=user,
            action_type=EncryptedAuditLog.ActionType.TRANSACTION,
            table_name='transactions',
            record_id=transaction_id,
            old_values=old_values,
            new_values=new_values,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def log_balance_change(self, user, customer_id: int, currency_code: str,
                          old_balance: float, new_balance: float,
                          transaction_id: int = None, ip_address: str = None,
                          user_agent: str = None) -> Optional[EncryptedAuditLog]:
        """
        Log a balance change event.
        
        Args:
            user: User who caused the balance change
            customer_id: ID of the customer
            currency_code: Currency code
            old_balance: Previous balance
            new_balance: New balance
            transaction_id: Related transaction ID
            ip_address: User's IP address
            user_agent: User's browser user agent
            
        Returns:
            EncryptedAuditLog instance or None if logging failed
        """
        balance_data = {
            'customer_id': customer_id,
            'currency_code': currency_code,
            'old_balance': str(old_balance),
            'new_balance': str(new_balance),
            'change_amount': str(new_balance - old_balance),
            'transaction_id': transaction_id
        }
        
        return self.log_user_action(
            user=user,
            action_type=EncryptedAuditLog.ActionType.BALANCE_CHANGE,
            table_name='balance_entries',
            record_id=customer_id,
            old_values={'balance': str(old_balance)},
            new_values=balance_data,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def log_login_attempt(self, username: str, success: bool, ip_address: str = None,
                         user_agent: str = None, error_message: str = '') -> Optional[EncryptedAuditLog]:
        """
        Log a login attempt.
        
        Args:
            username: Username used for login
            success: Whether login was successful
            ip_address: User's IP address
            user_agent: User's browser user agent
            error_message: Error message if login failed
            
        Returns:
            EncryptedAuditLog instance or None if logging failed
        """
        try:
            # Try to get user ID if login was successful
            user_id = None
            if success:
                try:
                    user = User.objects.get(username=username)
                    user_id = user.id
                except User.DoesNotExist:
                    pass
            
            audit_log = EncryptedAuditLog.log_action(
                action_type=EncryptedAuditLog.ActionType.LOGIN,
                table_name='auth_user',
                record_id=user_id or 0,
                user_id=user_id,
                old_values=None,
                new_values={'username': username, 'timestamp': timezone.now().isoformat()},
                ip_address=ip_address,
                user_agent=user_agent,
                success=success,
                error_message=error_message
            )
            
            logger.debug(f"Login attempt logged: {username} - {'success' if success else 'failed'}")
            return audit_log
            
        except Exception as e:
            logger.error(f"Failed to log login attempt: {str(e)}")
            return None
    
    def log_logout(self, user, ip_address: str = None, user_agent: str = None) -> Optional[EncryptedAuditLog]:
        """
        Log a logout event.
        
        Args:
            user: User who logged out
            ip_address: User's IP address
            user_agent: User's browser user agent
            
        Returns:
            EncryptedAuditLog instance or None if logging failed
        """
        return self.log_user_action(
            user=user,
            action_type=EncryptedAuditLog.ActionType.LOGOUT,
            table_name='auth_user',
            record_id=user.id,
            old_values=None,
            new_values={'username': user.username, 'timestamp': timezone.now().isoformat()},
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def _mask_sensitive_data(self, data: Dict) -> Dict:
        """
        Mask sensitive data in a dictionary.
        
        Args:
            data: Dictionary containing data to mask
            
        Returns:
            Dictionary with sensitive fields masked
        """
        if not data:
            return data
        
        masked_data = data.copy()
        
        for field_name, value in masked_data.items():
            if field_name.lower() in [f.lower() for f in self.SENSITIVE_FIELDS]:
                if value and isinstance(value, str):
                    masked_data[field_name] = mask_sensitive_value(value)
                elif value:
                    masked_data[field_name] = '***MASKED***'
        
        return masked_data
    
    def get_audit_trail(self, table_name: str = None, record_id: int = None,
                       user_id: int = None, action_type: str = None,
                       start_date=None, end_date=None, limit: int = 100) -> List[EncryptedAuditLog]:
        """
        Get audit trail with filtering options.
        
        Args:
            table_name: Filter by table name
            record_id: Filter by record ID
            user_id: Filter by user ID
            action_type: Filter by action type
            start_date: Filter by start date
            end_date: Filter by end date
            limit: Maximum number of records to return
            
        Returns:
            List of EncryptedAuditLog instances
        """
        try:
            queryset = EncryptedAuditLog.objects.all()
            
            if table_name:
                queryset = queryset.filter(table_name=table_name)
            if record_id:
                queryset = queryset.filter(record_id=record_id)
            if user_id:
                queryset = queryset.filter(user_id=user_id)
            if action_type:
                queryset = queryset.filter(action_type=action_type)
            if start_date:
                queryset = queryset.filter(created_at__gte=start_date)
            if end_date:
                queryset = queryset.filter(created_at__lte=end_date)
            
            return list(queryset.order_by('-created_at')[:limit])
            
        except Exception as e:
            logger.error(f"Failed to get audit trail: {str(e)}")
            return []


# Global audit logger instance
_audit_logger = None


def get_audit_logger() -> AuditLogger:
    """
    Get the global audit logger instance (singleton pattern).
    
    Returns:
        AuditLogger: The audit logger instance
    """
    global _audit_logger
    
    if _audit_logger is None:
        _audit_logger = AuditLogger()
    
    return _audit_logger


# Convenience functions for common audit operations

def audit_user_action(user, action_type: str, table_name: str, record_id: int, **kwargs):
    """Convenience function to audit user actions."""
    return get_audit_logger().log_user_action(user, action_type, table_name, record_id, **kwargs)


def audit_system_event(event_type: str, description: str, **kwargs):
    """Convenience function to audit system events."""
    return get_audit_logger().log_system_event(event_type, description, **kwargs)


def audit_transaction_event(user, transaction_id: int, event_type: str, **kwargs):
    """Convenience function to audit transaction events."""
    return get_audit_logger().log_transaction_event(user, transaction_id, event_type, **kwargs)


def audit_balance_change(user, customer_id: int, currency_code: str, old_balance: float, new_balance: float, **kwargs):
    """Convenience function to audit balance changes."""
    return get_audit_logger().log_balance_change(user, customer_id, currency_code, old_balance, new_balance, **kwargs)


def audit_login_attempt(username: str, success: bool, **kwargs):
    """Convenience function to audit login attempts."""
    return get_audit_logger().log_login_attempt(username, success, **kwargs)


def audit_logout(user, **kwargs):
    """Convenience function to audit logout events."""
    return get_audit_logger().log_logout(user, **kwargs)
