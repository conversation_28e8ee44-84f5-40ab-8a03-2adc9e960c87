{"timestamp": "2025-08-20T03:39:45.357356", "production_components": {"C:\\Users\\<USER>\\Documents\\exchange-accounting\\.env.production": {"exists": true, "size_kb": 3.84, "description": "Environment configuration", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\nginx.conf.production": {"exists": true, "size_kb": 9.21, "description": "Nginx configuration", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\arena-doviz.service": {"exists": true, "size_kb": 1.14, "description": "Systemd service file", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\arena-doviz-backup.service": {"exists": true, "size_kb": 0.62, "description": "Backup service file", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\arena-doviz-backup.timer": {"exists": true, "size_kb": 0.36, "description": "Backup timer file", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\setup_production.py": {"exists": true, "size_kb": 7.65, "description": "Production setup script", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\setup_postgresql.py": {"exists": true, "size_kb": 13.38, "description": "PostgreSQL setup script", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\setup_ssl.sh": {"exists": true, "size_kb": 8.78, "description": "SSL setup script", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\deploy_production.py": {"exists": true, "size_kb": 19.1, "description": "Complete deployment script", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\backup_database.sh": {"exists": true, "size_kb": 5.51, "description": "Database backup script", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\security_audit.py": {"exists": true, "size_kb": 11.55, "description": "Security audit script", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\tests\\load_testing\\load_test.py": {"exists": true, "size_kb": 12.46, "description": "Load testing script", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\apps\\core\\monitoring.py": {"exists": true, "size_kb": 26.95, "description": "Monitoring system", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\templates\\monitoring\\dashboard.html": {"exists": true, "size_kb": 17.63, "description": "Monitoring dashboard", "status": "READY"}}, "transfer_forms": {"Internal Transfer": {"url": "/transactions/transfer/internal/add/", "status_code": 200, "accessible": true, "has_form": true}, "External Transfer": {"url": "/transactions/transfer/external/add/", "status_code": 200, "accessible": true, "has_form": true}, "International Transfer": {"url": "/transactions/transfer/international/add/", "status_code": 200, "accessible": true, "has_form": true}}, "monitoring_system": {"health_check": {"status_code": 200, "working": true}, "dashboard": {"status_code": 200, "working": true}, "monitoring.py": {"exists": true, "executable": true}, "health_check.py": {"exists": true, "executable": true}}, "security_features": {"security_audit": {"exists": true, "executable": true}, "production_settings": {"debug_disabled": true, "allowed_hosts_configured": true, "encryption_key_configured": true, "security_headers_configured": true}}, "performance_metrics": {"load_testing": {"exists": true, "size_kb": 12.46}, "database": {"response_time_ms": 0.12, "status": "GOOD"}}, "overall_status": "PRODUCTION_READY", "deployment_scripts": {"C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\deploy_production.py": {"exists": true, "size_kb": 19.1, "description": "Complete deployment automation", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\setup_production.py": {"exists": true, "size_kb": 7.65, "description": "Production environment setup", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\setup_postgresql.py": {"exists": true, "size_kb": 13.38, "description": "PostgreSQL setup", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\setup_ssl.sh": {"exists": true, "size_kb": 8.78, "description": "SSL certificate setup", "status": "READY"}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\scripts\\backup_database.sh": {"exists": true, "size_kb": 5.51, "description": "Database backup automation", "status": "READY"}}}