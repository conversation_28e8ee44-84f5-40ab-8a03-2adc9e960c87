#!/usr/bin/env python
"""
Production Fixes Verification Script for Arena Doviz
Tests all the fixes implemented for transaction forms in production environment.
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')
django.setup()

from apps.accounts.models import User
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken

class ProductionFixesTest:
    def __init__(self, base_url='http://localhost:8000'):
        self.base_url = base_url
        self.token = None
        self.headers = {}
        
    def authenticate(self):
        """Authenticate and get JWT token."""
        print("🔐 Testing Authentication...")
        
        # Get a superuser for testing
        superuser = User.objects.filter(is_superuser=True).first()
        if not superuser:
            print("❌ No superuser found. Please create one first.")
            return False
            
        # Generate JWT token
        refresh = RefreshToken.for_user(superuser)
        self.token = str(refresh.access_token)
        self.headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
        
        print(f"✅ Authenticated as: {superuser.username}")
        return True
    
    def test_database_data(self):
        """Test if database has required data."""
        print("\n📊 Testing Database Data...")
        
        users_count = User.objects.count()
        customers_count = Customer.objects.count()
        locations_count = Location.objects.count()
        currencies_count = Currency.objects.count()
        couriers_count = User.objects.filter(role='courier').count()
        
        print(f"Users: {users_count}")
        print(f"Customers: {customers_count}")
        print(f"Locations: {locations_count}")
        print(f"Currencies: {currencies_count}")
        print(f"Couriers: {couriers_count}")
        
        if customers_count == 0:
            print("⚠️  No customers found - dropdown will be empty")
        if couriers_count == 0:
            print("⚠️  No courier users found - courier dropdown will be empty")
            
        return True
    
    def test_api_endpoints(self):
        """Test API endpoints that are used by transaction forms."""
        print("\n🔗 Testing API Endpoints...")
        
        endpoints = [
            '/api/v1/customers/customers/',
            '/api/v1/locations/locations/',
            '/api/v1/currencies/currencies/',
            '/api/v1/accounts/users/?role=courier',
            '/api/v1/transactions/types/',
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", headers=self.headers, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    count = len(data.get('results', []))
                    print(f"✅ {endpoint} - {count} items")
                else:
                    print(f"❌ {endpoint} - Status: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint} - Error: {e}")
        
        return True
    
    def test_customer_balance_endpoint(self):
        """Test customer balance endpoint specifically."""
        print("\n💰 Testing Customer Balance Endpoint...")
        
        customer = Customer.objects.first()
        if not customer:
            print("❌ No customers found to test balance endpoint")
            return False
            
        endpoint = f'/api/v1/customers/customers/{customer.id}/balance/'
        try:
            response = requests.get(f"{self.base_url}{endpoint}", headers=self.headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Customer balance endpoint working - {len(data)} balance records")
                return True
            else:
                print(f"❌ Customer balance endpoint failed - Status: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Customer balance endpoint error: {e}")
            return False
    
    def test_static_files(self):
        """Test if static files are accessible."""
        print("\n📁 Testing Static Files...")
        
        static_files = [
            '/static/css/transactions.css',
            '/static/js/transactions/common.js',
            '/static/js/transactions/exchange.js',
            '/static/js/transactions/adjustment.js',
        ]
        
        for static_file in static_files:
            try:
                response = requests.get(f"{self.base_url}{static_file}", timeout=10)
                if response.status_code == 200:
                    print(f"✅ {static_file} - Accessible")
                else:
                    print(f"❌ {static_file} - Status: {response.status_code}")
            except Exception as e:
                print(f"❌ {static_file} - Error: {e}")
        
        return True
    
    def test_transaction_pages(self):
        """Test if transaction pages are accessible."""
        print("\n📄 Testing Transaction Pages...")
        
        pages = [
            '/transactions/',
            '/transactions/type/EXCHANGE/add/',
            '/transactions/type/DEPOSIT/add/',
            '/transactions/type/WITHDRAWAL/add/',
            '/transactions/type/ADJUSTMENT/add/',
            '/transactions/type/REMITTANCE/add/',
        ]
        
        for page in pages:
            try:
                response = requests.get(f"{self.base_url}{page}", timeout=10)
                if response.status_code == 200:
                    print(f"✅ {page} - Accessible")
                else:
                    print(f"❌ {page} - Status: {response.status_code}")
            except Exception as e:
                print(f"❌ {page} - Error: {e}")
        
        return True
    
    def run_all_tests(self):
        """Run all tests."""
        print("🚀 Starting Arena Doviz Production Fixes Verification")
        print("=" * 60)
        
        if not self.authenticate():
            return False
            
        self.test_database_data()
        self.test_api_endpoints()
        self.test_customer_balance_endpoint()
        self.test_static_files()
        self.test_transaction_pages()
        
        print("\n" + "=" * 60)
        print("✅ Production fixes verification completed!")
        print("\nNext steps:")
        print("1. Start the production server: python run_production.py")
        print("2. Visit http://localhost:8000/transactions/")
        print("3. Test each transaction form manually")
        print("4. Verify dropdowns populate correctly")
        print("5. Check UI colors are consistent (Arena Doviz theme)")
        
        return True

if __name__ == '__main__':
    tester = ProductionFixesTest()
    tester.run_all_tests()
