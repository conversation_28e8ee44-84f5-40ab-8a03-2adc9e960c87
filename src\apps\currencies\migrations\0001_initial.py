# Generated by Django 4.2.23 on 2025-08-13 08:44

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('locations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Currency',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.Boolean<PERSON>ield(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('code', models.CharField(help_text='ISO 4217 currency code (e.g., USD, AED, IRR)', max_length=3, unique=True, verbose_name='Currency code')),
                ('name', models.CharField(help_text='Full name of the currency (e.g., US Dollar)', max_length=100, verbose_name='Currency name')),
                ('symbol', models.CharField(help_text='Currency symbol (e.g., $, د.إ, ﷼)', max_length=10, verbose_name='Currency symbol')),
                ('decimal_places', models.PositiveSmallIntegerField(default=2, help_text='Number of decimal places for this currency', validators=[django.core.validators.MaxValueValidator(4)], verbose_name='Decimal places')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this currency is currently active', verbose_name='Is active')),
                ('is_base_currency', models.BooleanField(default=False, help_text='Whether this is the base currency for exchange rate calculations', verbose_name='Is base currency')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Order in which currencies should be displayed', verbose_name='Sort order')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this currency', verbose_name='Notes')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Currency',
                'verbose_name_plural': 'Currencies',
                'ordering': ['sort_order', 'code'],
            },
        ),
        migrations.CreateModel(
            name='ExchangeRate',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('buy_rate', models.DecimalField(decimal_places=6, help_text='Rate at which we buy the from_currency', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.000001'))], verbose_name='Buy rate')),
                ('sell_rate', models.DecimalField(decimal_places=6, help_text='Rate at which we sell the from_currency', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.000001'))], verbose_name='Sell rate')),
                ('effective_from', models.DateTimeField(help_text='Date and time when this rate becomes effective', verbose_name='Effective from')),
                ('effective_until', models.DateTimeField(blank=True, help_text='Date and time when this rate expires (null for current rate)', null=True, verbose_name='Effective until')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this exchange rate is currently active', verbose_name='Is active')),
                ('source', models.CharField(blank=True, help_text='Source of the exchange rate (e.g., Central Bank, Market)', max_length=100, verbose_name='Rate source')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this exchange rate', verbose_name='Notes')),
                ('approved_at', models.DateTimeField(blank=True, help_text='Date and time when this rate was approved', null=True, verbose_name='Approved at')),
                ('approved_by', models.ForeignKey(blank=True, help_text='User who approved this rate', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_rates', to=settings.AUTH_USER_MODEL, verbose_name='Approved by')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('from_currency', models.ForeignKey(help_text='Currency being exchanged from', on_delete=django.db.models.deletion.PROTECT, related_name='rates_from', to='currencies.currency', verbose_name='From currency')),
                ('location', models.ForeignKey(help_text='Location where this exchange rate applies', on_delete=django.db.models.deletion.PROTECT, related_name='exchange_rates', to='locations.location', verbose_name='Location')),
                ('to_currency', models.ForeignKey(help_text='Currency being exchanged to', on_delete=django.db.models.deletion.PROTECT, related_name='rates_to', to='currencies.currency', verbose_name='To currency')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Exchange Rate',
                'verbose_name_plural': 'Exchange Rates',
                'ordering': ['-effective_from'],
            },
        ),
        migrations.CreateModel(
            name='ExchangeRateHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('old_buy_rate', models.DecimalField(blank=True, decimal_places=6, max_digits=15, null=True, verbose_name='Old buy rate')),
                ('new_buy_rate', models.DecimalField(decimal_places=6, max_digits=15, verbose_name='New buy rate')),
                ('old_sell_rate', models.DecimalField(blank=True, decimal_places=6, max_digits=15, null=True, verbose_name='Old sell rate')),
                ('new_sell_rate', models.DecimalField(decimal_places=6, max_digits=15, verbose_name='New sell rate')),
                ('reason', models.TextField(blank=True, help_text='Reason for the rate change', verbose_name='Reason for change')),
                ('changed_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='rate_changes', to=settings.AUTH_USER_MODEL, verbose_name='Changed by')),
                ('exchange_rate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='currencies.exchangerate', verbose_name='Exchange rate')),
            ],
            options={
                'verbose_name': 'Exchange Rate History',
                'verbose_name_plural': 'Exchange Rate History',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['exchange_rate', 'created_at'], name='currencies__exchang_c260f0_idx'), models.Index(fields=['changed_by'], name='currencies__changed_106927_idx'), models.Index(fields=['created_at'], name='currencies__created_f8ccd4_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='exchangerate',
            index=models.Index(fields=['from_currency', 'to_currency', 'location'], name='currencies__from_cu_0431bd_idx'),
        ),
        migrations.AddIndex(
            model_name='exchangerate',
            index=models.Index(fields=['effective_from'], name='currencies__effecti_034859_idx'),
        ),
        migrations.AddIndex(
            model_name='exchangerate',
            index=models.Index(fields=['is_active'], name='currencies__is_acti_46eac5_idx'),
        ),
        migrations.AddIndex(
            model_name='exchangerate',
            index=models.Index(fields=['location'], name='currencies__locatio_6e2c7b_idx'),
        ),
        migrations.AddConstraint(
            model_name='exchangerate',
            constraint=models.UniqueConstraint(fields=('from_currency', 'to_currency', 'location', 'effective_from'), name='unique_rate_per_location_time'),
        ),
        migrations.AddConstraint(
            model_name='exchangerate',
            constraint=models.CheckConstraint(check=models.Q(('buy_rate__gt', 0), ('sell_rate__gt', 0)), name='positive_rates'),
        ),
        migrations.AddIndex(
            model_name='currency',
            index=models.Index(fields=['code'], name='currencies__code_712bc0_idx'),
        ),
        migrations.AddIndex(
            model_name='currency',
            index=models.Index(fields=['is_active'], name='currencies__is_acti_4f1b46_idx'),
        ),
        migrations.AddIndex(
            model_name='currency',
            index=models.Index(fields=['is_base_currency'], name='currencies__is_base_9c2ea9_idx'),
        ),
        migrations.AddIndex(
            model_name='currency',
            index=models.Index(fields=['sort_order'], name='currencies__sort_or_fdaf27_idx'),
        ),
        migrations.AddConstraint(
            model_name='currency',
            constraint=models.UniqueConstraint(condition=models.Q(('is_base_currency', True)), fields=('is_base_currency',), name='unique_base_currency'),
        ),
    ]
