"""
Management command to set up default report templates for Arena Doviz.
"""

from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from apps.reports.models import ReportTemplate


class Command(BaseCommand):
    help = 'Set up default report templates for Arena Doviz'

    def handle(self, *args, **options):
        """Set up default report templates."""
        
        templates = [
            {
                'name': 'Customer Statement',
                'report_type': ReportTemplate.ReportType.CUSTOMER_STATEMENT,
                'description': 'Detailed customer statement with transaction history',
                'template_config': {
                    'default_parameters': {
                        'format': 'pdf',
                        'include_balance': True,
                        'include_transactions': True,
                        'include_summary': True
                    },
                    'fields': [
                        'customer_info',
                        'statement_period',
                        'transaction_history',
                        'balance_summary',
                        'footer'
                    ]
                },
                'layout_config': {
                    'page_size': 'A4',
                    'orientation': 'portrait',
                    'margins': {
                        'top': 2.5,
                        'bottom': 2.5,
                        'left': 2.0,
                        'right': 2.0
                    },
                    'header': {
                        'include_logo': True,
                        'company_name': 'Arena Doviz Exchange',
                        'title': 'Customer Statement'
                    },
                    'footer': {
                        'include_page_numbers': True,
                        'include_generation_date': True,
                        'disclaimer': 'This statement is computer generated and does not require a signature.'
                    }
                },
                'is_public': True,
                'allowed_roles': ['admin', 'manager', 'operator'],
                'is_active': True,
                'sort_order': 1
            },
            {
                'name': 'Balance Summary Report',
                'report_type': ReportTemplate.ReportType.BALANCE_SUMMARY,
                'description': 'Summary of customer balances by currency and location',
                'template_config': {
                    'default_parameters': {
                        'format': 'pdf',
                        'group_by_currency': True,
                        'group_by_location': True,
                        'include_zero_balances': False
                    }
                },
                'layout_config': {
                    'page_size': 'A4',
                    'orientation': 'landscape'
                },
                'is_public': True,
                'allowed_roles': ['admin', 'manager'],
                'is_active': True,
                'sort_order': 2
            },
            {
                'name': 'Transaction Report',
                'report_type': ReportTemplate.ReportType.TRANSACTION_REPORT,
                'description': 'Detailed transaction report with filters',
                'template_config': {
                    'default_parameters': {
                        'format': 'pdf',
                        'include_documents': False,
                        'group_by_date': True
                    }
                },
                'layout_config': {
                    'page_size': 'A4',
                    'orientation': 'landscape'
                },
                'is_public': True,
                'allowed_roles': ['admin', 'manager', 'operator'],
                'is_active': True,
                'sort_order': 3
            },
            {
                'name': 'Daily Summary Report',
                'report_type': ReportTemplate.ReportType.DAILY_SUMMARY,
                'description': 'Daily summary of transactions and balances',
                'template_config': {
                    'default_parameters': {
                        'format': 'pdf',
                        'include_charts': True,
                        'include_commission_summary': True
                    }
                },
                'layout_config': {
                    'page_size': 'A4',
                    'orientation': 'portrait'
                },
                'is_public': True,
                'allowed_roles': ['admin', 'manager'],
                'is_active': True,
                'sort_order': 4
            },
            {
                'name': 'Profit & Loss Report',
                'report_type': ReportTemplate.ReportType.PROFIT_LOSS,
                'description': 'Profit and loss analysis report',
                'template_config': {
                    'default_parameters': {
                        'format': 'pdf',
                        'include_breakdown': True,
                        'include_charts': True
                    }
                },
                'layout_config': {
                    'page_size': 'A4',
                    'orientation': 'portrait'
                },
                'is_public': False,
                'allowed_roles': ['admin', 'manager'],
                'is_active': True,
                'sort_order': 5
            }
        ]

        created_count = 0
        updated_count = 0

        for template_data in templates:
            template, created = ReportTemplate.objects.get_or_create(
                report_type=template_data['report_type'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created report template: {template.name}')
                )
            else:
                # Update existing template with new configuration
                for key, value in template_data.items():
                    if key != 'report_type':  # Don't update the key field
                        setattr(template, key, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated report template: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Report template setup completed: {created_count} created, {updated_count} updated'
            )
        )
