<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Transfer Forms - Arena Doviz</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .debug-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .debug-log {
            background: #000;
            color: #0f0;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Arena Doviz Transfer Forms Debug</h1>
        
        <!-- Debug Panel -->
        <div class="debug-panel">
            <h5>Debug Console</h5>
            <div id="debug-log" class="debug-log"></div>
            <button class="btn btn-sm btn-secondary mt-2" onclick="clearDebugLog()">Clear Log</button>
        </div>
        
        <!-- Test Buttons -->
        <div class="row mb-4">
            <div class="col-md-12">
                <h5>API Tests</h5>
                <button class="btn btn-primary me-2" onclick="testCustomersAPI()">Test Customers API</button>
                <button class="btn btn-primary me-2" onclick="testLocationsAPI()">Test Locations API</button>
                <button class="btn btn-primary me-2" onclick="testCurrenciesAPI()">Test Currencies API</button>
                <button class="btn btn-primary me-2" onclick="testAuth()">Test Authentication</button>
            </div>
        </div>
        
        <!-- Mock Form -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Mock Internal Transfer Form</h5>
                    </div>
                    <div class="card-body">
                        <form id="test-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="customer" class="form-label">Customer</label>
                                        <select class="form-select" id="customer" name="customer">
                                            <option value="">Select customer...</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="location" class="form-label">Location</label>
                                        <select class="form-select" id="location" name="location">
                                            <option value="">Select location...</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="recipient_customer" class="form-label">Recipient Customer</label>
                                        <select class="form-select" id="recipient_customer" name="recipient_customer">
                                            <option value="">Select recipient...</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="from_currency" class="form-label">Currency</label>
                                        <select class="form-select" id="from_currency" name="from_currency">
                                            <option value="">Select currency...</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <button type="button" class="btn btn-success" onclick="loadAllData()">Load All Data</button>
                                <button type="button" class="btn btn-info" onclick="checkFormElements()">Check Form Elements</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Mock ArenaDoviz Auth -->
    <script>
        // Mock ArenaDoviz authentication object
        window.ArenaDoviz = {
            auth: {
                getAccessToken: function() {
                    // Return a mock token for testing
                    return 'mock-token-for-testing';
                },
                getCurrentUser: function() {
                    return {
                        id: 1,
                        username: 'testuser',
                        first_name: 'Test',
                        last_name: 'User',
                        display_name: 'Test User'
                    };
                }
            }
        };
        
        // Debug logging function
        function debugLog(message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            let logMessage = `[${timestamp}] ${message}`;
            
            if (data) {
                logMessage += '\n' + JSON.stringify(data, null, 2);
            }
            
            logElement.innerHTML += logMessage + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            
            // Also log to browser console
            if (data) {
                console.log(message, data);
            } else {
                console.log(message);
            }
        }
        
        function clearDebugLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        // Override console.log to also show in debug panel
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            debugLog('LOG: ' + args.join(' '));
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            debugLog('ERROR: ' + args.join(' '));
        };
        
        // Test functions
        function testAuth() {
            debugLog('Testing authentication...');
            const token = ArenaDoviz.auth.getAccessToken();
            const user = ArenaDoviz.auth.getCurrentUser();
            debugLog('Auth token:', token);
            debugLog('Current user:', user);
        }
        
        function testCustomersAPI() {
            debugLog('Testing Customers API...');
            $.ajax({
                url: 'http://127.0.0.1:8000/api/v1/customers/customers/',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
                    'Content-Type': 'application/json'
                },
                success: function(data) {
                    debugLog('Customers API Success:', data);
                },
                error: function(xhr, status, error) {
                    debugLog('Customers API Error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                }
            });
        }
        
        function testLocationsAPI() {
            debugLog('Testing Locations API...');
            $.ajax({
                url: 'http://127.0.0.1:8000/api/v1/locations/locations/',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
                    'Content-Type': 'application/json'
                },
                success: function(data) {
                    debugLog('Locations API Success:', data);
                },
                error: function(xhr, status, error) {
                    debugLog('Locations API Error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                }
            });
        }
        
        function testCurrenciesAPI() {
            debugLog('Testing Currencies API...');
            $.ajax({
                url: 'http://127.0.0.1:8000/api/v1/currencies/currencies/',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
                    'Content-Type': 'application/json'
                },
                success: function(data) {
                    debugLog('Currencies API Success:', data);
                },
                error: function(xhr, status, error) {
                    debugLog('Currencies API Error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                }
            });
        }
        
        function checkFormElements() {
            debugLog('Checking form elements...');
            const elements = ['customer', 'location', 'recipient_customer', 'from_currency'];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    debugLog(`Element #${id}: Found, options count: ${element.options.length}`);
                } else {
                    debugLog(`Element #${id}: NOT FOUND`);
                }
            });
        }
        
        function loadAllData() {
            debugLog('Loading all data...');
            testCustomersAPI();
            testLocationsAPI();
            testCurrenciesAPI();
        }
        
        // Initialize on page load
        $(document).ready(function() {
            debugLog('Page loaded, jQuery ready');
            testAuth();
            checkFormElements();
        });
    </script>
</body>
</html>
