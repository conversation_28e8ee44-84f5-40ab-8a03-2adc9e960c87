2025-08-16 05:48:11,206 - apps.transactions.signals - INFO - New transaction created: 20250816-0001
2025-08-16 05:48:11,210 - apps.transactions.signals - INFO - New balance entry created for customer CUS000001
2025-08-16 05:48:11,276 - apps.transactions.signals - INFO - New balance entry created for customer CUS000001
2025-08-16 05:48:55,222 - apps.transactions.signals - INFO - New transaction created: 20250816-0001
2025-08-16 05:48:55,228 - apps.transactions.signals - INFO - New balance entry created for customer CUS000002
2025-08-16 05:48:55,233 - apps.transactions.signals - INFO - New balance entry created for customer CUS000002
2025-08-16 05:48:55,235 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:48:55,238 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:48:55,241 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:48:55,244 - apps.transactions.models - INFO - Balance entries created for transaction 20250816-0001
2025-08-16 05:48:55,289 - apps.transactions.models - INFO - Transaction saved: 20250816-0001 - completed
2025-08-16 05:49:44,674 - apps.transactions.signals - INFO - New transaction created: 20250816-0001
2025-08-16 05:49:44,677 - apps.transactions.signals - INFO - New balance entry created for customer CUS000003
2025-08-16 05:49:44,680 - apps.transactions.signals - INFO - New balance entry created for customer CUS000003
2025-08-16 05:49:44,737 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:49:44,803 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:49:44,808 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:49:44,868 - apps.transactions.models - INFO - Balance entries created for transaction 20250816-0001
2025-08-16 05:49:44,869 - apps.transactions.models - INFO - Transaction saved: 20250816-0001 - completed
2025-08-16 05:49:44,928 - apps.transactions.signals - INFO - New transaction created: 20250816-0002
2025-08-16 05:49:44,929 - apps.transactions.signals - INFO - New balance entry created for customer CUS000001
2025-08-16 05:49:44,931 - apps.transactions.signals - INFO - New balance entry created for customer CUS000001
2025-08-16 05:49:44,968 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:49:44,991 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:49:45,025 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:49:45,056 - apps.transactions.models - INFO - Balance entries created for transaction 20250816-0002
2025-08-16 05:49:45,057 - apps.transactions.models - INFO - Transaction saved: 20250816-0002 - completed
2025-08-16 05:49:45,115 - apps.transactions.signals - INFO - New transaction created: 20250816-0003
2025-08-16 05:49:45,228 - apps.transactions.models - INFO - Transaction saved: 20250816-0003 - pending
2025-08-16 05:49:45,233 - apps.transactions.signals - INFO - New transaction created: 20250816-0004
2025-08-16 05:49:45,234 - apps.transactions.models - INFO - Transaction saved: 20250816-0004 - cancelled
2025-08-16 05:49:45,237 - apps.transactions.signals - INFO - New transaction created: 20250816-0005
2025-08-16 05:49:45,238 - apps.transactions.signals - INFO - New balance entry created for customer CUS000002
2025-08-16 05:49:45,241 - apps.transactions.signals - INFO - New balance entry created for customer CUS000002
2025-08-16 05:49:45,244 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:49:45,247 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:49:45,303 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-16 05:49:45,308 - apps.transactions.models - INFO - Balance entries created for transaction 20250816-0005
2025-08-16 05:49:45,308 - apps.transactions.models - INFO - Transaction saved: 20250816-0005 - completed
2025-08-18 00:13:10,651 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:13:16,774 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:13:22,171 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:13:49,498 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:15:06,372 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:17:01,039 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:17:02,533 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:17:05,895 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:17:11,129 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:17:13,568 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:17:15,026 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 00:18:17,800 - apps.transactions.commission_models - INFO - Commission calculated for transaction PREVIEW: $ 100.00 using rule CHN - DEPOSIT
2025-08-18 00:18:17,801 - apps.transactions.serializers - INFO - Auto-calculated commission: $ 100.00
2025-08-18 00:18:17,809 - apps.transactions.signals - INFO - New transaction created: 20250818-0001
2025-08-18 00:18:17,810 - apps.transactions.models - INFO - Transaction saved: 20250818-0001 - draft
2025-08-18 00:18:17,813 - apps.transactions.serializers - INFO - Transaction created: 20250818-0001 by admin (admin)
2025-08-18 01:11:54,954 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'transaction_type': '4b2acd1a-7790-4d6e-80be-9572a2ae08dc', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'from_amount': 800, 'delivery_method': ''}
2025-08-18 01:11:54,955 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('71593a1c-bfa9-48b5-8203-c9bd5552e0c7'), 'transaction_type': UUID('4b2acd1a-7790-4d6e-80be-9572a2ae08dc'), 'from_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'to_currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'from_amount': Decimal('800.000000'), 'delivery_method': ''}
2025-08-18 01:11:54,956 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 01:11:54,960 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 01:11:54,960 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-18 01:11:59,140 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'transaction_type': '4b2acd1a-7790-4d6e-80be-9572a2ae08dc', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'from_amount': 800, 'delivery_method': ''}
2025-08-18 01:11:59,141 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('71593a1c-bfa9-48b5-8203-c9bd5552e0c7'), 'transaction_type': UUID('4b2acd1a-7790-4d6e-80be-9572a2ae08dc'), 'from_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'to_currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'from_amount': Decimal('800.000000'), 'delivery_method': ''}
2025-08-18 01:11:59,143 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 01:11:59,146 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 01:11:59,146 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-18 01:12:05,244 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'transaction_type': '4b2acd1a-7790-4d6e-80be-9572a2ae08dc', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'from_amount': 800, 'delivery_method': 'courier'}
2025-08-18 01:12:05,244 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('71593a1c-bfa9-48b5-8203-c9bd5552e0c7'), 'transaction_type': UUID('4b2acd1a-7790-4d6e-80be-9572a2ae08dc'), 'from_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'to_currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'from_amount': Decimal('800.000000'), 'delivery_method': 'courier'}
2025-08-18 01:12:05,246 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 01:12:05,250 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 01:12:05,250 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-18 01:12:11,897 - apps.transactions.views - INFO - Transaction creation request from admin (admin): {'transaction_type': '4b2acd1a-7790-4d6e-80be-9572a2ae08dc', 'customer': '82ecffff-2a6a-4417-b379-98f042a1a25e', 'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'description': 'asd', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'from_amount': '800', 'exchange_rate': '3.675000', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'to_amount': '2940.000000', 'commission_amount': '0', 'delivery_method': 'courier', 'courier': '3f003cc8-7c47-4122-948f-58a96251d806', 'step_number': '1', 'total_steps': '1', 'document_type': 'other', 'status': 'pending'}
2025-08-18 01:12:11,908 - apps.transactions.views - INFO - Transaction creation attempt by admin (admin): {'transaction_type': '4b2acd1a-7790-4d6e-80be-9572a2ae08dc', 'customer': '82ecffff-2a6a-4417-b379-98f042a1a25e', 'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'description': 'asd', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'from_amount': '800', 'exchange_rate': '3.675000', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'to_amount': '2940.000000', 'commission_amount': '0', 'delivery_method': 'courier', 'courier': '3f003cc8-7c47-4122-948f-58a96251d806', 'step_number': '1', 'total_steps': '1', 'document_type': 'other', 'status': 'pending'}
2025-08-18 01:12:11,912 - apps.transactions.commission_models - INFO - Commission calculated for transaction PREVIEW: $ 8.00 using rule IST - EXCHANGE
2025-08-18 01:12:11,913 - apps.transactions.serializers - INFO - Auto-calculated commission: $ 8.00
2025-08-18 01:12:11,916 - apps.transactions.signals - INFO - New transaction created: 20250818-0002
2025-08-18 01:12:11,917 - apps.transactions.models - INFO - Transaction saved: 20250818-0002 - draft
2025-08-18 01:12:11,918 - apps.transactions.serializers - INFO - Transaction created: 20250818-0002 by admin (admin)
2025-08-18 01:12:11,921 - apps.transactions.views - INFO - Transaction created successfully: 20250818-0002
2025-08-18 01:12:12,998 - apps.transactions.views - INFO - Bulk document upload request from admin (admin): transaction_id=e0bc0c00-f20e-44b2-a743-d1aa2a900ba9, files=1
2025-08-18 01:12:13,001 - apps.transactions.views - WARNING - Bulk upload validation failed: {'files': {0: [ErrorDetail(string='The submitted data was not a file. Check the encoding type on the form.', code='invalid')]}}
2025-08-18 02:53:16,102 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': 'e66335d2-bd8f-4e70-80e8-9c4c3fb543cf', 'transaction_type': 'a99d8899-74ff-4334-8a6e-040a9869c76b', 'from_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'from_amount': 2300, 'delivery_method': ''}
2025-08-18 02:53:16,103 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('e66335d2-bd8f-4e70-80e8-9c4c3fb543cf'), 'transaction_type': UUID('a99d8899-74ff-4334-8a6e-040a9869c76b'), 'from_currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'to_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'from_amount': Decimal('2300.000000'), 'delivery_method': ''}
2025-08-18 02:53:16,106 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 02:53:16,111 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 02:53:16,111 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-18 02:53:22,830 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': 'e66335d2-bd8f-4e70-80e8-9c4c3fb543cf', 'transaction_type': 'a99d8899-74ff-4334-8a6e-040a9869c76b', 'from_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'from_amount': 2300, 'delivery_method': 'in_person'}
2025-08-18 02:53:22,831 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('e66335d2-bd8f-4e70-80e8-9c4c3fb543cf'), 'transaction_type': UUID('a99d8899-74ff-4334-8a6e-040a9869c76b'), 'from_currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'to_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'from_amount': Decimal('2300.000000'), 'delivery_method': 'in_person'}
2025-08-18 02:53:22,832 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 02:53:22,835 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 02:53:22,835 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-18 02:53:24,202 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': 'e66335d2-bd8f-4e70-80e8-9c4c3fb543cf', 'transaction_type': 'a99d8899-74ff-4334-8a6e-040a9869c76b', 'from_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'from_amount': 2300, 'delivery_method': 'courier'}
2025-08-18 02:53:24,204 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('e66335d2-bd8f-4e70-80e8-9c4c3fb543cf'), 'transaction_type': UUID('a99d8899-74ff-4334-8a6e-040a9869c76b'), 'from_currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'to_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'from_amount': Decimal('2300.000000'), 'delivery_method': 'courier'}
2025-08-18 02:53:24,205 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 02:53:24,210 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 02:53:24,211 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-18 02:53:36,917 - apps.transactions.views - INFO - Transaction creation request from admin (admin): {'transaction_type': 'a99d8899-74ff-4334-8a6e-040a9869c76b', 'customer': 'de103197-e43e-4a68-b648-79845a85ee7a', 'location': 'e66335d2-bd8f-4e70-80e8-9c4c3fb543cf', 'description': 'fffff', 'from_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'from_amount': '2300', 'exchange_rate': '0.272400', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'to_amount': '626.520000', 'commission_amount': '0', 'delivery_method': 'courier', 'courier': '3f003cc8-7c47-4122-948f-58a96251d806', 'step_number': '1', 'total_steps': '1', 'document_type': 'other', 'status': 'pending'}
2025-08-18 02:53:36,926 - apps.transactions.views - INFO - Transaction creation attempt by admin (admin): {'transaction_type': 'a99d8899-74ff-4334-8a6e-040a9869c76b', 'customer': 'de103197-e43e-4a68-b648-79845a85ee7a', 'location': 'e66335d2-bd8f-4e70-80e8-9c4c3fb543cf', 'description': 'fffff', 'from_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'from_amount': '2300', 'exchange_rate': '0.272400', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'to_amount': '626.520000', 'commission_amount': '0', 'delivery_method': 'courier', 'courier': '3f003cc8-7c47-4122-948f-58a96251d806', 'step_number': '1', 'total_steps': '1', 'document_type': 'other', 'status': 'pending'}
2025-08-18 02:53:37,134 - apps.transactions.signals - INFO - New transaction created: 20250818-0003
2025-08-18 02:53:37,135 - apps.transactions.models - INFO - Transaction saved: 20250818-0003 - draft
2025-08-18 02:53:37,136 - apps.transactions.serializers - INFO - Transaction created: 20250818-0003 by admin (admin)
2025-08-18 02:53:37,139 - apps.transactions.views - INFO - Transaction created successfully: 20250818-0003
2025-08-18 02:53:38,324 - apps.transactions.views - INFO - Bulk document upload request from admin (admin): transaction_id=9e19a8c3-8cd0-44e5-967d-85653b18b049, files=1
2025-08-18 02:53:38,327 - apps.transactions.views - WARNING - Bulk upload validation failed: {'files': {0: [ErrorDetail(string='The submitted data was not a file. Check the encoding type on the form.', code='invalid')]}}
2025-08-18 02:54:29,387 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'transaction_type': '4b2acd1a-7790-4d6e-80be-9572a2ae08dc', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'from_amount': 344, 'delivery_method': ''}
2025-08-18 02:54:29,387 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('71593a1c-bfa9-48b5-8203-c9bd5552e0c7'), 'transaction_type': UUID('4b2acd1a-7790-4d6e-80be-9572a2ae08dc'), 'from_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'to_currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'from_amount': Decimal('344.000000'), 'delivery_method': ''}
2025-08-18 02:54:29,389 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 02:54:29,394 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 02:54:29,394 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-18 02:54:31,926 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'transaction_type': '4b2acd1a-7790-4d6e-80be-9572a2ae08dc', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'from_amount': 344, 'delivery_method': 'courier'}
2025-08-18 02:54:31,926 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('71593a1c-bfa9-48b5-8203-c9bd5552e0c7'), 'transaction_type': UUID('4b2acd1a-7790-4d6e-80be-9572a2ae08dc'), 'from_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'to_currency': UUID('e01a9fbb-4b91-4a55-8dcb-60665974b17d'), 'from_amount': Decimal('344.000000'), 'delivery_method': 'courier'}
2025-08-18 02:54:31,927 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 02:54:31,930 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 02:54:31,931 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-18 02:54:39,418 - apps.transactions.views - INFO - Transaction creation request from admin (admin): {'transaction_type': '4b2acd1a-7790-4d6e-80be-9572a2ae08dc', 'customer': '4add5c82-e6c1-4cf6-9e49-9708910ecedc', 'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'description': 'dfgdfg', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'from_amount': '344', 'exchange_rate': '3.675000', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'to_amount': '1264.200000', 'commission_amount': '0', 'delivery_method': 'courier', 'courier': 'd26bb059-ada7-478e-8af9-d4fe6c547a03', 'step_number': '1', 'total_steps': '1', 'document_type': 'other', 'status': 'pending'}
2025-08-18 02:54:39,425 - apps.transactions.views - INFO - Transaction creation attempt by admin (admin): {'transaction_type': '4b2acd1a-7790-4d6e-80be-9572a2ae08dc', 'customer': '4add5c82-e6c1-4cf6-9e49-9708910ecedc', 'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'description': 'dfgdfg', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'from_amount': '344', 'exchange_rate': '3.675000', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'to_amount': '1264.200000', 'commission_amount': '0', 'delivery_method': 'courier', 'courier': 'd26bb059-ada7-478e-8af9-d4fe6c547a03', 'step_number': '1', 'total_steps': '1', 'document_type': 'other', 'status': 'pending'}
2025-08-18 02:54:39,429 - apps.transactions.commission_models - INFO - Commission calculated for transaction PREVIEW: $ 3.44 using rule IST - EXCHANGE
2025-08-18 02:54:39,432 - apps.transactions.serializers - INFO - Auto-calculated commission: $ 3.44
2025-08-18 02:54:39,438 - apps.transactions.signals - INFO - New transaction created: 20250818-0004
2025-08-18 02:54:39,440 - apps.transactions.models - INFO - Transaction saved: 20250818-0004 - draft
2025-08-18 02:54:39,443 - apps.transactions.serializers - INFO - Transaction created: 20250818-0004 by admin (admin)
2025-08-18 02:54:39,449 - apps.transactions.views - INFO - Transaction created successfully: 20250818-0004
2025-08-18 02:54:40,705 - apps.transactions.views - INFO - Bulk document upload request from admin (admin): transaction_id=27143e18-b94c-45d5-9c7c-6e3b60c2b63b, files=1
2025-08-18 02:54:40,708 - apps.transactions.views - WARNING - Bulk upload validation failed: {'files': {0: [ErrorDetail(string='The submitted data was not a file. Check the encoding type on the form.', code='invalid')]}}
2025-08-18 05:34:38,634 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'transaction_type': '74755ddc-b1c8-4a46-bc74-38337a16ec7b', 'from_currency': '2c710d9f-e811-48f6-aaed-85c74cbe8b95', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'from_amount': 10000000, 'delivery_method': ''}
2025-08-18 05:34:38,636 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('71593a1c-bfa9-48b5-8203-c9bd5552e0c7'), 'transaction_type': UUID('74755ddc-b1c8-4a46-bc74-38337a16ec7b'), 'from_currency': UUID('2c710d9f-e811-48f6-aaed-85c74cbe8b95'), 'to_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'from_amount': Decimal('10000000.000000'), 'delivery_method': ''}
2025-08-18 05:34:38,638 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 05:34:38,643 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 05:34:38,644 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('2c710d9f-e811-48f6-aaed-85c74cbe8b95'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-18 05:34:53,135 - apps.transactions.views - INFO - Commission calculation request from admin (admin): {'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'transaction_type': '74755ddc-b1c8-4a46-bc74-38337a16ec7b', 'from_currency': '2c710d9f-e811-48f6-aaed-85c74cbe8b95', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'from_amount': 10000000, 'delivery_method': 'internal'}
2025-08-18 05:34:53,136 - apps.transactions.views - INFO - Commission calculation data validated: {'location': UUID('71593a1c-bfa9-48b5-8203-c9bd5552e0c7'), 'transaction_type': UUID('74755ddc-b1c8-4a46-bc74-38337a16ec7b'), 'from_currency': UUID('2c710d9f-e811-48f6-aaed-85c74cbe8b95'), 'to_currency': UUID('5bcd3511-8b29-426e-942f-b9cc51bb2b2b'), 'from_amount': Decimal('10000000.000000'), 'delivery_method': 'internal'}
2025-08-18 05:34:53,137 - apps.transactions.views - INFO - Total active commission rules in system: 24
2025-08-18 05:34:53,140 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-18 05:34:53,140 - apps.transactions.views - INFO - Commission calculation result: {'amount': Decimal('0'), 'currency': UUID('2c710d9f-e811-48f6-aaed-85c74cbe8b95'), 'rule': None, 'breakdown': {'message': 'No applicable commission rules found'}}
2025-08-19 23:23:50,422 - apps.transactions.views - INFO - Transaction creation request from admin (admin): {'csrfmiddlewaretoken': 'PjQ32rVOlyb44T3DOAS6DWWaa3EmECJ2W5aOKRboUveZ16qfMookQDXMMdyCVNc3', 'transaction_type_code': 'ADJUSTMENT', 'customer': '4add5c82-e6c1-4cf6-9e49-9708910ecedc', 'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'reference_number': '', 'description': '', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'adjustment_type': 'increase', 'from_amount': '100', 'current_balance': '', 'new_balance': '', 'adjustment_reason': 'bonus', 'adjustment_details': 'l,l,l;', 'related_transaction': '', 'requested_by': 'Current User', 'confirm_adjustment': 'on', 'customer_notified': 'on', 'document_files': {}, 'document_type': 'other', 'notes': '', 'transaction_type': '43e77b46-65eb-4300-b9f9-a24a08bc170e', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'status': 'pending', 'exchange_rate': 1, 'delivery_method': 'internal'}
2025-08-19 23:23:50,422 - apps.transactions.views - WARNING - Missing required fields: ['to_amount']
2025-08-20 01:30:22,542 - apps.transactions.views - INFO - Transaction creation request from admin (admin): {'csrfmiddlewaretoken': 'ugg3LtsWUnZTf0TMt7J4fLTEiUAEKfnjB2AOtTIwtk2OcdgorVfissUgU4uU1qQk', 'transaction_type_code': 'DEPOSIT', 'customer': '0f694494-b706-4a96-924f-317cef18ccd9', 'location': 'e66335d2-bd8f-4e70-80e8-9c4c3fb543cf', 'reference_number': '', 'description': '', 'from_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'from_amount': '10', 'commission_amount': '', 'deposit_source': 'cash', 'delivery_method': 'cash', 'courier': '', 'bank_reference': '', 'check_number': '', 'received_by': 'Current User', 'receipt_number': 'RCP-*************', 'cash_verified': 'on', 'customer_id_verified': 'on', 'document_files': {}, 'document_type': 'other', 'notes': '', 'transaction_type': '74755ddc-b1c8-4a46-bc74-38337a16ec7b', 'status': 'pending', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'to_amount': '10', 'exchange_rate': 1}
2025-08-20 01:30:22,552 - apps.transactions.views - ERROR - Transaction creation failed: {'commission_amount': [ErrorDetail(string='A valid number is required.', code='invalid')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\views.py", line 177, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'commission_amount': [ErrorDetail(string='A valid number is required.', code='invalid')]}
2025-08-20 01:33:18,062 - apps.transactions.views - INFO - Transaction creation request from admin (admin): {'csrfmiddlewaretoken': 'mJ0VcBmsYM023gSB0oYGpI8RcLBhzuSxtvkGU1C2xJ3X0tfdYcuUCp9tOVvxQFly', 'transaction_type_code': 'REMITTANCE', 'customer': '4add5c82-e6c1-4cf6-9e49-9708910ecedc', 'location': 'e66335d2-bd8f-4e70-80e8-9c4c3fb543cf', 'reference_number': '', 'beneficiary_name': 'dkkk', 'description': '', 'from_currency': '2c710d9f-e811-48f6-aaed-85c74cbe8b95', 'from_amount': '5000', 'exchange_rate': '', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'to_amount': '', 'commission_amount': '', 'beneficiary_phone': '', 'beneficiary_id': '', 'beneficiary_address': 'asdasd', 'destination_country': 'CN', 'destination_city': 'sadasd', 'delivery_method': 'mobile_wallet', 'pickup_location': '', 'bank_details': '', 'mobile_wallet': 'asdasdasd', 'remittance_purpose': 'medical', 'purpose_description': '', 'document_files': {}, 'document_type': 'other', 'notes': '', 'transaction_type': 'fb78b230-1cc9-41f5-b14d-40e939bc512b', 'status': 'pending'}
2025-08-20 01:33:18,063 - apps.transactions.views - WARNING - Missing required fields: ['to_amount']
2025-08-20 01:34:57,662 - apps.transactions.views - INFO - Transaction creation request from admin (admin): {'csrfmiddlewaretoken': 'CL9VK7Wrr67ihfYK3bDHcRm1X0DQoL7hJxtGsxc103adeslm1Z9VpynDzax6FWAi', 'transaction_type_code': 'ADJUSTMENT', 'customer': 'de103197-e43e-4a68-b648-79845a85ee7a', 'location': '71593a1c-bfa9-48b5-8203-c9bd5552e0c7', 'reference_number': '', 'description': '', 'from_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'adjustment_type': 'increase', 'from_amount': '10000', 'current_balance': '', 'new_balance': '', 'adjustment_reason': 'refund', 'adjustment_details': 'dasdasd', 'related_transaction': 'asdsad', 'requested_by': 'Current User', 'confirm_adjustment': 'on', 'document_files': {}, 'document_type': 'other', 'notes': '', 'transaction_type': '43e77b46-65eb-4300-b9f9-a24a08bc170e', 'status': 'pending', 'to_currency': '5bcd3511-8b29-426e-942f-b9cc51bb2b2b', 'exchange_rate': 1, 'delivery_method': 'internal'}
2025-08-20 01:34:57,731 - apps.transactions.views - WARNING - Missing required fields: ['to_amount']
2025-08-20 01:54:44,519 - apps.transactions.views - INFO - Transaction creation request from admin (admin): {'csrfmiddlewaretoken': 'V33r5N8zrsTLKK7WPivCNRd5JNCXDCLD2PncNdo90pWGHXuyN61Q0yeHlXwdUNeE', 'transaction_type_code': 'DEPOSIT', 'customer': 'de103197-e43e-4a68-b648-79845a85ee7a', 'location': 'e66335d2-bd8f-4e70-80e8-9c4c3fb543cf', 'reference_number': '', 'description': '', 'from_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'from_amount': '999.999998', 'commission_amount': '', 'deposit_source': 'cash', 'delivery_method': 'cash', 'courier': '', 'bank_reference': '', 'check_number': '', 'received_by': 'Current User', 'receipt_number': 'RCP-*************', 'cash_verified': 'on', 'document_files': {}, 'document_type': 'other', 'notes': '', 'transaction_type': '74755ddc-b1c8-4a46-bc74-38337a16ec7b', 'status': 'pending', 'to_currency': 'e01a9fbb-4b91-4a55-8dcb-60665974b17d', 'to_amount': '999.999998', 'exchange_rate': 1}
2025-08-20 01:54:44,528 - apps.transactions.views - ERROR - Transaction creation failed: {'commission_amount': [ErrorDetail(string='A valid number is required.', code='invalid')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\views.py", line 177, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'commission_amount': [ErrorDetail(string='A valid number is required.', code='invalid')]}
2025-08-20 02:08:01,884 - apps.transactions.views - INFO - Transaction creation request from admin (admin): {'transaction_type': '74755ddc-b1c8-4a46-bc74-38337a16ec7b', 'customer': '29e87d20-2207-452e-9224-c62ea23b4eb9', 'from_currency': 'USD', 'to_currency': 'USD', 'from_amount': 100.0, 'to_amount': 100.0, 'commission_amount': 2.5, 'exchange_rate': 1.0, 'delivery_method': 'cash', 'status': 'draft'}
2025-08-20 02:37:21,822 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-20 02:38:36,528 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-20 04:29:56,832 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0001
2025-08-20 04:29:56,833 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0001
2025-08-20 04:29:56,834 - apps.transactions.signals - INFO - New transaction created: TXN-********-0001
2025-08-20 04:29:56,835 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:29:56,850 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:29:56,854 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:29:56,856 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:29:56,858 - apps.transactions.models - INFO - Balance entries created for transaction TXN-********-0001
2025-08-20 04:29:56,858 - apps.transactions.models - INFO - Transaction saved: TXN-********-0001 - completed
2025-08-20 04:29:56,859 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:29:56,864 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0002
2025-08-20 04:29:56,865 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0002
2025-08-20 04:29:56,866 - apps.transactions.signals - INFO - New transaction created: TXN-********-0002
2025-08-20 04:29:56,867 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:29:56,877 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:29:56,886 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:29:56,889 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:29:56,890 - apps.transactions.models - INFO - Balance entries created for transaction TXN-********-0002
2025-08-20 04:29:56,891 - apps.transactions.models - INFO - Transaction saved: TXN-********-0002 - completed
2025-08-20 04:29:56,892 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:29:56,918 - apps.transactions.views - INFO - Transaction creation request from Test User (testuser): <QueryDict: {'transaction_type': ['bd5ea514-b794-4b01-b59e-b10cda496307'], 'customer': ['cdd659bb-ddd8-4e3c-820f-2640d76ba4fa'], 'location': ['524739e6-6973-4f2e-bb31-e8351dbbfab2'], 'from_currency': ['4b66fec1-439e-4217-bebd-e79aebb574ab'], 'to_currency': ['4b66fec1-439e-4217-bebd-e79aebb574ab'], 'from_amount': ['1000.00'], 'to_amount': ['1000.00'], 'exchange_rate': ['1.0'], 'description': ['Balance adjustment for testing'], 'status': ['pending'], 'delivery_method': ['internal']}>
2025-08-20 04:29:56,926 - apps.transactions.views - INFO - Transaction creation attempt by Test User (testuser): <QueryDict: {'transaction_type': ['bd5ea514-b794-4b01-b59e-b10cda496307'], 'customer': ['cdd659bb-ddd8-4e3c-820f-2640d76ba4fa'], 'location': ['524739e6-6973-4f2e-bb31-e8351dbbfab2'], 'from_currency': ['4b66fec1-439e-4217-bebd-e79aebb574ab'], 'to_currency': ['4b66fec1-439e-4217-bebd-e79aebb574ab'], 'from_amount': ['1000.00'], 'to_amount': ['1000.00'], 'exchange_rate': ['1.0'], 'description': ['Balance adjustment for testing'], 'status': ['pending'], 'delivery_method': ['internal']}>
2025-08-20 04:29:56,927 - apps.transactions.views - WARNING - Permission denied for transaction creation: Test User (testuser)
2025-08-20 04:29:56,927 - apps.transactions.views - ERROR - Transaction creation failed: You don't have permission to create transactions.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\views.py", line 177, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\views.py", line 141, in perform_create
    raise PermissionDenied("You don't have permission to create transactions.")
rest_framework.exceptions.PermissionDenied: You don't have permission to create transactions.
2025-08-20 04:30:31,230 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0001
2025-08-20 04:30:31,231 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0001
2025-08-20 04:30:31,232 - apps.transactions.signals - INFO - New transaction created: TXN-********-0001
2025-08-20 04:30:31,233 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:30:31,235 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:30:31,238 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:30:31,240 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:30:31,258 - apps.transactions.models - INFO - Balance entries created for transaction TXN-********-0001
2025-08-20 04:30:31,259 - apps.transactions.models - INFO - Transaction saved: TXN-********-0001 - completed
2025-08-20 04:30:31,261 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:30:31,269 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0002
2025-08-20 04:30:31,293 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0002
2025-08-20 04:30:31,295 - apps.transactions.signals - INFO - New transaction created: TXN-********-0002
2025-08-20 04:30:31,297 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:30:31,300 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:30:31,303 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:30:31,317 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:30:31,322 - apps.transactions.models - INFO - Balance entries created for transaction TXN-********-0002
2025-08-20 04:30:31,323 - apps.transactions.models - INFO - Transaction saved: TXN-********-0002 - completed
2025-08-20 04:30:31,324 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:30:31,334 - apps.transactions.views - INFO - Transaction creation request from Test User (testuser): <QueryDict: {'transaction_type': ['2ed03a87-6969-423b-acf7-b187c3a4a1f3'], 'customer': ['044bf0a3-c550-406e-bf96-7215689e458a'], 'location': ['efe565f2-c2ce-4275-908f-69ae9ff573f4'], 'from_currency': ['e7ded671-b61c-4e73-a4b7-aeac8bae44c0'], 'to_currency': ['e7ded671-b61c-4e73-a4b7-aeac8bae44c0'], 'from_amount': ['1000.00'], 'to_amount': ['1000.00'], 'exchange_rate': ['1.0'], 'description': ['Balance adjustment for testing'], 'status': ['pending'], 'delivery_method': ['internal']}>
2025-08-20 04:30:31,356 - apps.transactions.views - INFO - Transaction creation attempt by Test User (testuser): <QueryDict: {'transaction_type': ['2ed03a87-6969-423b-acf7-b187c3a4a1f3'], 'customer': ['044bf0a3-c550-406e-bf96-7215689e458a'], 'location': ['efe565f2-c2ce-4275-908f-69ae9ff573f4'], 'from_currency': ['e7ded671-b61c-4e73-a4b7-aeac8bae44c0'], 'to_currency': ['e7ded671-b61c-4e73-a4b7-aeac8bae44c0'], 'from_amount': ['1000.00'], 'to_amount': ['1000.00'], 'exchange_rate': ['1.0'], 'description': ['Balance adjustment for testing'], 'status': ['pending'], 'delivery_method': ['internal']}>
2025-08-20 04:30:31,361 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-20 04:30:31,365 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0003
2025-08-20 04:30:31,365 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0003
2025-08-20 04:30:31,366 - apps.transactions.signals - INFO - New transaction created: TXN-********-0003
2025-08-20 04:30:31,366 - apps.transactions.models - INFO - Transaction saved: TXN-********-0003 - draft
2025-08-20 04:30:31,382 - apps.transactions.serializers - INFO - Transaction created: TXN-********-0003 by Test User (testuser)
2025-08-20 04:30:31,383 - apps.transactions.views - INFO - Transaction created successfully: TXN-********-0003
2025-08-20 04:31:27,541 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0001
2025-08-20 04:31:27,542 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0001
2025-08-20 04:31:27,543 - apps.transactions.signals - INFO - New transaction created: TXN-********-0001
2025-08-20 04:31:27,544 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:27,548 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:27,550 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:31:27,552 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:31:27,555 - apps.transactions.models - INFO - Balance entries created for transaction TXN-********-0001
2025-08-20 04:31:27,555 - apps.transactions.models - INFO - Transaction saved: TXN-********-0001 - completed
2025-08-20 04:31:27,555 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:27,572 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0002
2025-08-20 04:31:27,572 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0002
2025-08-20 04:31:27,573 - apps.transactions.signals - INFO - New transaction created: TXN-********-0002
2025-08-20 04:31:27,574 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:27,576 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:27,579 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:31:27,581 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:31:27,582 - apps.transactions.models - INFO - Balance entries created for transaction TXN-********-0002
2025-08-20 04:31:27,583 - apps.transactions.models - INFO - Transaction saved: TXN-********-0002 - completed
2025-08-20 04:31:27,584 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:27,614 - apps.transactions.views - INFO - Transaction creation request from Test User (testuser): <QueryDict: {'transaction_type': ['c20ee6cb-8daf-4402-8349-0d53409a21e2'], 'customer': ['b989baca-d7c6-45e9-b59c-b90305b05a2d'], 'location': ['e1c10d1d-c5fc-4df3-bc15-f7c43cf555fa'], 'from_currency': ['27bffdb0-be1d-4aee-b416-c03a1545e5cc'], 'to_currency': ['27bffdb0-be1d-4aee-b416-c03a1545e5cc'], 'from_amount': ['1000.00'], 'to_amount': ['1000.00'], 'exchange_rate': ['1.0'], 'description': ['Balance adjustment for testing'], 'status': ['pending'], 'delivery_method': ['internal']}>
2025-08-20 04:31:27,647 - apps.transactions.views - INFO - Transaction creation attempt by Test User (testuser): <QueryDict: {'transaction_type': ['c20ee6cb-8daf-4402-8349-0d53409a21e2'], 'customer': ['b989baca-d7c6-45e9-b59c-b90305b05a2d'], 'location': ['e1c10d1d-c5fc-4df3-bc15-f7c43cf555fa'], 'from_currency': ['27bffdb0-be1d-4aee-b416-c03a1545e5cc'], 'to_currency': ['27bffdb0-be1d-4aee-b416-c03a1545e5cc'], 'from_amount': ['1000.00'], 'to_amount': ['1000.00'], 'exchange_rate': ['1.0'], 'description': ['Balance adjustment for testing'], 'status': ['pending'], 'delivery_method': ['internal']}>
2025-08-20 04:31:27,663 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-20 04:31:27,671 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0003
2025-08-20 04:31:27,671 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0003
2025-08-20 04:31:27,672 - apps.transactions.signals - INFO - New transaction created: TXN-********-0003
2025-08-20 04:31:27,673 - apps.transactions.models - INFO - Transaction saved: TXN-********-0003 - pending
2025-08-20 04:31:27,674 - apps.transactions.serializers - INFO - Transaction created: TXN-********-0003 by Test User (testuser)
2025-08-20 04:31:27,675 - apps.transactions.views - INFO - Transaction created successfully: TXN-********-0003
2025-08-20 04:31:57,400 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0001
2025-08-20 04:31:57,405 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0001
2025-08-20 04:31:57,416 - apps.transactions.signals - INFO - New transaction created: TXN-********-0001
2025-08-20 04:31:57,418 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:57,421 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:57,424 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:31:57,426 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:31:57,429 - apps.transactions.models - INFO - Balance entries created for transaction TXN-********-0001
2025-08-20 04:31:57,429 - apps.transactions.models - INFO - Transaction saved: TXN-********-0001 - completed
2025-08-20 04:31:57,430 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:57,450 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0002
2025-08-20 04:31:57,451 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0002
2025-08-20 04:31:57,452 - apps.transactions.signals - INFO - New transaction created: TXN-********-0002
2025-08-20 04:31:57,453 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:57,455 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:57,459 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:31:57,462 - apps.transactions.signals - INFO - New balance entry created for customer Company
2025-08-20 04:31:57,478 - apps.transactions.models - INFO - Balance entries created for transaction TXN-********-0002
2025-08-20 04:31:57,478 - apps.transactions.models - INFO - Transaction saved: TXN-********-0002 - completed
2025-08-20 04:31:57,480 - apps.transactions.signals - INFO - New balance entry created for customer CUST000002
2025-08-20 04:31:57,492 - apps.transactions.views - INFO - Transaction creation request from Test User (testuser): <QueryDict: {'transaction_type': ['bb8287e5-f30f-48d8-9c99-43add725b00f'], 'customer': ['e40bf762-4271-4a97-a0cc-a65db680e454'], 'location': ['d0a76125-f00c-4a91-accb-11fd004f28f6'], 'from_currency': ['1db0fc9d-0b52-4d51-ae67-c4aac4638929'], 'to_currency': ['1db0fc9d-0b52-4d51-ae67-c4aac4638929'], 'from_amount': ['1000.00'], 'to_amount': ['1000.00'], 'exchange_rate': ['1.0'], 'description': ['Balance adjustment for testing'], 'status': ['pending'], 'delivery_method': ['internal']}>
2025-08-20 04:31:57,513 - apps.transactions.views - INFO - Transaction creation attempt by Test User (testuser): <QueryDict: {'transaction_type': ['bb8287e5-f30f-48d8-9c99-43add725b00f'], 'customer': ['e40bf762-4271-4a97-a0cc-a65db680e454'], 'location': ['d0a76125-f00c-4a91-accb-11fd004f28f6'], 'from_currency': ['1db0fc9d-0b52-4d51-ae67-c4aac4638929'], 'to_currency': ['1db0fc9d-0b52-4d51-ae67-c4aac4638929'], 'from_amount': ['1000.00'], 'to_amount': ['1000.00'], 'exchange_rate': ['1.0'], 'description': ['Balance adjustment for testing'], 'status': ['pending'], 'delivery_method': ['internal']}>
2025-08-20 04:31:57,519 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction PREVIEW
2025-08-20 04:31:57,523 - apps.transactions.commission_utils.CommissionCalculator - INFO - No commission rules found for transaction TXN-********-0003
2025-08-20 04:31:57,523 - apps.transactions.models - INFO - No commission calculated for transaction TXN-********-0003
2025-08-20 04:31:57,524 - apps.transactions.signals - INFO - New transaction created: TXN-********-0003
2025-08-20 04:31:57,525 - apps.transactions.models - INFO - Transaction saved: TXN-********-0003 - pending
2025-08-20 04:31:57,528 - apps.transactions.serializers - INFO - Transaction created: TXN-********-0003 by Test User (testuser)
2025-08-20 04:31:57,537 - apps.transactions.views - INFO - Transaction created successfully: TXN-********-0003
