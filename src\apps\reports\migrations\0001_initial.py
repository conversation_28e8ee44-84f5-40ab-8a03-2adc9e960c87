# Generated by Django 4.2.23 on 2025-08-13 08:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('name', models.CharField(help_text='Name of the report template', max_length=200, verbose_name='Template name')),
                ('report_type', models.CharField(choices=[('customer_statement', 'Customer Statement'), ('balance_summary', 'Balance Summary'), ('transaction_report', 'Transaction Report'), ('profit_loss', 'Profit & Loss'), ('daily_summary', 'Daily Summary'), ('exchange_rate_report', 'Exchange Rate Report'), ('audit_report', 'Audit Report'), ('custom', 'Custom Report')], help_text='Type of report this template generates', max_length=30, verbose_name='Report type')),
                ('description', models.TextField(blank=True, help_text='Description of what this report template does', verbose_name='Description')),
                ('template_config', models.JSONField(default=dict, help_text='JSON configuration for the report template', verbose_name='Template configuration')),
                ('sql_query', models.TextField(blank=True, help_text='SQL query to extract data for the report', verbose_name='SQL query')),
                ('layout_config', models.JSONField(default=dict, help_text='JSON configuration for report layout and formatting', verbose_name='Layout configuration')),
                ('is_public', models.BooleanField(default=False, help_text='Whether this template is available to all users', verbose_name='Is public')),
                ('allowed_roles', models.JSONField(default=list, help_text='List of user roles allowed to use this template', verbose_name='Allowed roles')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this template is currently active', verbose_name='Is active')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Order in which templates should be displayed', verbose_name='Sort order')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Report Template',
                'verbose_name_plural': 'Report Templates',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ReportSchedule',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('name', models.CharField(help_text='Name of the report schedule', max_length=200, verbose_name='Schedule name')),
                ('description', models.TextField(blank=True, help_text='Description of the scheduled report', verbose_name='Description')),
                ('frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('yearly', 'Yearly')], help_text='How often to generate the report', max_length=20, verbose_name='Frequency')),
                ('parameters', models.JSONField(default=dict, help_text='Parameters to use for scheduled report generation', verbose_name='Report parameters')),
                ('format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('json', 'JSON'), ('html', 'HTML')], default='pdf', help_text='Format for scheduled reports', max_length=10, verbose_name='Format')),
                ('email_recipients', models.JSONField(default=list, help_text='List of email addresses to send the report to', verbose_name='Email recipients')),
                ('next_run_at', models.DateTimeField(help_text='When to generate the next report', verbose_name='Next run at')),
                ('last_run_at', models.DateTimeField(blank=True, help_text='When the report was last generated', null=True, verbose_name='Last run at')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this schedule is currently active', verbose_name='Is active')),
                ('run_count', models.PositiveIntegerField(default=0, help_text='Number of times this schedule has run', verbose_name='Run count')),
                ('success_count', models.PositiveIntegerField(default=0, help_text='Number of successful report generations', verbose_name='Success count')),
                ('failure_count', models.PositiveIntegerField(default=0, help_text='Number of failed report generations', verbose_name='Failure count')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('template', models.ForeignKey(help_text='Template to use for scheduled reports', on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='reports.reporttemplate', verbose_name='Report template')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Report Schedule',
                'verbose_name_plural': 'Report Schedules',
                'ordering': ['next_run_at'],
            },
        ),
        migrations.CreateModel(
            name='GeneratedReport',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('title', models.CharField(help_text='Title of the generated report', max_length=200, verbose_name='Report title')),
                ('parameters', models.JSONField(default=dict, help_text='Parameters used to generate this report', verbose_name='Report parameters')),
                ('status', models.CharField(choices=[('generating', 'Generating'), ('completed', 'Completed'), ('failed', 'Failed'), ('expired', 'Expired')], default='generating', help_text='Current status of the report generation', max_length=20, verbose_name='Status')),
                ('format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('json', 'JSON'), ('html', 'HTML')], default='pdf', help_text='Format of the generated report', max_length=10, verbose_name='Format')),
                ('file_path', models.CharField(blank=True, help_text='Path to the generated report file', max_length=500, verbose_name='File path')),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='Size of the generated report file in bytes', null=True, verbose_name='File size')),
                ('generation_started_at', models.DateTimeField(auto_now_add=True, help_text='When report generation started', verbose_name='Generation started at')),
                ('generation_completed_at', models.DateTimeField(blank=True, help_text='When report generation completed', null=True, verbose_name='Generation completed at')),
                ('expires_at', models.DateTimeField(blank=True, help_text='When this report expires and should be deleted', null=True, verbose_name='Expires at')),
                ('error_message', models.TextField(blank=True, help_text='Error message if generation failed', verbose_name='Error message')),
                ('record_count', models.PositiveIntegerField(blank=True, help_text='Number of records in the report', null=True, verbose_name='Record count')),
                ('download_count', models.PositiveIntegerField(default=0, help_text='Number of times this report has been downloaded', verbose_name='Download count')),
                ('last_downloaded_at', models.DateTimeField(blank=True, help_text='When this report was last downloaded', null=True, verbose_name='Last downloaded at')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('generated_by', models.ForeignKey(help_text='User who generated this report', on_delete=django.db.models.deletion.PROTECT, related_name='generated_reports', to=settings.AUTH_USER_MODEL, verbose_name='Generated by')),
                ('template', models.ForeignKey(help_text='Template used to generate this report', on_delete=django.db.models.deletion.PROTECT, related_name='generated_reports', to='reports.reporttemplate', verbose_name='Report template')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Generated Report',
                'verbose_name_plural': 'Generated Reports',
                'ordering': ['-generation_started_at'],
            },
        ),
        migrations.AddIndex(
            model_name='reporttemplate',
            index=models.Index(fields=['report_type'], name='reports_rep_report__f68e8c_idx'),
        ),
        migrations.AddIndex(
            model_name='reporttemplate',
            index=models.Index(fields=['is_active'], name='reports_rep_is_acti_4b0ce5_idx'),
        ),
        migrations.AddIndex(
            model_name='reporttemplate',
            index=models.Index(fields=['is_public'], name='reports_rep_is_publ_0e1438_idx'),
        ),
        migrations.AddIndex(
            model_name='reportschedule',
            index=models.Index(fields=['is_active', 'next_run_at'], name='reports_rep_is_acti_c0e6a8_idx'),
        ),
        migrations.AddIndex(
            model_name='reportschedule',
            index=models.Index(fields=['template'], name='reports_rep_templat_684edd_idx'),
        ),
        migrations.AddIndex(
            model_name='reportschedule',
            index=models.Index(fields=['frequency'], name='reports_rep_frequen_1e97ea_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedreport',
            index=models.Index(fields=['generated_by', 'status'], name='reports_gen_generat_87c071_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedreport',
            index=models.Index(fields=['template', 'status'], name='reports_gen_templat_272848_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedreport',
            index=models.Index(fields=['status'], name='reports_gen_status_b3120a_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedreport',
            index=models.Index(fields=['generation_started_at'], name='reports_gen_generat_a536d0_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedreport',
            index=models.Index(fields=['expires_at'], name='reports_gen_expires_a93554_idx'),
        ),
    ]
