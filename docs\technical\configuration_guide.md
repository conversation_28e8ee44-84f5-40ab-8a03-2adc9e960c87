# ⚙️ راهنمای پیکربندی سیستم Arena Doviz

این سند تنظیمات موردنیاز برای اجرا و تولید را توضیح می‌دهد.

## متغیرهای محیطی
- `SECRET_KEY`: کلید سری <PERSON> (ضروری)
- `DATABASE_URL`: اتصال PostgreSQL مانند: `******************************/exchange_db`
- `REDIS_URL`: آدرس Redis (اختیاری)
- `ALLOWED_HOSTS`: دامنه‌ها با کاما جدا
- `DEBUG`: false/true (در تولید false)
- `LANGUAGES`: مانند `fa,en`
- `DEFAULT_LANGUAGE`: `fa`
- `TIME_ZONE`: پیش‌فرض `Asia/Tehran`
- `WHATSAPP_INTEGRATION`: `desktop` (بازکردن WhatsApp Desktop)

## تنظیمات امنیتی
- فعال‌سازی HTTPS و HSTS
- تنظیم `SECURE_PROXY_SSL_HEADER` پشت Nginx
- محدودسازی اندازه آپلود (رسیدها/اسناد)
- رمزنگاری AES-256 برای داده‌های حساس (کلیدها در Secret Store)

## مدیریت کلید رمزنگاری (ARENA_ENCRYPTION_KEY)

### تولید کلید رمزنگاری
برای تولید کلید رمزنگاری جدید از دستور زیر استفاده کنید:
```bash
python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
```

### ذخیره‌سازی کلید در محیط تولید

#### روش 1: متغیر محیطی سیستم (توصیه شده)
```bash
# Windows
setx ARENA_ENCRYPTION_KEY "x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo="

# Linux/macOS
export ARENA_ENCRYPTION_KEY="x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo="
echo 'export ARENA_ENCRYPTION_KEY="x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo="' >> ~/.bashrc
```

#### روش 2: فایل .env (برای محیط توسعه)
```bash
# در فایل .env در ریشه پروژه
ARENA_ENCRYPTION_KEY=x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo=
```

#### روش 3: سرویس مدیریت رمز (برای تولید)
- Azure Key Vault
- AWS Secrets Manager
- HashiCorp Vault
- Google Secret Manager

### نکات مهم امنیتی
1. **هرگز کلید را در کد یا فایل‌های عمومی قرار ندهید**
2. **همان کلید را در تمام نودهای تولید استفاده کنید**
3. **کلید را به صورت منظم تغییر دهید (هر 6-12 ماه)**
4. **از کلید backup برای بازیابی داده‌های قدیمی نگهداری کنید**
5. **دسترسی به کلید را محدود کنید**

### تست کلید رمزنگاری
```bash
# تست کلید در محیط تولید
python manage.py shell --settings=config.settings.prod
>>> from apps.core.encryption import encrypt_data, decrypt_data
>>> test_data = "test encryption"
>>> encrypted = encrypt_data(test_data)
>>> decrypted = decrypt_data(encrypted)
>>> print(f"Original: {test_data}, Decrypted: {decrypted}")
```

## تنظیمات ماژول‌ها
### احراز هویت و مجوزها
- فعال‌سازی JWT برای API
- تعریف نقش‌ها: Admin, Accountant/Branch, Viewer
- مجوزها بر اساس نقش و مکان

### مشتریان
- فعال‌سازی ایجاد گروه واتساپ پس از ثبت (اختیاری)
- الگوی پیام اعلان تراکنش‌ها

### ارز و نرخ‌ها
- تعریف ارزهای اولیه: USD, AED, IRR
- تعریف مکان‌ها: Istanbul, Tabriz, Tehran, Dubai, China
- نرخ‌ها به تفکیک مکان؛ آخرین نرخ برای نمایش داشبورد

### معاملات
- پیکربندی کمیسیون: درصدی یا مبلغ ثابت
- فعال‌سازی اقساط چندمرحله‌ای
- تنظیمات الزامی بودن فیلدهای شماره پیگیری بانکی/داخلی
- مسیر ذخیره‌سازی فایل پیوست رسیدها

### گزارش‌گیری
- قالب‌های خروجی PDF/Excel
- نقشه‌نگاشت کدها: DBN, JV, TSN, TRQ, CBS

## فایل‌های پیکربندی نمونه
- `deployment/nginx.conf` (نمونه بلاک سرور)
- `deployment/docker-compose.yml` (اگر موجود)

## چک‌لیست قبل از تولید
- [ ] DEBUG=false
- [ ] SECRET_KEY امن
- [ ] ALLOWED_HOSTS تنظیم
- [ ] اتصال DB/Redis پایدار
- [ ] HTTPS فعال
- [ ] پشتیبان‌گیری زمان‌بندی‌شده

