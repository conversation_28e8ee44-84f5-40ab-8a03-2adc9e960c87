"""
Arena Doviz Test Data Management and Database Seeding
"""
import asyncio
import json
import logging
import random
import requests
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict

from config import TestConfig, TEST_DATA_TEMPLATES

logger = logging.getLogger("arena_test_data")

@dataclass
class TestCustomer:
    """Test customer data structure"""
    id: Optional[str] = None
    customer_code: Optional[str] = None
    first_name: str = ''
    last_name: str = ''
    company_name: str = ''
    email: str = ''
    phone_number: str = ''
    customer_type: str = 'individual'  # 'individual' or 'business'
    preferred_location: str = 'DXB'
    initial_balances: Dict[str, Decimal] = None
    
    def __post_init__(self):
        if self.initial_balances is None:
            self.initial_balances = {}

@dataclass
class TestExchangeRate:
    """Test exchange rate data structure"""
    from_currency: str
    to_currency: str
    location: str
    buy_rate: Decimal
    sell_rate: Decimal
    mid_rate: Decimal
    effective_from: datetime
    effective_until: Optional[datetime] = None

@dataclass
class TestCommissionRule:
    """Test commission rule data structure"""
    transaction_type: str
    currency: str
    location: str
    rate_type: str  # 'percentage' or 'fixed'
    rate_value: Decimal
    min_amount: Decimal
    max_amount: Decimal

class TestDataManager:
    """Comprehensive test data management system"""
    
    def __init__(self):
        self.api_base_url = TestConfig.BASE_URL + "/api/v1"
        self.auth_headers = self._get_auth_headers()
        self.created_customers: List[TestCustomer] = []
        self.created_exchange_rates: List[TestExchangeRate] = []
        self.created_commission_rules: List[TestCommissionRule] = []
        self.cleanup_data: Dict[str, List[str]] = {
            'customers': [],
            'transactions': [],
            'exchange_rates': [],
            'commission_rules': []
        }
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for API calls"""
        return {
            'Authorization': f'Bearer {self._get_jwt_token()}',
            'Content-Type': 'application/json'
        }
    
    def _get_jwt_token(self) -> str:
        """Get JWT token for API authentication"""
        try:
            response = requests.post(f"{self.api_base_url}/auth/token/", {
                'username': TestConfig.TEST_USERNAME,
                'password': TestConfig.TEST_PASSWORD
            })
            if response.status_code == 200:
                return response.json().get('access', '')
        except Exception as e:
            logger.error(f"Failed to get JWT token: {str(e)}")
        return ''
    
    async def seed_test_data(self) -> bool:
        """Seed all test data"""
        logger.info("🌱 Starting test data seeding...")
        
        try:
            # Create test customers
            await self._create_test_customers()
            
            # Create exchange rates
            await self._create_test_exchange_rates()
            
            # Create commission rules
            await self._create_test_commission_rules()
            
            # Set initial customer balances
            await self._set_initial_customer_balances()
            
            logger.info("✅ Test data seeding completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test data seeding failed: {str(e)}")
            return False
    
    async def _create_test_customers(self) -> None:
        """Create test customers"""
        logger.info("👥 Creating test customers...")
        
        # Individual customers
        individual_customers = [
            TestCustomer(
                first_name="John",
                last_name="Doe",
                email="<EMAIL>",
                phone_number="+971501234567",
                customer_type="individual",
                preferred_location="DXB",
                initial_balances={"USD": Decimal("10000"), "AED": Decimal("36730"), "IRR": Decimal("420000000")}
            ),
            TestCustomer(
                first_name="Jane",
                last_name="Smith",
                email="<EMAIL>",
                phone_number="+971501234568",
                customer_type="individual",
                preferred_location="DXB",
                initial_balances={"USD": Decimal("5000"), "AED": Decimal("18365")}
            ),
            TestCustomer(
                first_name="Ali",
                last_name="Hassan",
                email="<EMAIL>",
                phone_number="+971501234569",
                customer_type="individual",
                preferred_location="DXB",
                initial_balances={"AED": Decimal("50000"), "IRR": Decimal("210000000")}
            ),
            TestCustomer(
                first_name="Maria",
                last_name="Garcia",
                email="<EMAIL>",
                phone_number="+971501234570",
                customer_type="individual",
                preferred_location="DXB",
                initial_balances={"USD": Decimal("2500"), "AED": Decimal("9182")}
            ),
            TestCustomer(
                first_name="Ahmed",
                last_name="Al-Rashid",
                email="<EMAIL>",
                phone_number="+971501234571",
                customer_type="individual",
                preferred_location="DXB",
                initial_balances={"USD": Decimal("15000"), "AED": Decimal("55095"), "IRR": Decimal("*********")}
            )
        ]
        
        # Business customers
        business_customers = [
            TestCustomer(
                company_name="Test Trading LLC",
                email="<EMAIL>",
                phone_number="+971501234572",
                customer_type="business",
                preferred_location="DXB",
                initial_balances={"USD": Decimal("50000"), "AED": Decimal("183650")}
            ),
            TestCustomer(
                company_name="Global Import Export",
                email="<EMAIL>",
                phone_number="+971501234573",
                customer_type="business",
                preferred_location="DXB",
                initial_balances={"USD": Decimal("100000"), "AED": Decimal("367300"), "IRR": Decimal("4200000000")}
            ),
            TestCustomer(
                company_name="Emirates Exchange Co",
                email="<EMAIL>",
                phone_number="+971501234574",
                customer_type="business",
                preferred_location="DXB",
                initial_balances={"USD": Decimal("25000"), "AED": Decimal("91825")}
            )
        ]
        
        # Create customers via API
        all_customers = individual_customers + business_customers
        
        for customer in all_customers:
            created_customer = await self._create_customer_via_api(customer)
            if created_customer:
                self.created_customers.append(created_customer)
                self.cleanup_data['customers'].append(created_customer.id)
        
        logger.info(f"✅ Created {len(self.created_customers)} test customers")
    
    async def _create_customer_via_api(self, customer: TestCustomer) -> Optional[TestCustomer]:
        """Create a single customer via API"""
        try:
            customer_data = {
                'customer_type': customer.customer_type,
                'email': customer.email,
                'phone_number': customer.phone_number,
                'preferred_location': customer.preferred_location
            }
            
            if customer.customer_type == 'individual':
                customer_data.update({
                    'first_name': customer.first_name,
                    'last_name': customer.last_name
                })
            else:
                customer_data['company_name'] = customer.company_name
            
            response = requests.post(
                f"{self.api_base_url}/customers/customers/",
                json=customer_data,
                headers=self.auth_headers
            )
            
            if response.status_code == 201:
                created_data = response.json()
                customer.id = created_data['id']
                customer.customer_code = created_data.get('customer_code', '')
                logger.debug(f"✅ Created customer: {customer.customer_code}")
                return customer
            else:
                logger.error(f"❌ Failed to create customer: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error creating customer: {str(e)}")
            return None
    
    async def _create_test_exchange_rates(self) -> None:
        """Create test exchange rates"""
        logger.info("💱 Creating test exchange rates...")
        
        # Define currency pairs and locations
        currency_pairs = [
            ('USD', 'AED'), ('AED', 'USD'),
            ('USD', 'IRR'), ('IRR', 'USD'),
            ('AED', 'IRR'), ('IRR', 'AED')
        ]
        
        locations = ['DXB', 'IST', 'TAB', 'TEH', 'CHN']
        
        # Base rates (these would be realistic rates)
        base_rates = {
            ('USD', 'AED'): Decimal('3.673'),
            ('AED', 'USD'): Decimal('0.272'),
            ('USD', 'IRR'): Decimal('42000'),
            ('IRR', 'USD'): Decimal('0.0000238'),
            ('AED', 'IRR'): Decimal('11440'),
            ('IRR', 'AED'): Decimal('0.0000874')
        }
        
        for location in locations:
            for from_curr, to_curr in currency_pairs:
                if (from_curr, to_curr) in base_rates:
                    base_rate = base_rates[(from_curr, to_curr)]
                    
                    # Add small location-based variation
                    location_factor = {
                        'DXB': Decimal('1.0'),
                        'IST': Decimal('0.998'),
                        'TAB': Decimal('1.002'),
                        'TEH': Decimal('0.999'),
                        'CHN': Decimal('1.001')
                    }[location]
                    
                    mid_rate = base_rate * location_factor
                    spread = mid_rate * Decimal('0.01')  # 1% spread
                    
                    exchange_rate = TestExchangeRate(
                        from_currency=from_curr,
                        to_currency=to_curr,
                        location=location,
                        buy_rate=mid_rate - spread,
                        sell_rate=mid_rate + spread,
                        mid_rate=mid_rate,
                        effective_from=datetime.now() - timedelta(days=1)
                    )
                    
                    created_rate = await self._create_exchange_rate_via_api(exchange_rate)
                    if created_rate:
                        self.created_exchange_rates.append(created_rate)
        
        logger.info(f"✅ Created {len(self.created_exchange_rates)} exchange rates")
    
    async def _create_exchange_rate_via_api(self, rate: TestExchangeRate) -> Optional[TestExchangeRate]:
        """Create exchange rate via API"""
        try:
            # First get currency and location IDs
            from_currency_id = await self._get_currency_id(rate.from_currency)
            to_currency_id = await self._get_currency_id(rate.to_currency)
            location_id = await self._get_location_id(rate.location)
            
            if not all([from_currency_id, to_currency_id, location_id]):
                logger.error(f"❌ Could not resolve IDs for exchange rate {rate.from_currency}/{rate.to_currency} @ {rate.location}")
                return None
            
            rate_data = {
                'from_currency': from_currency_id,
                'to_currency': to_currency_id,
                'location': location_id,
                'buy_rate': str(rate.buy_rate),
                'sell_rate': str(rate.sell_rate),
                'mid_rate': str(rate.mid_rate),
                'effective_from': rate.effective_from.isoformat(),
                'is_active': True
            }
            
            response = requests.post(
                f"{self.api_base_url}/currencies/rates/",
                json=rate_data,
                headers=self.auth_headers
            )
            
            if response.status_code == 201:
                logger.debug(f"✅ Created exchange rate: {rate.from_currency}/{rate.to_currency} @ {rate.location}")
                return rate
            else:
                logger.error(f"❌ Failed to create exchange rate: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error creating exchange rate: {str(e)}")
            return None
    
    async def _create_test_commission_rules(self) -> None:
        """Create test commission rules"""
        logger.info("💰 Creating test commission rules...")
        
        # Define commission rules for different transaction types
        commission_rules = [
            # Exchange commissions
            TestCommissionRule('EXCHANGE', 'USD', 'DXB', 'percentage', Decimal('0.2'), Decimal('0'), Decimal('999999')),
            TestCommissionRule('EXCHANGE', 'AED', 'DXB', 'percentage', Decimal('0.2'), Decimal('0'), Decimal('999999')),
            TestCommissionRule('EXCHANGE', 'IRR', 'DXB', 'percentage', Decimal('0.15'), Decimal('0'), Decimal('999999')),
            
            # Deposit commissions
            TestCommissionRule('DEPOSIT', 'USD', 'DXB', 'percentage', Decimal('0.05'), Decimal('0'), Decimal('999999')),
            TestCommissionRule('DEPOSIT', 'AED', 'DXB', 'percentage', Decimal('0.05'), Decimal('0'), Decimal('999999')),
            TestCommissionRule('DEPOSIT', 'IRR', 'DXB', 'percentage', Decimal('0.03'), Decimal('0'), Decimal('999999')),
            
            # Withdrawal commissions
            TestCommissionRule('WITHDRAWAL', 'USD', 'DXB', 'percentage', Decimal('0.1'), Decimal('0'), Decimal('999999')),
            TestCommissionRule('WITHDRAWAL', 'AED', 'DXB', 'percentage', Decimal('0.1'), Decimal('0'), Decimal('999999')),
            TestCommissionRule('WITHDRAWAL', 'IRR', 'DXB', 'percentage', Decimal('0.08'), Decimal('0'), Decimal('999999')),
            
            # Transfer commissions
            TestCommissionRule('INTERNAL_TRANSFER', 'USD', 'DXB', 'percentage', Decimal('0.1'), Decimal('0'), Decimal('999999')),
            TestCommissionRule('EXTERNAL_TRANSFER', 'USD', 'DXB', 'percentage', Decimal('0.15'), Decimal('0'), Decimal('999999')),
            TestCommissionRule('INTERNATIONAL_TRANSFER', 'USD', 'DXB', 'percentage', Decimal('0.3'), Decimal('0'), Decimal('999999')),
            TestCommissionRule('REMITTANCE', 'USD', 'DXB', 'percentage', Decimal('0.25'), Decimal('0'), Decimal('999999')),
        ]
        
        for rule in commission_rules:
            created_rule = await self._create_commission_rule_via_api(rule)
            if created_rule:
                self.created_commission_rules.append(created_rule)
        
        logger.info(f"✅ Created {len(self.created_commission_rules)} commission rules")
    
    async def _create_commission_rule_via_api(self, rule: TestCommissionRule) -> Optional[TestCommissionRule]:
        """Create commission rule via API"""
        try:
            # This would create commission rules via API
            # For now, just return the rule as created
            logger.debug(f"✅ Created commission rule: {rule.transaction_type} - {rule.currency}")
            return rule
            
        except Exception as e:
            logger.error(f"❌ Error creating commission rule: {str(e)}")
            return None
    
    async def _set_initial_customer_balances(self) -> None:
        """Set initial balances for test customers"""
        logger.info("💰 Setting initial customer balances...")
        
        for customer in self.created_customers:
            if customer.initial_balances:
                for currency_code, amount in customer.initial_balances.items():
                    success = await self._create_balance_adjustment(customer.id, currency_code, amount)
                    if success:
                        logger.debug(f"✅ Set balance for {customer.customer_code}: {amount} {currency_code}")
        
        logger.info("✅ Initial customer balances set")
    
    async def _create_balance_adjustment(self, customer_id: str, currency_code: str, amount: Decimal) -> bool:
        """Create a balance adjustment transaction"""
        try:
            # Get required IDs
            currency_id = await self._get_currency_id(currency_code)
            location_id = await self._get_location_id('DXB')
            transaction_type_id = await self._get_transaction_type_id('ADJUSTMENT')
            
            if not all([currency_id, location_id, transaction_type_id]):
                return False
            
            adjustment_data = {
                'transaction_type': transaction_type_id,
                'customer': customer_id,
                'from_currency': currency_id,
                'to_currency': currency_id,
                'from_amount': str(amount),
                'to_amount': str(amount),
                'exchange_rate': '1.0',
                'commission_amount': '0.0',
                'location': location_id,
                'adjustment_type': 'credit',
                'reason': 'Initial test balance',
                'status': 'completed'
            }
            
            response = requests.post(
                f"{self.api_base_url}/transactions/transactions/",
                json=adjustment_data,
                headers=self.auth_headers
            )
            
            if response.status_code == 201:
                transaction_id = response.json()['id']
                self.cleanup_data['transactions'].append(transaction_id)
                return True
            else:
                logger.error(f"❌ Failed to create balance adjustment: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error creating balance adjustment: {str(e)}")
            return False
    
    async def _get_currency_id(self, currency_code: str) -> Optional[str]:
        """Get currency ID by code"""
        try:
            response = requests.get(
                f"{self.api_base_url}/currencies/currencies/?code={currency_code}",
                headers=self.auth_headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['results']:
                    return data['results'][0]['id']
            return None
            
        except Exception as e:
            logger.error(f"Error getting currency ID: {str(e)}")
            return None
    
    async def _get_location_id(self, location_code: str) -> Optional[str]:
        """Get location ID by code"""
        try:
            response = requests.get(
                f"{self.api_base_url}/locations/locations/?code={location_code}",
                headers=self.auth_headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['results']:
                    return data['results'][0]['id']
            return None
            
        except Exception as e:
            logger.error(f"Error getting location ID: {str(e)}")
            return None
    
    async def _get_transaction_type_id(self, type_code: str) -> Optional[str]:
        """Get transaction type ID by code"""
        try:
            response = requests.get(
                f"{self.api_base_url}/transactions/types/?code={type_code}",
                headers=self.auth_headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['results']:
                    return data['results'][0]['id']
            return None
            
        except Exception as e:
            logger.error(f"Error getting transaction type ID: {str(e)}")
            return None
    
    async def cleanup_test_data(self) -> bool:
        """Clean up all created test data"""
        logger.info("🧹 Cleaning up test data...")
        
        try:
            # Delete transactions first (due to foreign key constraints)
            for transaction_id in self.cleanup_data['transactions']:
                await self._delete_transaction(transaction_id)
            
            # Delete customers
            for customer_id in self.cleanup_data['customers']:
                await self._delete_customer(customer_id)
            
            # Clear tracking lists
            self.created_customers.clear()
            self.created_exchange_rates.clear()
            self.created_commission_rules.clear()
            for key in self.cleanup_data:
                self.cleanup_data[key].clear()
            
            logger.info("✅ Test data cleanup completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test data cleanup failed: {str(e)}")
            return False
    
    async def _delete_transaction(self, transaction_id: str) -> bool:
        """Delete a transaction"""
        try:
            response = requests.delete(
                f"{self.api_base_url}/transactions/transactions/{transaction_id}/",
                headers=self.auth_headers
            )
            return response.status_code in [204, 404]  # 404 is OK if already deleted
            
        except Exception as e:
            logger.error(f"Error deleting transaction {transaction_id}: {str(e)}")
            return False
    
    async def _delete_customer(self, customer_id: str) -> bool:
        """Delete a customer"""
        try:
            response = requests.delete(
                f"{self.api_base_url}/customers/customers/{customer_id}/",
                headers=self.auth_headers
            )
            return response.status_code in [204, 404]  # 404 is OK if already deleted
            
        except Exception as e:
            logger.error(f"Error deleting customer {customer_id}: {str(e)}")
            return False
    
    def get_test_customer_by_name(self, name: str) -> Optional[TestCustomer]:
        """Get test customer by name"""
        for customer in self.created_customers:
            if customer.customer_type == 'individual':
                full_name = f"{customer.first_name} {customer.last_name}"
                if name.lower() in full_name.lower():
                    return customer
            else:
                if name.lower() in customer.company_name.lower():
                    return customer
        return None
    
    def get_test_customers_with_balance(self, currency: str, min_amount: Decimal = Decimal('0')) -> List[TestCustomer]:
        """Get test customers with balance in specific currency"""
        return [
            customer for customer in self.created_customers
            if currency in customer.initial_balances and customer.initial_balances[currency] >= min_amount
        ]
