# Generated by Django 4.2.23 on 2025-08-13 08:44

import apps.accounts.models
from django.conf import settings
import django.contrib.auth.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('phone_number', models.CharField(blank=True, help_text='Contact phone number', max_length=20, validators=[django.core.validators.RegexValidator(message='Phone number must be entered in the format: "+*********". Up to 15 digits allowed.', regex='^\\+?1?\\d{9,15}$')], verbose_name='Phone number')),
                ('role', models.CharField(choices=[('admin', 'Admin'), ('accountant', 'Accountant'), ('branch_employee', 'Branch Employee'), ('viewer', 'Viewer'), ('courier', 'Courier')], default='viewer', help_text='User role determining access permissions', max_length=20, verbose_name='Role')),
                ('is_active_session', models.BooleanField(default=False, help_text='Whether the user currently has an active session', verbose_name='Has active session')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, help_text='IP address of the last login', null=True, verbose_name='Last login IP')),
                ('failed_login_attempts', models.PositiveIntegerField(default=0, help_text='Number of consecutive failed login attempts', verbose_name='Failed login attempts')),
                ('account_locked_until', models.DateTimeField(blank=True, help_text='Account is locked until this date/time', null=True, verbose_name='Account locked until')),
                ('employee_id', models.CharField(blank=True, help_text='Unique employee identifier', max_length=20, null=True, unique=True, verbose_name='Employee ID')),
                ('department', models.CharField(blank=True, help_text='Department or division', max_length=100, verbose_name='Department')),
                ('notes', models.TextField(blank=True, help_text='Internal notes about this user', verbose_name='Notes')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'ordering': ['last_name', 'first_name'],
            },
            managers=[
                ('objects', apps.accounts.models.CustomUserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('session_key', models.CharField(help_text='Django session key', max_length=40, unique=True, verbose_name='Session key')),
                ('ip_address', models.GenericIPAddressField(help_text='IP address of the session', verbose_name='IP address')),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent string', verbose_name='User agent')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this session is currently active', verbose_name='Is active')),
                ('ended_at', models.DateTimeField(blank=True, help_text='When the session ended', null=True, verbose_name='Ended at')),
                ('expires_at', models.DateTimeField(blank=True, help_text='When the session expires', null=True, verbose_name='Expires at')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'User Session',
                'verbose_name_plural': 'User Sessions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('action', models.CharField(choices=[('login', 'Login'), ('logout', 'Logout'), ('create', 'Create'), ('update', 'Update'), ('delete', 'Delete'), ('view', 'View'), ('export', 'Export'), ('import', 'Import'), ('approve', 'Approve'), ('reject', 'Reject')], help_text='Type of action performed', max_length=20, verbose_name='Action')),
                ('model_name', models.CharField(blank=True, help_text='Name of the model that was affected', max_length=100, verbose_name='Model name')),
                ('object_id', models.CharField(blank=True, help_text='ID of the object that was affected', max_length=100, verbose_name='Object ID')),
                ('object_repr', models.CharField(blank=True, help_text='String representation of the affected object', max_length=200, verbose_name='Object representation')),
                ('changes', models.JSONField(blank=True, default=dict, help_text='JSON representation of changes made', verbose_name='Changes')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address from which the action was performed', null=True, verbose_name='IP address')),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent string', verbose_name='User agent')),
                ('additional_data', models.JSONField(blank=True, default=dict, help_text='Additional context data', verbose_name='Additional data')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Audit Log',
                'verbose_name_plural': 'Audit Logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
