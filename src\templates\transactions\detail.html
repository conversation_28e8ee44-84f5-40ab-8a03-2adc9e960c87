{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transaction Details" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-receipt"></i>
                {% trans "Transaction Details" %}
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'transactions_web:list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {% trans "Back to List" %}
                </a>
                <button class="btn btn-primary" id="print-transaction">
                    <i class="bi bi-printer"></i>
                    {% trans "Print" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Status Alert -->
<div class="row mb-4" id="status-alert" style="display: none;">
    <div class="col-12">
        <div class="alert" id="status-alert-content">
        </div>
    </div>
</div>

<!-- Transaction Information -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    {% trans "Transaction Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Transaction Number" %}</label>
                            <p class="mb-0"><code id="transaction-number">-</code></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Customer" %}</label>
                            <p class="mb-0" id="customer-name">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Transaction Type" %}</label>
                            <p class="mb-0" id="transaction-type">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Location" %}</label>
                            <p class="mb-0" id="location-name">-</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Status" %}</label>
                            <p class="mb-0" id="transaction-status">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Created Date" %}</label>
                            <p class="mb-0" id="created-date">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Created By" %}</label>
                            <p class="mb-0" id="created-by">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Reference Number" %}</label>
                            <p class="mb-0" id="reference-number">-</p>
                        </div>
                    </div>
                </div>
                <div class="row" id="description-section">
                    <div class="col-12">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Description" %}</label>
                            <p class="mb-0" id="transaction-description">-</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Transaction Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear"></i>
                    {% trans "Actions" %}
                </h5>
            </div>
            <div class="card-body">
                <div id="transaction-actions">
                    <div class="text-center text-muted">
                        {% trans "Loading actions..." %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Info -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-speedometer2"></i>
                    {% trans "Quick Info" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">{% trans "Processing Time" %}</small>
                    <div class="fw-bold" id="processing-time">-</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">{% trans "Last Updated" %}</small>
                    <div class="fw-bold" id="last-updated">-</div>
                </div>
                <div class="mb-2">
                    <small class="text-muted">{% trans "Updated By" %}</small>
                    <div class="fw-bold" id="updated-by">-</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Exchange Details -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-arrow-left-right"></i>
                    {% trans "Exchange Details" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">{% trans "From" %}</h6>
                            <div class="border p-3">
                                <h4 class="mb-1" id="from-amount">-</h4>
                                <p class="mb-0 text-muted" id="from-currency">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">{% trans "Exchange Rate" %}</h6>
                            <div class="border p-3">
                                <h4 class="mb-1" id="exchange-rate">-</h4>
                                <p class="mb-0 text-muted" id="rate-type">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">{% trans "To" %}</h6>
                            <div class="border p-3">
                                <h4 class="mb-1" id="to-amount">-</h4>
                                <p class="mb-0 text-muted" id="to-currency">-</p>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Commission Amount" %}</label>
                            <p class="mb-0 text-success fw-bold" id="commission-amount">-</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Net Amount" %}</label>
                            <p class="mb-0 fw-bold" id="net-amount">-</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Balance Entries -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-journal-text"></i>
                    {% trans "Balance Entries" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="balance-entries-table">
                        <thead>
                            <tr>
                                <th>{% trans "Account" %}</th>
                                <th>{% trans "Currency" %}</th>
                                <th>{% trans "Debit" %}</th>
                                <th>{% trans "Credit" %}</th>
                                <th>{% trans "Balance" %}</th>
                                <th>{% trans "Date" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center text-muted">
                                    {% trans "Loading balance entries..." %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction History -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    {% trans "Transaction History" %}
                </h5>
            </div>
            <div class="card-body">
                <div id="transaction-history">
                    <div class="text-center text-muted">
                        {% trans "Loading history..." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Documents -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-earmark"></i>
                    {% trans "Documents" %}
                </h5>
                <button class="btn btn-sm btn-outline-primary" id="upload-document">
                    <i class="bi bi-upload"></i>
                    {% trans "Upload Document" %}
                </button>
            </div>
            <div class="card-body">
                <div id="documents-list">
                    <div class="text-center text-muted">
                        {% trans "Loading documents..." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Modal -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalTitle">{% trans "Transaction Action" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="action-form">
                    <input type="hidden" id="action-type">
                    
                    <div class="mb-3">
                        <label for="action-notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="action-notes" name="notes" rows="3" placeholder="{% trans 'Optional notes for this action...' %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="confirm-action">{% trans "Confirm" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.transaction-detail-card {
    border-left: 4px solid var(--bs-primary);
}

.status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.amount-display {
    font-size: 1.25rem;
    font-weight: 600;
}

.commission-info {
    background-color: var(--bs-light);
    border-radius: 0.375rem;
    padding: 1rem;
}

/* Timeline styles */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--bs-border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item-last {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    z-index: 1;
}

.timeline-content {
    background-color: var(--bs-light);
    border-radius: 0.375rem;
    padding: 1rem;
    border-left: 3px solid var(--bs-primary);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.timeline-body p {
    margin-bottom: 0.5rem;
}

@media print {
    .btn-group, .no-print {
        display: none !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }

    .timeline::before {
        background-color: #000 !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let transactionId = null;
let transactionData = null;

$(document).ready(function() {
    // Get transaction ID from URL
    const pathParts = window.location.pathname.split('/');
    transactionId = pathParts[pathParts.length - 2]; // Get UUID from URL
    
    if (transactionId) {
        loadTransactionDetails();
        loadBalanceEntries();
        loadTransactionHistory();
        loadTransactionDocuments();
    }
    
    // Event handlers
    $('#print-transaction').on('click', function() {
        window.print();
    });
    
    $('#confirm-action').on('click', function() {
        performTransactionAction();
    });
});

function loadTransactionDetails() {
    $.ajax({
        url: `/api/v1/transactions/transactions/${transactionId}/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            transactionData = data;
            populateTransactionDetails(data);
            generateActionButtons(data);
        },
        error: function(xhr) {
            console.error('Error loading transaction details:', xhr);
            showAlert('danger', '{% trans "Failed to load transaction details" %}');
        }
    });
}

function populateTransactionDetails(transaction) {
    $('#transaction-number').text(transaction.transaction_number || '-');

    // Handle customer information (nested object in detail view)
    if (transaction.customer) {
        const customerName = transaction.customer.display_name || transaction.customer.get_display_name ||
                           `${transaction.customer.first_name || ''} ${transaction.customer.last_name || ''}`.trim() ||
                           transaction.customer.company_name || 'Unknown Customer';
        $('#customer-name').html(`<a href="/customers/${transaction.customer.id}/" class="text-decoration-none">${customerName}</a>`);
    } else {
        $('#customer-name').text('-');
    }

    // Handle transaction type (nested object in detail view)
    const transactionTypeName = transaction.transaction_type ? transaction.transaction_type.name : '-';
    $('#transaction-type').text(transactionTypeName);

    // Handle location (nested object in detail view)
    const locationName = transaction.location ? transaction.location.name : '-';
    $('#location-name').text(locationName);

    $('#transaction-status').html(`<span class="badge bg-${getStatusClass(transaction.status)}">${transaction.status_display}</span>`);
    $('#created-date').text(transaction.created_at ? new Date(transaction.created_at).toLocaleString() : '-');
    $('#created-by').text(transaction.created_by_name || '-');
    $('#reference-number').text(transaction.reference_number || '-');
    $('#transaction-description').text(transaction.description || '-');
    
    // Exchange details
    $('#from-amount').text(transaction.from_amount || '-');
    $('#from-currency').text(transaction.from_currency_code || '-');
    $('#to-amount').text(transaction.to_amount || '-');
    $('#to-currency').text(transaction.to_currency_code || '-');
    $('#exchange-rate').text(transaction.exchange_rate || '-');
    $('#rate-type').text(transaction.rate_type_display || '-');
    $('#commission-amount').text(transaction.commission_amount || '-');
    $('#net-amount').text(transaction.net_amount || '-');
    
    // Quick info
    $('#processing-time').text(calculateProcessingTime(transaction.created_at, transaction.updated_at));
    $('#last-updated').text(transaction.updated_at ? new Date(transaction.updated_at).toLocaleString() : '-');
    $('#updated-by').text(transaction.updated_by_name || '-');
    
    // Status alert
    if (transaction.status === 'pending') {
        $('#status-alert').show();
        $('#status-alert-content').addClass('alert-warning').html(`
            <i class="bi bi-exclamation-triangle"></i>
            {% trans "This transaction is pending approval." %}
        `);
    } else if (transaction.status === 'rejected') {
        $('#status-alert').show();
        $('#status-alert-content').addClass('alert-danger').html(`
            <i class="bi bi-x-circle"></i>
            {% trans "This transaction has been rejected." %}
        `);
    }
}

function generateActionButtons(transaction) {
    let buttons = '';
    
    if (transaction.can_be_approved) {
        buttons += `
            <button class="btn btn-success btn-sm mb-2 w-100" onclick="showActionModal('approve')">
                <i class="bi bi-check-circle"></i>
                {% trans "Approve" %}
            </button>
        `;
    }
    
    if (transaction.status === 'approved') {
        buttons += `
            <button class="btn btn-info btn-sm mb-2 w-100" onclick="showActionModal('complete')">
                <i class="bi bi-check2-circle"></i>
                {% trans "Complete" %}
            </button>
        `;
    }
    
    if (transaction.can_be_cancelled) {
        buttons += `
            <button class="btn btn-danger btn-sm mb-2 w-100" onclick="showActionModal('cancel')">
                <i class="bi bi-x-circle"></i>
                {% trans "Cancel" %}
            </button>
        `;
    }
    
    if (transaction.can_be_rejected) {
        buttons += `
            <button class="btn btn-warning btn-sm mb-2 w-100" onclick="showActionModal('reject')">
                <i class="bi bi-slash-circle"></i>
                {% trans "Reject" %}
            </button>
        `;
    }
    
    if (buttons === '') {
        buttons = '<div class="text-center text-muted">{% trans "No actions available" %}</div>';
    }
    
    $('#transaction-actions').html(buttons);
}

function loadBalanceEntries() {
    $.ajax({
        url: `/api/v1/transactions/balance-entries/?transaction=${transactionId}`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayBalanceEntries(data.results || []);
        },
        error: function(xhr) {
            $('#balance-entries-table tbody').html('<tr><td colspan="6" class="text-center text-danger">{% trans "Failed to load balance entries" %}</td></tr>');
        }
    });
}

function displayBalanceEntries(entries) {
    const tbody = $('#balance-entries-table tbody');
    tbody.empty();
    
    if (entries.length > 0) {
        entries.forEach(function(entry) {
            const debitAmount = entry.entry_type === 'debit' ? entry.amount : '-';
            const creditAmount = entry.entry_type === 'credit' ? entry.amount : '-';
            const balanceClass = parseFloat(entry.balance_after) >= 0 ? 'text-success' : 'text-danger';
            
            const row = `
                <tr>
                    <td>${entry.account_name}</td>
                    <td>${entry.currency_code}</td>
                    <td class="text-end">${debitAmount}</td>
                    <td class="text-end">${creditAmount}</td>
                    <td class="text-end ${balanceClass}">${entry.balance_after}</td>
                    <td>${new Date(entry.created_at).toLocaleString()}</td>
                </tr>
            `;
            tbody.append(row);
        });
    } else {
        tbody.append('<tr><td colspan="6" class="text-center text-muted">{% trans "No balance entries found" %}</td></tr>');
    }
}

function loadTransactionHistory() {
    $.ajax({
        url: `/api/v1/accounts/audit-logs/?object_id=${transactionId}&model_name=Transaction`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayTransactionHistory(data.results || []);
        },
        error: function(xhr) {
            console.error('Error loading transaction history:', xhr);
            $('#transaction-history').html(
                '<div class="text-center text-muted">' +
                '{% trans "No history available or failed to load" %}' +
                '</div>'
            );
        }
    });
}

function displayTransactionHistory(history) {
    let html = '';

    if (history.length === 0) {
        html = '<div class="text-center text-muted">{% trans "No transaction history found" %}</div>';
    } else {
        html = '<div class="timeline">';

        history.forEach(function(entry, index) {
            const timestamp = new Date(entry.timestamp).toLocaleString();
            const actionClass = getActionClass(entry.action);
            const isLast = index === history.length - 1;

            html += `
                <div class="timeline-item ${isLast ? 'timeline-item-last' : ''}">
                    <div class="timeline-marker bg-${actionClass}">
                        <i class="bi ${getActionIcon(entry.action)}"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <h6 class="mb-1">${getActionTitle(entry.action)}</h6>
                            <small class="text-muted">${timestamp}</small>
                        </div>
                        <div class="timeline-body">
                            <p class="mb-1">${entry.description || getActionDescription(entry.action)}</p>
                            <small class="text-muted">
                                {% trans "by" %} ${entry.user_name || entry.user || 'System'}
                                ${entry.ip_address ? ' from ' + entry.ip_address : ''}
                            </small>
                            ${entry.additional_data ? `<div class="mt-2"><small class="text-muted">${JSON.stringify(entry.additional_data)}</small></div>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
    }

    $('#transaction-history').html(html);
}

function getActionClass(action) {
    const actionClasses = {
        'create': 'primary',
        'update': 'info',
        'approve': 'success',
        'reject': 'danger',
        'cancel': 'warning',
        'complete': 'success',
        'delete': 'danger'
    };
    return actionClasses[action] || 'secondary';
}

function getActionIcon(action) {
    const actionIcons = {
        'create': 'bi-plus-circle',
        'update': 'bi-pencil',
        'approve': 'bi-check-circle',
        'reject': 'bi-x-circle',
        'cancel': 'bi-exclamation-triangle',
        'complete': 'bi-check-circle-fill',
        'delete': 'bi-trash'
    };
    return actionIcons[action] || 'bi-circle';
}

function getActionTitle(action) {
    const actionTitles = {
        'create': '{% trans "Transaction Created" %}',
        'update': '{% trans "Transaction Updated" %}',
        'approve': '{% trans "Transaction Approved" %}',
        'reject': '{% trans "Transaction Rejected" %}',
        'cancel': '{% trans "Transaction Cancelled" %}',
        'complete': '{% trans "Transaction Completed" %}',
        'delete': '{% trans "Transaction Deleted" %}'
    };
    return actionTitles[action] || action.charAt(0).toUpperCase() + action.slice(1);
}

function getActionDescription(action) {
    const actionDescriptions = {
        'create': '{% trans "Transaction was created and is now pending approval" %}',
        'update': '{% trans "Transaction details were modified" %}',
        'approve': '{% trans "Transaction was approved and is ready for processing" %}',
        'reject': '{% trans "Transaction was rejected and requires revision" %}',
        'cancel': '{% trans "Transaction was cancelled" %}',
        'complete': '{% trans "Transaction was completed successfully" %}',
        'delete': '{% trans "Transaction was deleted" %}'
    };
    return actionDescriptions[action] || '{% trans "Transaction status changed" %}';
}

function loadTransactionDocuments() {
    $.ajax({
        url: `/api/v1/transactions/documents/?transaction=${transactionId}`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayDocuments(data.results || []);
        },
        error: function(xhr) {
            $('#documents-list').html('<div class="text-center text-danger">{% trans "Failed to load documents" %}</div>');
        }
    });
}

function displayDocuments(documents) {
    let html = '';
    
    if (documents.length > 0) {
        documents.forEach(function(doc) {
            html += `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${doc.document_type_display}</strong>
                            <p class="mb-0">${doc.title}</p>
                            <small class="text-muted">{% trans "Uploaded" %}: ${new Date(doc.created_at).toLocaleDateString()}</small>
                        </div>
                        <div>
                            <a href="${doc.file_url}" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="bi bi-download"></i>
                            </a>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        html = '<div class="text-center text-muted">{% trans "No documents found" %}</div>';
    }
    
    $('#documents-list').html(html);
}

function showActionModal(actionType) {
    $('#action-type').val(actionType);
    
    const titles = {
        'approve': '{% trans "Approve Transaction" %}',
        'complete': '{% trans "Complete Transaction" %}',
        'cancel': '{% trans "Cancel Transaction" %}',
        'reject': '{% trans "Reject Transaction" %}'
    };
    
    $('#actionModalTitle').text(titles[actionType]);
    $('#actionModal').modal('show');
}

function performTransactionAction() {
    const actionType = $('#action-type').val();
    const notes = $('#action-notes').val();
    
    $.ajax({
        url: `/api/v1/transactions/transactions/${transactionId}/${actionType}/`,
        method: 'POST',
        headers: getAuthHeaders(),
        data: JSON.stringify({ notes: notes }),
        success: function(data) {
            $('#actionModal').modal('hide');
            showAlert('success', data.message || '{% trans "Action completed successfully" %}');
            // Reload transaction details
            loadTransactionDetails();
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || '{% trans "Action failed" %}';
            showAlert('danger', error);
        }
    });
}

function calculateProcessingTime(createdAt, updatedAt) {
    if (!createdAt || !updatedAt) return '-';
    
    const created = new Date(createdAt);
    const updated = new Date(updatedAt);
    const diffMs = updated - created;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
        return `${diffHours}h ${diffMinutes}m`;
    } else {
        return `${diffMinutes}m`;
    }
}

function getStatusClass(status) {
    const statusClasses = {
        'draft': 'secondary',
        'pending': 'warning',
        'approved': 'info',
        'completed': 'success',
        'cancelled': 'danger',
        'rejected': 'danger'
    };
    return statusClasses[status] || 'secondary';
}

function getAuthHeaders() {
    const token = ArenaDoviz.auth.getAccessToken() || '';
    const headers = {
        'Content-Type': 'application/json'
    };

    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('main .container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
