{% extends 'transactions/base_form.html' %}
{% load i18n static %}

{% block transaction_specific_fields %}
<!-- International Transfer Details -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-globe"></i>
            {% trans "International Transfer Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "Send Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Send Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.01" required placeholder="0.00">
                    <div class="form-text">{% trans "Amount to send internationally" %}</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="to_currency" class="form-label">{% trans "Receive Currency" %}</label>
                    <select class="form-select" id="to_currency" name="to_currency">
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                    <div class="form-text">{% trans "Currency recipient will receive" %}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="to_amount" class="form-label">{% trans "Receive Amount" %}</label>
                    <input type="number" class="form-control" id="to_amount" name="to_amount" step="0.01" placeholder="0.00" readonly>
                    <div class="form-text">{% trans "Calculated based on exchange rate" %}</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="exchange_rate" class="form-label">{% trans "Exchange Rate" %}</label>
                    <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" step="0.000001" placeholder="0.000000" readonly>
                    <div class="form-text">{% trans "Current international rate" %}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="commission_amount" class="form-label">{% trans "Transfer Fee" %}</label>
                    <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.01" placeholder="0.00">
                    <div class="form-text">{% trans "International transfer fee" %}</div>
                </div>
            </div>
        </div>
        
        <!-- Balance Warning -->
        <div id="balance-warning"></div>
    </div>
</div>

<!-- Recipient Country & Bank Details -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-geo-alt"></i>
            {% trans "Destination Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="destination_country" class="form-label">{% trans "Destination Country" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="destination_country" name="destination_country" required>
                        <option value="">{% trans "Select country..." %}</option>
                        <option value="US">{% trans "United States" %}</option>
                        <option value="GB">{% trans "United Kingdom" %}</option>
                        <option value="DE">{% trans "Germany" %}</option>
                        <option value="FR">{% trans "France" %}</option>
                        <option value="CA">{% trans "Canada" %}</option>
                        <option value="AU">{% trans "Australia" %}</option>
                        <option value="JP">{% trans "Japan" %}</option>
                        <option value="CN">{% trans "China" %}</option>
                        <option value="IN">{% trans "India" %}</option>
                        <option value="OTHER">{% trans "Other" %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="destination_city" class="form-label">{% trans "Destination City" %}</label>
                    <input type="text" class="form-control" id="destination_city" name="destination_city" placeholder="{% trans 'City name...' %}">
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="bank_name" class="form-label">{% trans "Bank Name" %} <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="bank_name" name="bank_name" required placeholder="{% trans 'International bank name...' %}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="swift_code" class="form-label">{% trans "SWIFT Code" %} <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="swift_code" name="swift_code" required placeholder="{% trans 'SWIFT/BIC code...' %}">
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="account_number" class="form-label">{% trans "Account Number" %} <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="account_number" name="account_number" required placeholder="{% trans 'International account number...' %}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="account_name" class="form-label">{% trans "Beneficiary Name" %} <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="account_name" name="account_name" required placeholder="{% trans 'Beneficiary full name...' %}">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Compliance Information -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-shield-check"></i>
            {% trans "Compliance Information" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="transfer_purpose" class="form-label">{% trans "Purpose of Transfer" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="transfer_purpose" name="transfer_purpose" required>
                        <option value="">{% trans "Select purpose..." %}</option>
                        <option value="family_support">{% trans "Family Support" %}</option>
                        <option value="business_payment">{% trans "Business Payment" %}</option>
                        <option value="investment">{% trans "Investment" %}</option>
                        <option value="education">{% trans "Education Expenses" %}</option>
                        <option value="medical">{% trans "Medical Expenses" %}</option>
                        <option value="property">{% trans "Property Purchase" %}</option>
                        <option value="loan_payment">{% trans "Loan Payment" %}</option>
                        <option value="other">{% trans "Other" %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="relationship" class="form-label">{% trans "Relationship to Beneficiary" %}</label>
                    <select class="form-select" id="relationship" name="relationship">
                        <option value="">{% trans "Select relationship..." %}</option>
                        <option value="self">{% trans "Self" %}</option>
                        <option value="family">{% trans "Family Member" %}</option>
                        <option value="friend">{% trans "Friend" %}</option>
                        <option value="business">{% trans "Business Partner" %}</option>
                        <option value="other">{% trans "Other" %}</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="compliance_notes" class="form-label">{% trans "Additional Information" %}</label>
            <textarea class="form-control" id="compliance_notes" name="compliance_notes" rows="3" placeholder="{% trans 'Additional compliance information...' %}"></textarea>
            <div class="form-text">{% trans "Any additional information required for compliance" %}</div>
        </div>
        
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="compliance_declaration" name="compliance_declaration" required>
            <label class="form-check-label" for="compliance_declaration">
                {% trans "I declare that this transfer complies with all applicable laws and regulations" %} <span class="text-danger">*</span>
            </label>
        </div>
    </div>
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="{% static 'js/transactions/international_transfer.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = new InternationalTransferForm();
    form.init();
});
</script>
{% endblock %}
