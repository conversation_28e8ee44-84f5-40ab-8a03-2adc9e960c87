"""
Commission calculation utilities for Arena Doviz Exchange Accounting System.
Provides comprehensive commission calculation logic for different transaction types.
"""

from decimal import Decimal
from django.utils import timezone
from django.db.models import Q
from .commission_models import CommissionRule, CommissionTier
import logging

logger = logging.getLogger(__name__)


class CommissionCalculator:
    """
    Main class for calculating commissions based on transaction details and rules.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def calculate_commission(self, transaction):
        """
        Calculate commission for a transaction using the best matching rule.
        
        Args:
            transaction: Transaction object
            
        Returns:
            dict: {
                'amount': Decimal,
                'currency': Currency object,
                'rule': CommissionRule object or None,
                'breakdown': dict with calculation details
            }
        """
        try:
            # Find applicable commission rules
            applicable_rules = self._find_applicable_rules(transaction)

            if not applicable_rules:
                self.logger.info(f"No commission rules found for transaction {getattr(transaction, 'transaction_number', 'NEW')}")
                return {
                    'amount': Decimal('0'),
                    'currency': transaction.from_currency,
                    'rule': None,
                    'breakdown': {'message': 'No applicable commission rules found'}
                }
            
            # Select the best rule (highest priority)
            best_rule = applicable_rules[0]
            
            # Calculate commission based on rule type
            if best_rule.commission_type == CommissionRule.CommissionType.TIERED:
                return self._calculate_tiered_commission(transaction, best_rule)
            else:
                return self._calculate_standard_commission(transaction, best_rule)
                
        except Exception as e:
            self.logger.error(f"Error calculating commission for transaction {transaction.transaction_number}: {str(e)}")
            return {
                'amount': Decimal('0'),
                'currency': transaction.from_currency,
                'rule': None,
                'breakdown': {'error': str(e)}
            }
    
    def _find_applicable_rules(self, transaction):
        """
        Find all commission rules that apply to the given transaction.
        
        Args:
            transaction: Transaction object
            
        Returns:
            QuerySet: Applicable commission rules ordered by priority
        """
        now = timezone.now()
        
        # Base query for active rules
        query = Q(is_active=True)
        
        # Add effective date filters
        query &= Q(Q(effective_from__isnull=True) | Q(effective_from__lte=now))
        query &= Q(Q(effective_until__isnull=True) | Q(effective_until__gte=now))
        
        # Add location filter
        query &= Q(Q(location__isnull=True) | Q(location=transaction.location))
        
        # Add transaction type filter
        query &= Q(Q(transaction_type__isnull=True) | Q(transaction_type=transaction.transaction_type))
        
        # Add currency filters
        query &= Q(Q(from_currency__isnull=True) | Q(from_currency=transaction.from_currency))
        query &= Q(Q(to_currency__isnull=True) | Q(to_currency=transaction.to_currency))
        
        # Add amount range filters
        query &= Q(Q(min_amount__isnull=True) | Q(min_amount__lte=transaction.from_amount))
        query &= Q(Q(max_amount__isnull=True) | Q(max_amount__gte=transaction.from_amount))
        
        # Get rules and filter by applicable category
        rules = CommissionRule.objects.filter(query).order_by('priority', 'name')

        # Additional filtering for transaction categories
        applicable_rules = []
        for rule in rules:
            if rule.applies_to_transaction(transaction):
                applicable_rules.append(rule)

        return applicable_rules
    
    def _calculate_standard_commission(self, transaction, rule):
        """
        Calculate commission using standard rules (percentage, fixed, or hybrid).
        
        Args:
            transaction: Transaction object
            rule: CommissionRule object
            
        Returns:
            dict: Commission calculation result
        """
        commission_amount, commission_currency = rule.calculate_commission(transaction)
        
        breakdown = {
            'rule_name': rule.name,
            'rule_type': rule.get_commission_type_display(),
            'transaction_amount': transaction.from_amount,
            'calculation_details': {}
        }
        
        if rule.commission_type == CommissionRule.CommissionType.PERCENTAGE:
            breakdown['calculation_details'] = {
                'percentage_rate': rule.percentage_rate,
                'calculation': f"{transaction.from_amount} × {rule.percentage_rate}% = {commission_amount}"
            }
        elif rule.commission_type == CommissionRule.CommissionType.FIXED_AMOUNT:
            breakdown['calculation_details'] = {
                'fixed_amount': rule.fixed_amount,
                'calculation': f"Fixed amount: {commission_amount}"
            }
        elif rule.commission_type == CommissionRule.CommissionType.HYBRID:
            percentage_part = transaction.from_amount * (rule.percentage_rate / 100)
            breakdown['calculation_details'] = {
                'fixed_amount': rule.fixed_amount,
                'percentage_rate': rule.percentage_rate,
                'percentage_part': percentage_part,
                'calculation': f"Fixed: {rule.fixed_amount} + Percentage: {percentage_part} = {commission_amount}"
            }
        
        return {
            'amount': commission_amount,
            'currency': commission_currency,
            'rule': rule,
            'breakdown': breakdown
        }
    
    def _calculate_tiered_commission(self, transaction, rule):
        """
        Calculate commission using tiered structure.
        
        Args:
            transaction: Transaction object
            rule: CommissionRule object
            
        Returns:
            dict: Commission calculation result
        """
        tiers = rule.tiers.filter(
            min_amount__lte=transaction.from_amount
        ).filter(
            Q(max_amount__isnull=True) | Q(max_amount__gte=transaction.from_amount)
        ).order_by('min_amount')
        
        if not tiers.exists():
            return {
                'amount': Decimal('0'),
                'currency': rule.commission_currency or transaction.from_currency,
                'rule': rule,
                'breakdown': {'error': 'No applicable tier found'}
            }
        
        # Use the first matching tier (should be the most specific)
        tier = tiers.first()
        commission_currency = rule.commission_currency or transaction.from_currency
        
        if tier.percentage_rate:
            commission_amount = transaction.from_amount * (tier.percentage_rate / 100)
            calculation = f"{transaction.from_amount} × {tier.percentage_rate}% = {commission_amount}"
        elif tier.fixed_amount:
            commission_amount = tier.fixed_amount
            calculation = f"Fixed amount: {commission_amount}"
        else:
            commission_amount = Decimal('0')
            calculation = "No commission rate defined for tier"
        
        breakdown = {
            'rule_name': rule.name,
            'rule_type': 'Tiered',
            'tier_range': f"{tier.min_amount} - {tier.max_amount or '∞'}",
            'transaction_amount': transaction.from_amount,
            'calculation_details': {
                'tier_id': tier.id,
                'percentage_rate': tier.percentage_rate,
                'fixed_amount': tier.fixed_amount,
                'calculation': calculation
            }
        }
        
        return {
            'amount': commission_amount,
            'currency': commission_currency,
            'rule': rule,
            'breakdown': breakdown
        }
    
    def get_commission_preview(self, transaction_data):
        """
        Get commission preview for transaction data without creating a transaction.
        
        Args:
            transaction_data: Dict with transaction details
            
        Returns:
            dict: Commission preview result
        """
        # Create a temporary transaction-like object for calculation
        class TempTransaction:
            def __init__(self, data):
                self.transaction_number = 'PREVIEW'
                self.location = data.get('location')
                self.transaction_type = data.get('transaction_type')
                self.from_currency = data.get('from_currency')
                self.to_currency = data.get('to_currency')
                self.from_amount = data.get('from_amount', Decimal('0'))
                self.delivery_method = data.get('delivery_method')
            
            def is_exchange_transaction(self):
                return self.from_currency != self.to_currency
        
        temp_transaction = TempTransaction(transaction_data)
        return self.calculate_commission(temp_transaction)
    
    def bulk_calculate_commissions(self, transactions):
        """
        Calculate commissions for multiple transactions efficiently.
        
        Args:
            transactions: List or QuerySet of Transaction objects
            
        Returns:
            dict: Transaction ID -> commission calculation result
        """
        results = {}
        
        for transaction in transactions:
            results[transaction.id] = self.calculate_commission(transaction)
        
        return results


class CommissionRuleManager:
    """
    Manager class for commission rule operations.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def create_default_rules(self, location=None):
        """
        Create default commission rules for a location.
        
        Args:
            location: Location object (None for global rules)
        """
        try:
            # Default percentage rule for currency exchange
            exchange_rule, created = CommissionRule.objects.get_or_create(
                name=f"Default Exchange Commission{' - ' + location.name if location else ''}",
                location=location,
                applicable_for=CommissionRule.ApplicableFor.CURRENCY_EXCHANGE,
                defaults={
                    'description': 'Default commission for currency exchange transactions',
                    'commission_type': CommissionRule.CommissionType.PERCENTAGE,
                    'percentage_rate': Decimal('1.5'),  # 1.5%
                    'is_active': True,
                    'priority': 100
                }
            )
            
            if created:
                self.logger.info(f"Created default exchange commission rule: {exchange_rule.name}")
            
            # Default fixed rule for bank transfers
            transfer_rule, created = CommissionRule.objects.get_or_create(
                name=f"Default Transfer Commission{' - ' + location.name if location else ''}",
                location=location,
                applicable_for=CommissionRule.ApplicableFor.BANK_TRANSFER,
                defaults={
                    'description': 'Default commission for bank transfer transactions',
                    'commission_type': CommissionRule.CommissionType.FIXED_AMOUNT,
                    'fixed_amount': Decimal('10.00'),  # $10 fixed
                    'is_active': True,
                    'priority': 100
                }
            )
            
            if created:
                self.logger.info(f"Created default transfer commission rule: {transfer_rule.name}")
            
            return [exchange_rule, transfer_rule]
            
        except Exception as e:
            self.logger.error(f"Error creating default commission rules: {str(e)}")
            return []
    
    def validate_rule_conflicts(self, rule):
        """
        Check for potential conflicts with existing rules.
        
        Args:
            rule: CommissionRule object
            
        Returns:
            list: List of conflicting rules
        """
        conflicts = []
        
        # Find rules with same criteria
        similar_rules = CommissionRule.objects.filter(
            location=rule.location,
            transaction_type=rule.transaction_type,
            applicable_for=rule.applicable_for,
            from_currency=rule.from_currency,
            to_currency=rule.to_currency,
            is_active=True
        ).exclude(id=rule.id if rule.id else None)
        
        for similar_rule in similar_rules:
            # Check for overlapping amount ranges
            if self._ranges_overlap(
                rule.min_amount, rule.max_amount,
                similar_rule.min_amount, similar_rule.max_amount
            ):
                conflicts.append(similar_rule)
        
        return conflicts
    
    def _ranges_overlap(self, min1, max1, min2, max2):
        """Check if two amount ranges overlap."""
        # Convert None to appropriate values for comparison
        min1 = min1 or Decimal('0')
        max1 = max1 or Decimal('999999999999')
        min2 = min2 or Decimal('0')
        max2 = max2 or Decimal('999999999999')
        
        return not (max1 < min2 or max2 < min1)


# Global instances
commission_calculator = CommissionCalculator()
commission_rule_manager = CommissionRuleManager()
