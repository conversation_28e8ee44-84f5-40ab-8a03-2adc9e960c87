"""
Gunicorn configuration for Arena Doviz production server.
"""

import multiprocessing
import os

# Server socket
bind = "0.0.0.0:8000"  # Bind to all interfaces on port 8000
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# Process naming
proc_name = "arena_doviz"

# Server mechanics
daemon = False
pidfile = "logs/gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# SSL (uncomment when you have SSL certificates)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Application
pythonpath = "src"
chdir = "."

# Environment
raw_env = [
    "DJANGO_SETTINGS_MODULE=config.settings.prod",
]

# Preload application for better performance
preload_app = True

# Worker timeout
graceful_timeout = 30

# Enable threading
threads = 2

# Create logs directory
os.makedirs("logs", exist_ok=True)
