"""
Transactions app configuration for Arena Doviz Exchange Accounting System.
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class TransactionsConfig(AppConfig):
    """Configuration for the transactions app."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.transactions'
    verbose_name = 'Arena Doviz Transactions'
    
    def ready(self):
        """
        Initialize the transactions app when Django starts.
        """
        logger.info("Arena Doviz Transactions app is ready")
        
        # Import signal handlers
        try:
            from . import signals
            logger.debug("Transactions app signals imported successfully")
        except ImportError as e:
            logger.warning(f"Could not import transactions app signals: {e}")
        
        # Initialize transaction services
        self._initialize_transaction_services()

    def _initialize_transaction_services(self):
        """Initialize transaction processing services."""
        logger.debug("Initializing transaction services...")

        try:
            # Setup transaction types and default settings
            self._setup_transaction_types()

            # Setup default commission rules
            self._setup_default_commission_rules()

            logger.info("Transaction services initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize transaction services: {e}")

    def _setup_transaction_types(self):
        """Setup default transaction types."""
        # This will be implemented when the database is ready
        logger.debug("Transaction types setup completed")

    def _setup_default_commission_rules(self):
        """Set up default commission rules if none exist."""
        try:
            # Only run this in production/development, not during migrations
            import sys
            if 'migrate' in sys.argv or 'makemigrations' in sys.argv:
                return

            from django.db import connection
            from django.db.utils import OperationalError

            # Check if database is ready
            try:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
            except OperationalError:
                # Database not ready yet
                return

            from .commission_models import CommissionRule
            from .commission_utils import commission_rule_manager

            # Check if any commission rules exist
            if not CommissionRule.objects.filter(is_active=True, is_deleted=False).exists():
                logger.info("No commission rules found, creating default rules...")

                # Create default global rules
                default_rules = commission_rule_manager.create_default_rules()

                if default_rules:
                    logger.info(f"Created {len(default_rules)} default commission rules")
                else:
                    logger.warning("Failed to create default commission rules")
            else:
                logger.debug("Commission rules already exist, skipping default setup")

        except Exception as e:
            logger.error(f"Error setting up default commission rules: {str(e)}")
