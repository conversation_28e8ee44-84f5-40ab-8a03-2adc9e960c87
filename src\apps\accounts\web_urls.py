"""
Web URL configuration for Arena Doviz Accounts app.
"""

from django.urls import path
from django.contrib.auth import views as auth_views
from django.views.generic import TemplateView

app_name = 'accounts_web'

urlpatterns = [
    path('login/', auth_views.LoginView.as_view(
        template_name='accounts/simple_login.html',
        redirect_authenticated_user=True
    ), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('profile/', TemplateView.as_view(template_name='accounts/profile.html'), name='profile'),
]
