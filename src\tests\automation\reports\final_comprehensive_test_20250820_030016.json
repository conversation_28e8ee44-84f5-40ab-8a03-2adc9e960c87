{"timestamp": "2025-08-20T03:00:11.194714", "transfer_forms": {"forms": {"Internal Transfer": {"url": "/transactions/transfer/internal/add/", "status_code": 200, "accessible": true, "has_form": true}, "External Transfer": {"url": "/transactions/transfer/external/add/", "status_code": 200, "accessible": true, "has_form": true}, "International Transfer": {"url": "/transactions/transfer/international/add/", "status_code": 200, "accessible": true, "has_form": true}}, "apis": {"Customers": {"endpoint": "/api/v1/customers/customers/", "status_code": 200, "working": true}, "Locations": {"endpoint": "/api/v1/locations/locations/", "status_code": 200, "working": true}, "Currencies": {"endpoint": "/api/v1/currencies/currencies/", "status_code": 200, "working": true}, "Types": {"endpoint": "/api/v1/transactions/types/", "status_code": 200, "working": true}}, "status": "PASS"}, "production_deployment": {"scripts": {"setup_production.py": {"path": "src/scripts/setup_production.py", "exists": false, "size": 0}, "security_audit.py": {"path": "src/scripts/security_audit.py", "exists": false, "size": 0}, "load_test.py": {"path": "src/tests/load_testing/load_test.py", "exists": false, "size": 0}, "monitoring.py": {"path": "src/apps/core/monitoring.py", "exists": false, "size": 0}, "health_check.py": {"path": "src/apps/core/management/commands/health_check.py", "exists": false, "size": 0}, "production_settings": {"debug_disabled": true, "allowed_hosts_configured": true, "database_configured": false, "security_headers": true}}, "status": "PARTIAL"}, "security_audit": {"auditor_available": false, "error": "No module named 'security_audit'", "status": "ERROR"}, "overall_status": "TRANSFER_FORMS_FIXED"}