#!/usr/bin/env python
"""
Simple server for Arena Doviz
"""
import os
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set environment variables
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
os.environ.setdefault('DEBUG', 'True')
os.environ.setdefault('ARENA_ENCRYPTION_KEY', 'test-key-for-development-only-not-secure')

# Change to src directory
os.chdir(src_path)

# Import Django and setup
import django
django.setup()

# Start server
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    print("Starting Arena Doviz server...")
    print("Access at: http://127.0.0.1:8000")
    execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8000', '--noreload'])
