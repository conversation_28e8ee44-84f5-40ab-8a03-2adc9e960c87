"""
Tests for Arena Doviz encryption functionality.
"""

import os
import tempfile
from unittest.mock import patch
from django.test import TestCase, override_settings
from django.core.exceptions import ImproperlyConfigured, ValidationError
from apps.core.encryption import (
    AESEncryption, EncryptionError, encrypt_sensitive_data, decrypt_sensitive_data,
    generate_encryption_key, should_encrypt_field, mask_sensitive_value
)
from apps.core.fields import (
    EncryptedCharField, EncryptedTextField, EncryptedEmailField,
    EncryptedDecimalField, EncryptedJSONField
)


class EncryptionTestCase(TestCase):
    """Test cases for AES encryption functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_key = generate_encryption_key()
        self.test_data = "sensitive information"
        self.test_email = "<EMAIL>"
        self.test_json = {"key": "value", "number": 123}
    
    @override_settings(ARENA_ENCRYPTION_KEY=None)
    @patch.dict(os.environ, {}, clear=True)
    def test_encryption_key_generation_in_debug(self):
        """Test that encryption key is generated in DEBUG mode."""
        with override_settings(DEBUG=True):
            encryption = AESEncryption()
            self.assertIsNotNone(encryption._fernet)
    
    @override_settings(ARENA_ENCRYPTION_KEY=None, DEBUG=False)
    @patch.dict(os.environ, {}, clear=True)
    def test_encryption_key_required_in_production(self):
        """Test that encryption key is required in production."""
        with self.assertRaises(ImproperlyConfigured):
            AESEncryption()
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': 'invalid_key'})
    def test_invalid_encryption_key(self):
        """Test handling of invalid encryption key."""
        with self.assertRaises(ImproperlyConfigured):
            AESEncryption()
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encryption_decryption_cycle(self):
        """Test complete encryption/decryption cycle."""
        encryption = AESEncryption()
        
        # Test string encryption
        encrypted = encryption.encrypt(self.test_data)
        self.assertIsNotNone(encrypted)
        self.assertNotEqual(encrypted, self.test_data)
        
        # Test decryption
        decrypted = encryption.decrypt(encrypted)
        self.assertEqual(decrypted, self.test_data)
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encryption_with_none_values(self):
        """Test encryption with None and empty values."""
        encryption = AESEncryption()
        
        # Test None
        self.assertIsNone(encryption.encrypt(None))
        self.assertIsNone(encryption.decrypt(None))
        
        # Test empty string
        self.assertIsNone(encryption.encrypt(''))
        self.assertIsNone(encryption.decrypt(''))
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encryption_with_bytes(self):
        """Test encryption with bytes input."""
        encryption = AESEncryption()
        test_bytes = self.test_data.encode('utf-8')
        
        encrypted = encryption.encrypt(test_bytes)
        decrypted = encryption.decrypt(encrypted)
        
        self.assertEqual(decrypted, self.test_data)
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_convenience_functions(self):
        """Test convenience encryption/decryption functions."""
        encrypted = encrypt_sensitive_data(self.test_data)
        decrypted = decrypt_sensitive_data(encrypted)
        
        self.assertEqual(decrypted, self.test_data)
    
    def test_should_encrypt_field(self):
        """Test field encryption detection."""
        self.assertTrue(should_encrypt_field('phone_number'))
        self.assertTrue(should_encrypt_field('email'))
        self.assertFalse(should_encrypt_field('id'))
        self.assertFalse(should_encrypt_field('created_at'))
    
    def test_mask_sensitive_value(self):
        """Test sensitive value masking."""
        test_value = "1234567890"
        masked = mask_sensitive_value(test_value)
        self.assertEqual(masked, "******7890")
        
        # Test with custom parameters
        masked_custom = mask_sensitive_value(test_value, mask_char='X', visible_chars=2)
        self.assertEqual(masked_custom, "XXXXXXXX90")
        
        # Test with short value
        short_value = "123"
        masked_short = mask_sensitive_value(short_value)
        self.assertEqual(masked_short, "***")
    
    def test_generate_encryption_key(self):
        """Test encryption key generation."""
        key = generate_encryption_key()
        self.assertIsInstance(key, str)
        self.assertTrue(len(key) > 0)
        
        # Test that generated key is valid
        with patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': key}):
            encryption = AESEncryption()
            self.assertIsNotNone(encryption._fernet)


class EncryptedFieldsTestCase(TestCase):
    """Test cases for encrypted Django model fields."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_key = generate_encryption_key()
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encrypted_char_field(self):
        """Test EncryptedCharField functionality."""
        field = EncryptedCharField(max_length=100)
        
        # Test value preparation (encryption)
        test_value = "sensitive text"
        prep_value = field.get_prep_value(test_value)
        self.assertIsNotNone(prep_value)
        self.assertNotEqual(prep_value, test_value)
        
        # Test value conversion (decryption)
        python_value = field.to_python(prep_value)
        self.assertEqual(python_value, test_value)
        
        # Test None values
        self.assertIsNone(field.get_prep_value(None))
        self.assertIsNone(field.to_python(None))
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encrypted_email_field(self):
        """Test EncryptedEmailField functionality."""
        field = EncryptedEmailField()
        
        test_email = "<EMAIL>"
        prep_value = field.get_prep_value(test_email)
        python_value = field.to_python(prep_value)
        
        self.assertEqual(python_value, test_email)
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encrypted_decimal_field(self):
        """Test EncryptedDecimalField functionality."""
        from decimal import Decimal
        
        field = EncryptedDecimalField(max_digits=10, decimal_places=2)
        
        test_decimal = Decimal('123.45')
        prep_value = field.get_prep_value(test_decimal)
        python_value = field.to_python(prep_value)
        
        self.assertEqual(python_value, test_decimal)
        self.assertIsInstance(python_value, Decimal)
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encrypted_json_field(self):
        """Test EncryptedJSONField functionality."""
        field = EncryptedJSONField()
        
        test_json = {"key": "value", "number": 123, "list": [1, 2, 3]}
        prep_value = field.get_prep_value(test_json)
        python_value = field.to_python(prep_value)
        
        self.assertEqual(python_value, test_json)
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_field_masking(self):
        """Test field value masking functionality."""
        field = EncryptedCharField(max_length=100, mask_char='X', visible_chars=3)
        
        test_value = "1234567890"
        masked = field.get_masked_value(test_value)
        
        self.assertEqual(masked, "XXXXXXX890")
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_field_error_handling(self):
        """Test field error handling."""
        field = EncryptedCharField(max_length=100)
        
        # Test with corrupted encrypted data
        corrupted_data = "invalid_encrypted_data"
        # Should not raise exception, should return original value
        result = field.from_db_value(corrupted_data, None, None)
        self.assertEqual(result, corrupted_data)


class EncryptionIntegrationTestCase(TestCase):
    """Integration tests for encryption with Django models."""
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encryption_in_model_context(self):
        """Test encryption in the context of Django models."""
        # This would require actual model instances
        # For now, we test the field behavior independently
        
        field = EncryptedCharField(max_length=200)
        
        # Simulate model field usage
        test_data = "sensitive customer information"
        
        # Simulate saving to database
        encrypted_for_db = field.get_prep_value(test_data)
        
        # Simulate loading from database
        decrypted_from_db = field.from_db_value(encrypted_for_db, None, None)
        
        self.assertEqual(decrypted_from_db, test_data)
    
    def test_sensitive_fields_configuration(self):
        """Test sensitive fields configuration."""
        from apps.core.encryption import SENSITIVE_FIELDS
        
        # Ensure important fields are marked as sensitive
        expected_sensitive_fields = [
            'phone_number', 'email', 'address', 'notes',
            'reference_number', 'tracking_code', 'delivery_address'
        ]
        
        for field in expected_sensitive_fields:
            self.assertIn(field, SENSITIVE_FIELDS)


class EncryptionManagementCommandTestCase(TestCase):
    """Test cases for encryption management commands."""
    
    def test_key_generation_command_import(self):
        """Test that key generation command can be imported."""
        try:
            from apps.core.management.commands.generate_encryption_key import Command
            self.assertIsNotNone(Command)
        except ImportError:
            self.fail("Key generation command could not be imported")
    
    def test_data_encryption_command_import(self):
        """Test that data encryption command can be imported."""
        try:
            from apps.core.management.commands.encrypt_existing_data import Command
            self.assertIsNotNone(Command)
        except ImportError:
            self.fail("Data encryption command could not be imported")


class EncryptionSecurityTestCase(TestCase):
    """Security-focused tests for encryption functionality."""
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encryption_produces_different_outputs(self):
        """Test that encryption produces different outputs for same input."""
        encryption = AESEncryption()
        
        test_data = "same input data"
        
        # Encrypt the same data multiple times
        encrypted1 = encryption.encrypt(test_data)
        encrypted2 = encryption.encrypt(test_data)
        
        # Should produce different encrypted values (due to random IV)
        self.assertNotEqual(encrypted1, encrypted2)
        
        # But both should decrypt to the same original data
        self.assertEqual(encryption.decrypt(encrypted1), test_data)
        self.assertEqual(encryption.decrypt(encrypted2), test_data)
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encrypted_data_format(self):
        """Test that encrypted data follows expected format."""
        encryption = AESEncryption()
        
        test_data = "test data"
        encrypted = encryption.encrypt(test_data)
        
        # Should be base64 encoded
        import base64
        try:
            decoded = base64.b64decode(encrypted)
            self.assertIsInstance(decoded, bytes)
        except Exception:
            self.fail("Encrypted data is not valid base64")
    
    @patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': generate_encryption_key()})
    def test_encryption_with_unicode(self):
        """Test encryption with Unicode characters."""
        encryption = AESEncryption()
        
        unicode_data = "تست یونیکد 测试 🔒"
        encrypted = encryption.encrypt(unicode_data)
        decrypted = encryption.decrypt(encrypted)
        
        self.assertEqual(decrypted, unicode_data)


class AuditLoggingTestCase(TestCase):
    """Test cases for audit logging functionality."""

    def setUp(self):
        """Set up test environment."""
        from django.contrib.auth import get_user_model
        User = get_user_model()

        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Set up encryption key for tests
        self.encryption_key = generate_encryption_key()
        self.env_patcher = patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': self.encryption_key})
        self.env_patcher.start()

    def tearDown(self):
        """Clean up test environment."""
        self.env_patcher.stop()

    def test_audit_logger_initialization(self):
        """Test audit logger initialization."""
        from apps.core.audit import get_audit_logger

        audit_logger = get_audit_logger()
        self.assertIsNotNone(audit_logger)
        self.assertTrue(audit_logger.enabled)

    def test_log_user_action(self):
        """Test logging user actions."""
        from apps.core.audit import get_audit_logger
        from apps.core.models import EncryptedAuditLog

        audit_logger = get_audit_logger()

        # Log a user action
        audit_log = audit_logger.log_user_action(
            user=self.user,
            action_type='create',
            table_name='test_table',
            record_id=123,
            old_values={'field1': 'old_value'},
            new_values={'field1': 'new_value'},
            ip_address='127.0.0.1',
            user_agent='Test Browser'
        )

        self.assertIsNotNone(audit_log)
        self.assertEqual(audit_log.action_type, 'create')
        self.assertEqual(audit_log.table_name, 'test_table')
        self.assertEqual(audit_log.record_id, '123')
        self.assertEqual(audit_log.user_id, str(self.user.id))
        self.assertTrue(audit_log.success)

    def test_log_system_event(self):
        """Test logging system events."""
        from apps.core.audit import get_audit_logger

        audit_logger = get_audit_logger()

        # Log a system event
        audit_log = audit_logger.log_system_event(
            event_type='backup',
            description='Database backup completed',
            metadata={'backup_size': '1GB', 'duration': '5 minutes'}
        )

        self.assertIsNotNone(audit_log)
        self.assertEqual(audit_log.action_type, 'system')
        self.assertEqual(audit_log.table_name, 'system')
        self.assertTrue(audit_log.success)

    def test_log_login_attempt(self):
        """Test logging login attempts."""
        from apps.core.audit import get_audit_logger

        audit_logger = get_audit_logger()

        # Log successful login
        audit_log = audit_logger.log_login_attempt(
            username=self.user.username,
            success=True,
            ip_address='127.0.0.1',
            user_agent='Test Browser'
        )

        self.assertIsNotNone(audit_log)
        self.assertEqual(audit_log.action_type, 'login')
        self.assertTrue(audit_log.success)

        # Log failed login
        failed_log = audit_logger.log_login_attempt(
            username='nonexistent',
            success=False,
            ip_address='127.0.0.1',
            user_agent='Test Browser',
            error_message='User not found'
        )

        self.assertIsNotNone(failed_log)
        self.assertEqual(failed_log.action_type, 'login')
        self.assertFalse(failed_log.success)
        self.assertEqual(failed_log.error_message, 'User not found')

    def test_sensitive_data_masking(self):
        """Test that sensitive data is masked in audit logs."""
        from apps.core.audit import get_audit_logger

        audit_logger = get_audit_logger()

        # Log action with sensitive data
        sensitive_data = {
            'phone_number': '1234567890',
            'email': '<EMAIL>',
            'normal_field': 'normal_value'
        }

        audit_log = audit_logger.log_user_action(
            user=self.user,
            action_type='update',
            table_name='customers',
            record_id=1,
            new_values=sensitive_data
        )

        self.assertIsNotNone(audit_log)
        # The actual masking is done in the audit logger,
        # so we just verify the log was created

    def test_audit_trail_retrieval(self):
        """Test retrieving audit trail."""
        from apps.core.audit import get_audit_logger

        audit_logger = get_audit_logger()

        # Create some audit logs
        for i in range(5):
            audit_logger.log_user_action(
                user=self.user,
                action_type='create',
                table_name='test_table',
                record_id=i,
                new_values={'field': f'value_{i}'}
            )

        # Retrieve audit trail
        audit_trail = audit_logger.get_audit_trail(
            table_name='test_table',
            user_id=self.user.id,
            limit=10
        )

        self.assertEqual(len(audit_trail), 5)
        self.assertEqual(audit_trail[0].table_name, 'test_table')
        self.assertEqual(audit_trail[0].user_id, str(self.user.id))
