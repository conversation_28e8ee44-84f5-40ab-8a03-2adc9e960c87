"""
Django management command to encrypt existing sensitive data.
This command should be run after implementing encrypted fields.
"""

import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from django.apps import apps
from apps.core.encryption import encrypt_sensitive_data, SENSITIVE_FIELDS

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Encrypt existing sensitive data in the database."""
    
    help = 'Encrypt existing sensitive data in Arena Doviz database'
    
    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--model',
            type=str,
            help='Specific model to encrypt (format: app_label.ModelName)'
        )
        
        parser.add_argument(
            '--field',
            type=str,
            help='Specific field to encrypt'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be encrypted without making changes'
        )
        
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of records to process in each batch (default: 100)'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force encryption even if data appears to be already encrypted'
        )
    
    def handle(self, *args, **options):
        """Handle the command execution."""
        try:
            if options['model']:
                self._encrypt_model(options)
            else:
                self._encrypt_all_models(options)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Encryption failed: {str(e)}')
            )
            logger.error(f"Data encryption command failed: {str(e)}")
    
    def _encrypt_all_models(self, options):
        """Encrypt sensitive data in all relevant models."""
        models_to_encrypt = [
            ('customers', 'Customer'),
            ('transactions', 'Transaction'),
            ('accounts', 'User'),
        ]
        
        for app_label, model_name in models_to_encrypt:
            try:
                model_options = options.copy()
                model_options['model'] = f"{app_label}.{model_name}"
                self._encrypt_model(model_options)
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to encrypt {app_label}.{model_name}: {str(e)}'
                    )
                )
    
    def _encrypt_model(self, options):
        """Encrypt sensitive data in a specific model."""
        model_path = options['model']
        app_label, model_name = model_path.split('.')
        
        try:
            model_class = apps.get_model(app_label, model_name)
        except LookupError:
            self.stdout.write(
                self.style.ERROR(f'Model {model_path} not found')
            )
            return
        
        # Get sensitive fields for this model
        model_sensitive_fields = self._get_model_sensitive_fields(model_class)
        
        if options['field']:
            # Only encrypt specific field
            if options['field'] not in model_sensitive_fields:
                self.stdout.write(
                    self.style.WARNING(
                        f"Field '{options['field']}' is not marked as sensitive for {model_path}"
                    )
                )
                return
            model_sensitive_fields = [options['field']]
        
        if not model_sensitive_fields:
            self.stdout.write(
                self.style.WARNING(f'No sensitive fields found for {model_path}')
            )
            return
        
        self.stdout.write(f'Processing {model_path}...')
        self.stdout.write(f'Sensitive fields: {", ".join(model_sensitive_fields)}')
        
        # Process records in batches
        total_records = model_class.objects.count()
        batch_size = options['batch_size']
        processed = 0
        encrypted = 0
        
        self.stdout.write(f'Total records: {total_records}')
        
        for offset in range(0, total_records, batch_size):
            batch_records = model_class.objects.all()[offset:offset + batch_size]
            
            with transaction.atomic():
                for record in batch_records:
                    record_encrypted = self._encrypt_record(
                        record, model_sensitive_fields, options
                    )
                    if record_encrypted:
                        encrypted += 1
                    processed += 1
            
            self.stdout.write(f'Processed {processed}/{total_records} records...')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Completed {model_path}: {encrypted} records encrypted out of {processed} processed'
            )
        )
    
    def _encrypt_record(self, record, sensitive_fields, options):
        """Encrypt sensitive fields in a single record."""
        record_modified = False
        
        for field_name in sensitive_fields:
            if not hasattr(record, field_name):
                continue
            
            current_value = getattr(record, field_name)
            
            if not current_value:
                continue
            
            # Check if already encrypted (unless forced)
            if not options['force'] and self._appears_encrypted(current_value):
                continue
            
            if options['dry_run']:
                self.stdout.write(
                    f'Would encrypt {record.__class__.__name__}[{record.pk}].{field_name}'
                )
                record_modified = True
                continue
            
            try:
                encrypted_value = encrypt_sensitive_data(str(current_value))
                if encrypted_value:
                    # Set the encrypted value directly to bypass field encryption
                    setattr(record, field_name, encrypted_value)
                    record_modified = True
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to encrypt {record.__class__.__name__}[{record.pk}].{field_name}: {str(e)}'
                    )
                )
        
        if record_modified and not options['dry_run']:
            try:
                record.save(update_fields=sensitive_fields)
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to save {record.__class__.__name__}[{record.pk}]: {str(e)}'
                    )
                )
                return False
        
        return record_modified
    
    def _get_model_sensitive_fields(self, model_class):
        """Get list of sensitive fields for a model."""
        model_fields = [field.name for field in model_class._meta.get_fields()]
        return [field for field in model_fields if field in SENSITIVE_FIELDS]
    
    def _appears_encrypted(self, value):
        """Check if a value appears to be already encrypted."""
        if not isinstance(value, str):
            return False
        
        # Encrypted values are base64 encoded and typically much longer
        if len(value) < 50:
            return False
        
        try:
            import base64
            base64.b64decode(value)
            return True
        except Exception:
            return False
