"""
API views for Arena Doviz Reports app.
Handles report generation, templates, and analytics.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Sum, Q
from django.db import transaction
from django.utils import timezone
from django.http import HttpResponse, Http404
from datetime import timedel<PERSON>, datetime
from .models import ReportTemplate, GeneratedReport, ReportSchedule
from .serializers import (
    ReportTemplateSerializer, GeneratedReportSerializer, ReportGenerationRequestSerializer,
    CustomerStatementRequestSerializer, ReportScheduleSerializer, ReportStatsSerializer,
    BalanceReportSerializer, TransactionReportSerializer
)
from apps.core.utils import log_user_action, get_client_ip, calculate_customer_balance, calculate_company_balance
import logging
import os

logger = logging.getLogger(__name__)


class ReportTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for ReportTemplate management."""
    
    queryset = ReportTemplate.objects.filter(is_deleted=False)
    serializer_class = ReportTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'report_type', 'sort_order']
    ordering = ['sort_order', 'name']
    
    def get_queryset(self):
        """Filter templates based on user permissions."""
        user = self.request.user
        queryset = self.queryset.filter(is_active=True)
        
        # Filter by report type if specified
        report_type = self.request.query_params.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)
        
        # Filter by user's role permissions
        if not user.can_manage_users():
            queryset = queryset.filter(
                Q(is_public=True) | Q(allowed_roles__contains=user.role)
            )
        
        return queryset


class GeneratedReportViewSet(viewsets.ModelViewSet):
    """ViewSet for GeneratedReport management."""
    
    queryset = GeneratedReport.objects.filter(is_deleted=False)
    serializer_class = GeneratedReportSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'template__name']
    ordering_fields = ['title', 'generation_started_at', 'status']
    ordering = ['-generation_started_at']
    
    def get_queryset(self):
        """Filter reports based on user permissions."""
        user = self.request.user
        queryset = self.queryset
        
        # Non-admin users can only see their own reports
        if not user.can_manage_users():
            queryset = queryset.filter(generated_by=user)
        
        # Filter by status (check if query_params exists for DRF requests)
        if hasattr(self.request, 'query_params'):
            status_filter = self.request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # Filter by template type
            template_type = self.request.query_params.get('template_type')
            if template_type:
                queryset = queryset.filter(template__report_type=template_type)
        
        # Filter by date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(generation_started_at__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(generation_started_at__date__lte=date_to)
        
        return queryset
    
    @action(detail=False, methods=['post'])
    def generate(self, request):
        """Generate a new report."""
        # Handle both template_id and report_type for backward compatibility
        data = request.data.copy()

        # If report_type is provided instead of template_id, find the template
        if 'report_type' in data and 'template_id' not in data:
            try:
                template = ReportTemplate.objects.filter(
                    report_type=data['report_type'],
                    is_active=True,
                    is_deleted=False
                ).first()

                if template:
                    data['template_id'] = str(template.id)
                else:
                    # Create a default template for this report type if none exists
                    template = self._create_default_template(data['report_type'])
                    data['template_id'] = str(template.id)
            except Exception as e:
                logger.error(f"Error finding template for report_type {data.get('report_type')}: {e}")
                return Response(
                    {'error': f'No template found for report type: {data.get("report_type")}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        serializer = ReportGenerationRequestSerializer(data=data, context={'request': request})

        if serializer.is_valid():
            template = ReportTemplate.objects.get(id=serializer.validated_data['template_id'])
            
            # Create the generated report record
            generated_report = GeneratedReport.objects.create(
                template=template,
                generated_by=request.user,
                title=serializer.validated_data['title'],
                parameters=serializer.validated_data['parameters'],
                format=serializer.validated_data['format'],
                status=GeneratedReport.Status.GENERATING
            )
            
            # Log the report generation request
            log_user_action(
                user=request.user,
                action='generate_report',
                model_name='GeneratedReport',
                object_id=str(generated_report.id),
                object_repr=str(generated_report),
                ip_address=get_client_ip(request),
                additional_data={
                    'template': template.name,
                    'format': serializer.validated_data['format'],
                    'parameters': serializer.validated_data['parameters']
                }
            )
            
            # Generate actual report content based on report type
            try:
                # Generate report with actual data
                self._generate_report_with_data(generated_report)
                
                logger.info(f"Report generation completed: {generated_report.title} - {timezone.now().strftime('%Y-%m-%d %H:%M')} by {request.user}")

                # Generate download URL
                download_url = f"/api/v1/reports/generated/{generated_report.id}/download/"

                return Response({
                    'message': 'Report generation completed successfully',
                    'report': GeneratedReportSerializer(generated_report).data,
                    'download_url': download_url
                })
                
            except Exception as e:
                generated_report.mark_failed(str(e))
                logger.error(f"Report generation failed: {e}")
                
                return Response(
                    {'error': f'Report generation failed: {str(e)}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _create_default_template(self, report_type):
        """Create a default template for the given report type."""
        template_configs = {
            'customer_statement': {
                'name': 'Customer Statement',
                'description': 'Default customer statement template',
                'required_parameters': ['date_from', 'date_to']
            },
            'balance_summary': {
                'name': 'Balance Summary',
                'description': 'Default balance summary template',
                'required_parameters': []
            },
            'balance_report': {
                'name': 'Balance Report',
                'description': 'Default balance report template',
                'required_parameters': []
            },
            'transaction_report': {
                'name': 'Transaction Report',
                'description': 'Default transaction report template',
                'required_parameters': ['date_from', 'date_to']
            },
            'daily_summary': {
                'name': 'Daily Summary',
                'description': 'Default daily summary template',
                'required_parameters': ['date_from', 'date_to']
            }
        }

        config = template_configs.get(report_type, {
            'name': f'{report_type.replace("_", " ").title()} Report',
            'description': f'Default {report_type} template',
            'required_parameters': []
        })

        template = ReportTemplate.objects.create(
            name=config['name'],
            report_type=report_type,
            description=config['description'],
            template_config={'required_parameters': config['required_parameters']},
            is_public=True,
            is_active=True
        )

        logger.info(f"Created default template for report type: {report_type}")
        return template
    
    @action(detail=False, methods=['post'])
    def generate_customer_statement(self, request):
        """Generate a customer statement report."""
        serializer = CustomerStatementRequestSerializer(data=request.data)
        
        if serializer.is_valid():
            # Get customer statement template
            try:
                template = ReportTemplate.objects.get(
                    report_type=ReportTemplate.ReportType.CUSTOMER_STATEMENT,
                    is_active=True,
                    is_deleted=False
                )
            except ReportTemplate.DoesNotExist:
                return Response(
                    {'error': 'Customer statement template not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Create the generated report record
            generated_report = GeneratedReport.objects.create(
                template=template,
                generated_by=request.user,
                title=f"Customer Statement - {serializer.validated_data['date_from']} to {serializer.validated_data['date_to']}",
                parameters=serializer.validated_data,
                format=serializer.validated_data['format'],
                status=GeneratedReport.Status.GENERATING
            )
            
            try:
                # Generate customer statement
                self._generate_customer_statement(generated_report, serializer.validated_data)
                
                logger.info(f"Customer statement generated: {generated_report.title} by {request.user}")

                # Generate download URL
                download_url = f"/api/v1/reports/generated/{generated_report.id}/download/"

                return Response({
                    'message': 'Customer statement generated successfully',
                    'report': GeneratedReportSerializer(generated_report).data,
                    'download_url': download_url
                })
                
            except Exception as e:
                generated_report.mark_failed(str(e))
                logger.error(f"Customer statement generation failed: {e}")
                
                return Response(
                    {'error': f'Customer statement generation failed: {str(e)}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download a generated report."""
        generated_report = self.get_object()
        
        # Check if report is downloadable
        if not generated_report.is_completed():
            return Response(
                {'error': 'Report is not ready for download'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if generated_report.is_expired():
            return Response(
                {'error': 'Report has expired'},
                status=status.HTTP_410_GONE
            )
        
        # Check if file exists
        if not generated_report.file_path or not os.path.exists(generated_report.file_path):
            return Response(
                {'error': 'Report file not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        try:
            # Read the file
            with open(generated_report.file_path, 'rb') as f:
                file_content = f.read()
            
            # Determine content type based on format
            content_types = {
                GeneratedReport.Format.PDF: 'application/pdf',
                GeneratedReport.Format.EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                GeneratedReport.Format.CSV: 'text/csv',
                GeneratedReport.Format.JSON: 'application/json',
                GeneratedReport.Format.HTML: 'text/html',
            }
            
            content_type = content_types.get(generated_report.format, 'application/octet-stream')
            
            # Create response
            response = HttpResponse(file_content, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{generated_report.title}.{generated_report.format}"'
            
            # Mark as downloaded
            generated_report.mark_downloaded()
            
            # Log the download
            log_user_action(
                user=request.user,
                action='download_report',
                model_name='GeneratedReport',
                object_id=str(generated_report.id),
                object_repr=str(generated_report),
                ip_address=get_client_ip(request),
                additional_data={
                    'title': generated_report.title,
                    'format': generated_report.format
                }
            )
            
            logger.info(f"Report downloaded: {generated_report.title} by {request.user}")
            
            return response
            
        except Exception as e:
            logger.error(f"Error downloading report {generated_report.id}: {e}")
            return Response(
                {'error': 'Error downloading report'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _generate_report_with_data(self, generated_report):
        """Generate report content with actual data based on report type."""
        import tempfile
        import os
        from django.utils import timezone
        from datetime import datetime, timedelta
        from django.db.models import Q, Sum, Count
        from apps.transactions.models import Transaction
        from apps.customers.models import Customer
        from apps.transactions.models import BalanceEntry
        from apps.locations.models import Location
        from apps.currencies.models import Currency

        # Get parameters
        parameters = generated_report.parameters or {}
        report_type = generated_report.template.report_type

        # Parse date parameters with proper error handling
        date_from_str = parameters.get('date_from', '')
        date_to_str = parameters.get('date_to', '')

        # Handle empty or invalid dates
        if date_from_str and date_from_str.strip():
            try:
                date_from = datetime.strptime(date_from_str, '%Y-%m-%d').date()
            except ValueError:
                logger.warning(f"Invalid date_from format: {date_from_str}, using default")
                date_from = (timezone.now() - timedelta(days=30)).date()
        else:
            date_from = (timezone.now() - timedelta(days=30)).date()

        if date_to_str and date_to_str.strip():
            try:
                date_to = datetime.strptime(date_to_str, '%Y-%m-%d').date()
            except ValueError:
                logger.warning(f"Invalid date_to format: {date_to_str}, using default")
                date_to = timezone.now().date()
        else:
            date_to = timezone.now().date()

        logger.info(f"Report generation - Type: {report_type}, Date range: {date_from} to {date_to}")

        try:
            if report_type == 'transaction_report' or report_type == 'transaction_summary':
                logger.info(f"Generating transaction report with {Transaction.objects.filter(created_at__date__gte=date_from, created_at__date__lte=date_to, is_deleted=False).count()} transactions")
                self._generate_transaction_report(generated_report, date_from, date_to, parameters)
            elif report_type == 'balance_summary' or report_type == 'balance_report':
                logger.info(f"Generating balance report for date range {date_from} to {date_to}")
                self._generate_balance_report(generated_report, date_from, date_to, parameters)
            elif report_type == 'customer_statement':
                # This is already handled by the existing method
                logger.info(f"Generating customer statement")
                self._generate_customer_statement(generated_report, parameters)
            elif report_type == 'customer_report':
                # Generate customer activity report
                logger.info(f"Generating customer activity report")
                self._generate_customer_activity_report(generated_report, date_from, date_to, parameters)
            elif report_type == 'profit_loss':
                logger.info(f"Generating profit & loss report")
                self._generate_profit_loss_report(generated_report, date_from, date_to, parameters)
            else:
                logger.warning(f"Unknown report type: {report_type}, using placeholder")
                # Fallback to placeholder for other report types
                self._generate_report_content(generated_report)

        except Exception as e:
            logger.error(f"Error generating {report_type} report: {str(e)}", exc_info=True)
            # Fallback to placeholder if there's an error
            self._generate_report_content(generated_report)

    def _generate_report_content(self, generated_report):
        """Generate the actual report content."""
        import tempfile
        import json
        import os
        from datetime import datetime

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{generated_report.format}') as temp_file:
            if generated_report.format == GeneratedReport.Format.JSON:
                dummy_data = {
                    'report_title': generated_report.title,
                    'generated_at': timezone.now().isoformat(),
                    'parameters': generated_report.parameters,
                    'data': 'This is a placeholder report'
                }
                temp_file.write(json.dumps(dummy_data, indent=2).encode())

            elif generated_report.format == GeneratedReport.Format.PDF:
                # Generate a proper PDF file
                self._generate_pdf_report(temp_file, generated_report)

            elif generated_report.format == GeneratedReport.Format.CSV:
                # Generate CSV content
                csv_content = "Report Title,Generated At,Status\n"
                csv_content += f'"{generated_report.title}","{timezone.now().isoformat()}","Completed"\n'
                temp_file.write(csv_content.encode())

            elif generated_report.format == GeneratedReport.Format.EXCEL:
                # Generate Excel content (placeholder)
                temp_file.write(b'Excel report placeholder')

            else:
                # HTML or other formats
                html_content = f"""
                <html>
                <head><title>{generated_report.title}</title></head>
                <body>
                    <h1>{generated_report.title}</h1>
                    <p>Generated at: {timezone.now().isoformat()}</p>
                    <p>This is a placeholder report content.</p>
                </body>
                </html>
                """
                temp_file.write(html_content.encode())

            file_path = temp_file.name

        # Mark as completed
        file_size = os.path.getsize(file_path)
        generated_report.mark_completed(file_path, file_size, 1)

    def _generate_pdf_report(self, temp_file, generated_report):
        """Generate a proper PDF report using reportlab."""
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER, TA_LEFT

            # Create PDF document
            doc = SimpleDocTemplate(temp_file.name, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#000d28')
            )
            story.append(Paragraph(generated_report.title, title_style))
            story.append(Spacer(1, 20))

            # Report info
            info_data = [
                ['Report Type:', generated_report.template.report_type if generated_report.template else 'General Report'],
                ['Generated By:', generated_report.generated_by.get_full_name() or generated_report.generated_by.username],
                ['Generated At:', timezone.now().strftime('%Y-%m-%d %H:%M:%S')],
                ['Format:', generated_report.format.upper()],
            ]

            info_table = Table(info_data, colWidths=[2*inch, 4*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8f9fa')),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))

            story.append(info_table)
            story.append(Spacer(1, 30))

            # Report content
            content_style = ParagraphStyle(
                'Content',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                alignment=TA_LEFT
            )

            story.append(Paragraph("Report Content", styles['Heading2']))
            story.append(Spacer(1, 12))
            story.append(Paragraph("This is a sample report generated by Arena Doviz Exchange System.", content_style))
            story.append(Paragraph("The report contains placeholder data for demonstration purposes.", content_style))

            # Parameters section if available
            if generated_report.parameters:
                story.append(Spacer(1, 20))
                story.append(Paragraph("Report Parameters", styles['Heading2']))
                story.append(Spacer(1, 12))

                for key, value in generated_report.parameters.items():
                    story.append(Paragraph(f"<b>{key}:</b> {value}", content_style))

            # Footer
            story.append(Spacer(1, 50))
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=8,
                alignment=TA_CENTER,
                textColor=colors.grey
            )
            story.append(Paragraph("Generated by Arena Doviz Exchange System", footer_style))

            # Build PDF
            doc.build(story)

        except ImportError:
            # Fallback if reportlab is not available
            logger.warning("reportlab not available, generating simple PDF placeholder")
            # Create a simple text file as fallback
            content = f"""
Arena Doviz Exchange System
Report: {generated_report.title}
Generated: {timezone.now().isoformat()}
Generated By: {generated_report.generated_by.username}

This is a placeholder report. To generate proper PDF reports,
please install reportlab: pip install reportlab
"""
            temp_file.write(content.encode())
        except Exception as e:
            logger.error(f"Error generating PDF: {e}")
            # Fallback content
            content = f"Error generating PDF report: {str(e)}"
            temp_file.write(content.encode())

    def _generate_customer_statement_pdf(self, temp_file, generated_report, customer, transactions, date_from, date_to):
        """Generate a professional customer statement PDF matching exchange office standards."""
        logger.info(f"Generating PDF for customer: {customer.get_display_name()}")
        logger.info(f"Transaction count: {transactions.count()}")
        logger.info(f"Date range: {date_from} to {date_to}")

        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch, cm
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
            from reportlab.platypus.tableofcontents import TableOfContents
            from decimal import Decimal
            import os

            # Create PDF document with professional margins
            doc = SimpleDocTemplate(
                temp_file.name,
                pagesize=A4,
                topMargin=1*inch,
                bottomMargin=1*inch,
                leftMargin=0.75*inch,
                rightMargin=0.75*inch
            )
            styles = getSampleStyleSheet()
            story = []

            # Define Arena Doviz color scheme
            primary_color = colors.HexColor('#000d28')  # Dark navy
            secondary_color = colors.HexColor('#6a0000')  # Dark red
            tertiary_color = colors.HexColor('#013121')  # Dark green
            light_gray = colors.HexColor('#f8f9fa')

            # Company Header with Logo Area
            header_table_data = [
                ['ARENA DOVIZ EXCHANGE OFFICE', ''],
                ['Statement of Account', f'Statement Date: {timezone.now().strftime("%d %B %Y")}'],
                ['Licensed Money Exchange Services', f'Statement Period: {date_from.strftime("%d %B %Y")} - {date_to.strftime("%d %B %Y")}']
            ]

            header_table = Table(header_table_data, colWidths=[4*inch, 2.5*inch])
            header_table.setStyle(TableStyle([
                # Company name styling
                ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (0, 0), 18),
                ('TEXTCOLOR', (0, 0), (0, 0), primary_color),

                # Statement title
                ('FONTNAME', (0, 1), (0, 1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 1), (0, 1), 14),
                ('TEXTCOLOR', (0, 1), (0, 1), secondary_color),

                # Right side info
                ('FONTNAME', (1, 1), (1, 2), 'Helvetica-Bold'),
                ('FONTSIZE', (1, 1), (1, 2), 10),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),

                # General styling
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                ('TOPPADDING', (0, 0), (-1, -1), 2),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 2),
            ]))

            story.append(header_table)
            story.append(Spacer(1, 30))

            # Customer Information Section
            customer_header = ParagraphStyle(
                'CustomerHeader',
                parent=styles['Heading3'],
                fontSize=12,
                spaceAfter=10,
                textColor=primary_color,
                fontName='Helvetica-Bold'
            )
            story.append(Paragraph("CUSTOMER INFORMATION", customer_header))

            # Customer details in professional format
            customer_info = [
                ['Customer Code:', customer.customer_code or 'N/A'],
                ['Customer Name:', customer.get_display_name()],
                ['Customer Type:', customer.get_customer_type_display()],
                ['Phone Number:', customer.phone_number or 'N/A'],
                ['Email Address:', customer.email or 'N/A'],
                ['Address:', customer.get_full_address() or 'N/A'],
                ['Registration Date:', customer.registration_date.strftime('%d %B %Y') if customer.registration_date else 'N/A'],
                ['Account Status:', customer.get_status_display()],
            ]

            customer_table = Table(customer_info, colWidths=[2.2*inch, 4.3*inch])
            customer_table.setStyle(TableStyle([
                # Header column styling
                ('BACKGROUND', (0, 0), (0, -1), light_gray),
                ('TEXTCOLOR', (0, 0), (0, -1), primary_color),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (0, -1), 9),

                # Data column styling
                ('TEXTCOLOR', (1, 0), (1, -1), colors.black),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (1, 0), (1, -1), 9),

                # Table styling
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('LEFTPADDING', (0, 0), (-1, -1), 8),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))

            story.append(customer_table)
            story.append(Spacer(1, 25))

            # Account Balance Summary
            balance_header = ParagraphStyle(
                'BalanceHeader',
                parent=styles['Heading3'],
                fontSize=12,
                spaceAfter=10,
                textColor=primary_color,
                fontName='Helvetica-Bold'
            )
            story.append(Paragraph("ACCOUNT BALANCE SUMMARY", balance_header))

            # Get customer balance summary
            balance_summary = customer.get_balance_summary()
            if balance_summary:
                balance_data = [['Currency', 'Balance', 'Status']]
                for currency_code, balance_info in balance_summary.items():
                    balance_amount = balance_info['amount']
                    status = 'Credit' if balance_amount >= 0 else 'Debit'
                    balance_data.append([
                        currency_code,
                        f"{balance_info['symbol']}{abs(balance_amount):,.2f}",
                        status
                    ])

                balance_table = Table(balance_data, colWidths=[1.5*inch, 2*inch, 1.5*inch])
                balance_table.setStyle(TableStyle([
                    # Header styling
                    ('BACKGROUND', (0, 0), (-1, 0), primary_color),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

                    # Data styling
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 9),
                    ('ALIGN', (0, 1), (0, -1), 'LEFT'),
                    ('ALIGN', (1, 1), (1, -1), 'RIGHT'),
                    ('ALIGN', (2, 1), (2, -1), 'CENTER'),

                    # Table styling
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, light_gray]),
                    ('LEFTPADDING', (0, 0), (-1, -1), 8),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 8),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ]))
                story.append(balance_table)
            else:
                story.append(Paragraph("No balance information available.", styles['Normal']))

            story.append(Spacer(1, 25))

            # Transaction Details Section
            if transactions.exists():
                transaction_header = ParagraphStyle(
                    'TransactionHeader',
                    parent=styles['Heading3'],
                    fontSize=12,
                    spaceAfter=15,
                    textColor=primary_color,
                    fontName='Helvetica-Bold'
                )
                story.append(Paragraph("TRANSACTION DETAILS", transaction_header))

                # Enhanced table headers with better descriptions
                transaction_data = [
                    ['Date', 'Ref. No.', 'Type', 'From Amount', 'To Amount', 'Exchange Rate', 'Commission', 'Status']
                ]

                # Calculate totals and add transaction rows
                total_from_amount = Decimal('0')
                total_to_amount = Decimal('0')
                total_commission = Decimal('0')
                completed_count = 0

                for txn in transactions:
                    # Format commission
                    commission_str = 'N/A'
                    if hasattr(txn, 'commission_amount') and txn.commission_amount:
                        commission_str = f"{txn.commission_currency.code if hasattr(txn, 'commission_currency') and txn.commission_currency else 'USD'} {txn.commission_amount:,.2f}"
                        if txn.status == 'completed':
                            total_commission += txn.commission_amount

                    # Format status with color coding
                    status_display = txn.get_status_display()

                    transaction_data.append([
                        txn.created_at.strftime('%d/%m/%Y'),
                        txn.transaction_number[:12] + '...' if len(txn.transaction_number) > 15 else txn.transaction_number,
                        txn.transaction_type.name if txn.transaction_type else 'Exchange',
                        f"{txn.from_currency.code}\n{txn.from_amount:,.2f}",
                        f"{txn.to_currency.code}\n{txn.to_amount:,.2f}",
                        f"{txn.exchange_rate:,.4f}",
                        commission_str,
                        status_display
                    ])

                    if txn.status == 'completed':
                        total_from_amount += txn.from_amount
                        total_to_amount += txn.to_amount
                        completed_count += 1

                # Create enhanced transactions table with better column widths
                transactions_table = Table(transaction_data, colWidths=[0.8*inch, 1.1*inch, 0.8*inch, 1*inch, 1*inch, 0.9*inch, 0.9*inch, 0.8*inch])
                transactions_table.setStyle(TableStyle([
                    # Header row styling
                    ('BACKGROUND', (0, 0), (-1, 0), primary_color),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

                    # Data rows styling
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 7),
                    ('ALIGN', (0, 1), (0, -1), 'CENTER'),  # Date
                    ('ALIGN', (1, 1), (1, -1), 'LEFT'),    # Ref No
                    ('ALIGN', (2, 1), (2, -1), 'CENTER'),  # Type
                    ('ALIGN', (3, 1), (4, -1), 'CENTER'),  # Amounts
                    ('ALIGN', (5, 1), (5, -1), 'CENTER'),  # Rate
                    ('ALIGN', (6, 1), (6, -1), 'CENTER'),  # Commission
                    ('ALIGN', (7, 1), (7, -1), 'CENTER'),  # Status

                    # Table structure
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, light_gray]),

                    # Padding
                    ('LEFTPADDING', (0, 0), (-1, -1), 4),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 4),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ]))

                story.append(transactions_table)
                story.append(Spacer(1, 25))

                # Enhanced Summary Section
                summary_header = ParagraphStyle(
                    'SummaryHeader',
                    parent=styles['Heading3'],
                    fontSize=12,
                    spaceAfter=10,
                    textColor=primary_color,
                    fontName='Helvetica-Bold'
                )
                story.append(Paragraph("STATEMENT SUMMARY", summary_header))

                # Calculate additional statistics
                pending_count = transactions.filter(status__in=['draft', 'pending', 'approved']).count()
                cancelled_count = transactions.filter(status__in=['cancelled', 'rejected']).count()

                summary_data = [
                    ['Statement Period:', f"{date_from.strftime('%d %B %Y')} to {date_to.strftime('%d %B %Y')}"],
                    ['Total Transactions:', f"{transactions.count():,}"],
                    ['Completed Transactions:', f"{completed_count:,}"],
                    ['Pending Transactions:', f"{pending_count:,}"],
                    ['Cancelled Transactions:', f"{cancelled_count:,}"],
                    ['Total Commission Earned:', f"USD {total_commission:,.2f}" if total_commission > 0 else "N/A"],
                ]

                summary_table = Table(summary_data, colWidths=[2.5*inch, 3*inch])
                summary_table.setStyle(TableStyle([
                    # Header column
                    ('BACKGROUND', (0, 0), (0, -1), light_gray),
                    ('TEXTCOLOR', (0, 0), (0, -1), primary_color),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (0, -1), 9),

                    # Data column
                    ('TEXTCOLOR', (1, 0), (1, -1), colors.black),
                    ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                    ('FONTSIZE', (1, 0), (1, -1), 9),

                    # Table styling
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                    ('LEFTPADDING', (0, 0), (-1, -1), 8),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 8),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ]))

                story.append(summary_table)

            else:
                # No transactions message
                no_transactions_style = ParagraphStyle(
                    'NoTransactions',
                    parent=styles['Normal'],
                    fontSize=11,
                    spaceAfter=20,
                    alignment=TA_CENTER,
                    textColor=colors.grey
                )
                story.append(Paragraph("No transactions found for the selected period.", no_transactions_style))

            story.append(Spacer(1, 40))

            # Professional Footer
            footer_line = Table([['', '', '']], colWidths=[2*inch, 2.5*inch, 2*inch])
            footer_line.setStyle(TableStyle([
                ('LINEABOVE', (0, 0), (-1, 0), 1, colors.grey),
            ]))
            story.append(footer_line)
            story.append(Spacer(1, 15))

            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=8,
                alignment=TA_CENTER,
                textColor=colors.grey,
                spaceAfter=5
            )

            story.append(Paragraph("ARENA DOVIZ EXCHANGE OFFICE", footer_style))
            story.append(Paragraph("Licensed Money Exchange Services", footer_style))
            story.append(Paragraph("This statement is computer generated and does not require a signature.", footer_style))
            story.append(Paragraph(f"Generated on: {timezone.now().strftime('%d %B %Y at %H:%M:%S')}", footer_style))
            story.append(Paragraph("For inquiries, please contact our customer service department.", footer_style))

            # Build PDF
            doc.build(story)

        except ImportError:
            # Fallback if reportlab is not available
            logger.warning("reportlab not available for customer statement generation")
            content = f"""
ARENA DOVIZ EXCHANGE
CUSTOMER STATEMENT

Customer: {customer.get_display_name()}
Customer Code: {customer.customer_code}
Period: {date_from} to {date_to}
Generated: {timezone.now().isoformat()}

Transaction Count: {transactions.count()}

This is a text-based statement. To generate PDF statements,
please install reportlab: pip install reportlab
"""
            temp_file.write(content.encode())
        except Exception as e:
            logger.error(f"Error generating customer statement PDF: {e}")
            # Fallback content
            content = f"Error generating customer statement: {str(e)}"
            temp_file.write(content.encode())
    
    def _generate_customer_statement(self, generated_report, parameters):
        """Generate customer statement content with actual transaction data."""
        import tempfile
        from datetime import datetime
        from django.db.models import Q
        from apps.transactions.models import Transaction
        from apps.customers.models import Customer

        try:
            # Get customer
            customer_id = parameters.get('customer_id')
            if not customer_id:
                raise ValueError("Customer ID is required for customer statement")

            customer = Customer.objects.get(id=customer_id)

            # Get date range with defaults if empty
            date_from_str = parameters.get('date_from', '')
            date_to_str = parameters.get('date_to', '')

            if not date_from_str or not date_to_str:
                # Default to last 30 days if dates not provided
                from django.utils import timezone
                today = timezone.now().date()
                date_to = today
                date_from = today - timedelta(days=30)
                logger.warning(f"Empty dates provided, using default range: {date_from} to {date_to}")
            else:
                try:
                    date_from = datetime.strptime(date_from_str, '%Y-%m-%d').date()
                    date_to = datetime.strptime(date_to_str, '%Y-%m-%d').date()
                except ValueError as e:
                    logger.error(f"Invalid date format: {e}")
                    # Fallback to default dates
                    from django.utils import timezone
                    today = timezone.now().date()
                    date_to = today
                    date_from = today - timedelta(days=30)

            # Get transactions for the customer in the date range
            transactions = Transaction.objects.filter(
                customer=customer,
                created_at__date__gte=date_from,
                created_at__date__lte=date_to,
                is_deleted=False
            ).select_related(
                'transaction_type', 'from_currency', 'to_currency',
                'location', 'created_by'
            ).order_by('created_at')

            logger.info(f"Generating customer statement for {customer.get_display_name()} ({customer.customer_code})")
            logger.info(f"Date range: {date_from} to {date_to}")
            logger.info(f"Found {transactions.count()} transactions")

            # Generate PDF with actual data
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                logger.info(f"Starting PDF generation for customer statement")
                self._generate_customer_statement_pdf(temp_file, generated_report, customer, transactions, date_from, date_to)
                file_path = temp_file.name
                logger.info(f"PDF generated at: {file_path}")

            # Mark as completed
            file_size = os.path.getsize(file_path)
            logger.info(f"PDF file size: {file_size} bytes")
            generated_report.mark_completed(file_path, file_size, transactions.count())

        except Exception as e:
            logger.error(f"Error generating customer statement: {e}")
            # Fallback to placeholder if there's an error
            self._generate_report_content(generated_report)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get report statistics."""
        # Calculate basic statistics
        total_templates = ReportTemplate.objects.filter(is_deleted=False).count()
        active_templates = ReportTemplate.objects.filter(is_active=True, is_deleted=False).count()

        total_generated_reports = GeneratedReport.objects.filter(is_deleted=False).count()
        completed_reports = GeneratedReport.objects.filter(
            status=GeneratedReport.Status.COMPLETED,
            is_deleted=False
        ).count()
        failed_reports = GeneratedReport.objects.filter(
            status=GeneratedReport.Status.FAILED,
            is_deleted=False
        ).count()

        # Today's reports
        today = timezone.now().date()
        reports_generated_today = GeneratedReport.objects.filter(
            generation_started_at__date=today,
            is_deleted=False
        ).count()

        # Reports by type
        reports_by_type = dict(
            GeneratedReport.objects.filter(is_deleted=False)
            .values('template__report_type')
            .annotate(count=Count('id'))
            .values_list('template__report_type', 'count')
        )

        # Reports by format
        reports_by_format = dict(
            GeneratedReport.objects.filter(is_deleted=False)
            .values('format')
            .annotate(count=Count('id'))
            .values_list('format', 'count')
        )

        # Active schedules
        active_schedules = ReportSchedule.objects.filter(
            is_active=True,
            is_deleted=False
        ).count()

        # Total downloads
        total_downloads = GeneratedReport.objects.filter(
            is_deleted=False
        ).aggregate(
            total=Sum('download_count')
        )['total'] or 0

        # Average generation time (placeholder)
        average_generation_time = "2m 30s"  # This would be calculated from actual data

        stats_data = {
            'total_templates': total_templates,
            'active_templates': active_templates,
            'total_generated_reports': total_generated_reports,
            'completed_reports': completed_reports,
            'failed_reports': failed_reports,
            'reports_generated_today': reports_generated_today,
            'reports_by_type': reports_by_type,
            'reports_by_format': reports_by_format,
            'active_schedules': active_schedules,
            'total_downloads': total_downloads,
            'average_generation_time': average_generation_time
        }

        serializer = ReportStatsSerializer(stats_data)
        return Response(serializer.data)

    def _generate_transaction_report(self, generated_report, date_from, date_to, parameters):
        """Generate transaction report with actual data."""
        import tempfile
        import os
        from django.db.models import Q
        from apps.transactions.models import Transaction
        from apps.locations.models import Location
        from apps.currencies.models import Currency

        # Build query filters
        filters = Q(
            created_at__date__gte=date_from,
            created_at__date__lte=date_to,
            is_deleted=False
        )

        # Add location filter if specified
        location_id = parameters.get('location_id') or parameters.get('location')
        if location_id:
            filters &= Q(location_id=location_id)

        # Add currency filter if specified
        currency_code = parameters.get('currency_code')
        if currency_code:
            filters &= Q(Q(from_currency__code=currency_code) | Q(to_currency__code=currency_code))

        # Add status filter if specified
        status = parameters.get('status')
        if status:
            filters &= Q(status=status)

        # Get transactions
        transactions = Transaction.objects.filter(filters).select_related(
            'customer', 'location', 'from_currency', 'to_currency', 'transaction_type'
        ).order_by('-created_at')

        # Generate PDF with transaction data
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            self._generate_transaction_report_pdf(temp_file, generated_report, transactions, date_from, date_to)
            file_path = temp_file.name

        # Mark as completed
        file_size = os.path.getsize(file_path)
        generated_report.mark_completed(file_path, file_size, transactions.count())

    def _generate_balance_report(self, generated_report, date_from, date_to, parameters):
        """Generate balance report with actual data."""
        import tempfile
        import os
        from django.db.models import Q, Sum
        from apps.transactions.models import BalanceEntry
        from apps.locations.models import Location
        from apps.currencies.models import Currency

        # Build query filters
        filters = Q(is_deleted=False)

        # Add location filter if specified
        location_id = parameters.get('balance_location_id') or parameters.get('location_id') or parameters.get('location')
        if location_id:
            filters &= Q(location_id=location_id)

        # Add currency filter if specified
        currency_code = parameters.get('balance_currency_code') or parameters.get('currency_code')
        if currency_code:
            filters &= Q(currency__code=currency_code)

        # Get balance entries and aggregate them
        from django.db.models import Sum
        from apps.customers.models import Customer

        # Get aggregated balances by customer, location, and currency
        balance_aggregates = BalanceEntry.objects.filter(filters).values(
            'customer', 'location', 'currency'
        ).annotate(
            total_balance=Sum('amount')
        )

        # Convert to a list of balance objects for easier handling
        balances = []
        for agg in balance_aggregates:
            if agg['total_balance'] and agg['total_balance'] != 0:  # Only include non-zero balances
                # Get the actual objects
                customer = Customer.objects.get(id=agg['customer']) if agg['customer'] else None
                location = Location.objects.get(id=agg['location'])
                currency = Currency.objects.get(id=agg['currency'])

                # Create a simple object to hold the data
                balance_obj = type('Balance', (), {
                    'customer': customer,
                    'location': location,
                    'currency': currency,
                    'balance': agg['total_balance']
                })()
                balances.append(balance_obj)

        # Generate PDF with balance data
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            self._generate_balance_report_pdf(temp_file, generated_report, balances, date_from, date_to)
            file_path = temp_file.name

        # Mark as completed
        file_size = os.path.getsize(file_path)
        generated_report.mark_completed(file_path, file_size, len(balances))

    def _generate_transaction_report_pdf(self, temp_file, generated_report, transactions, date_from, date_to):
        """Generate transaction report PDF with actual data."""
        logger.info(f"Generating transaction report PDF with {transactions.count()} transactions")
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
            from decimal import Decimal

            # Create PDF document
            doc = SimpleDocTemplate(temp_file.name, pagesize=A4, topMargin=0.5*inch)
            styles = getSampleStyleSheet()
            story = []

            # Header
            header_style = ParagraphStyle(
                'Header',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=20,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#000d28')
            )
            story.append(Paragraph("ARENA DOVIZ EXCHANGE", header_style))
            story.append(Paragraph("TRANSACTION REPORT", header_style))
            story.append(Spacer(1, 20))

            # Report Information
            info_data = [
                ['Report Period:', f"{date_from.strftime('%Y-%m-%d')} to {date_to.strftime('%Y-%m-%d')}"],
                ['Generated On:', timezone.now().strftime('%Y-%m-%d %H:%M:%S')],
                ['Total Transactions:', str(transactions.count())],
            ]

            info_table = Table(info_data, colWidths=[2*inch, 4*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8f9fa')),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            story.append(info_table)
            story.append(Spacer(1, 20))

            # Transaction Table
            if transactions.exists():
                # Table headers
                headers = ['Date', 'Customer', 'Type', 'From', 'To', 'Amount', 'Rate', 'Status']
                data = [headers]

                # Add transaction data
                for transaction in transactions[:100]:  # Limit to first 100 transactions
                    row = [
                        transaction.created_at.strftime('%Y-%m-%d'),
                        transaction.customer.get_display_name()[:20] if transaction.customer else 'N/A',
                        transaction.transaction_type.name[:15] if transaction.transaction_type else 'N/A',
                        transaction.from_currency.code if transaction.from_currency else 'N/A',
                        transaction.to_currency.code if transaction.to_currency else 'N/A',
                        f"{transaction.from_amount:.2f}" if transaction.from_amount else '0.00',
                        f"{transaction.exchange_rate:.4f}" if transaction.exchange_rate else '0.0000',
                        transaction.get_status_display()[:10]
                    ]
                    data.append(row)

                # Create table
                table = Table(data, colWidths=[0.8*inch, 1.2*inch, 0.8*inch, 0.6*inch, 0.6*inch, 0.8*inch, 0.8*inch, 0.8*inch])
                table.setStyle(TableStyle([
                    # Header row
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#000d28')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

                    # Data rows
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 7),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

                    # Alternating row colors
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
                ]))
                story.append(table)
            else:
                story.append(Paragraph("No transactions found for the specified criteria.", styles['Normal']))

            # Footer
            story.append(Spacer(1, 30))
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=8,
                alignment=TA_CENTER,
                textColor=colors.grey
            )
            story.append(Paragraph("Generated by Arena Doviz Exchange System", footer_style))

            # Build PDF
            doc.build(story)

        except ImportError:
            # Fallback if reportlab is not available
            logger.warning("reportlab not available for transaction report generation")
            content = f"""
ARENA DOVIZ EXCHANGE
TRANSACTION REPORT

Period: {date_from} to {date_to}
Generated: {timezone.now().isoformat()}

Transaction Count: {transactions.count()}

This is a text-based report. To generate PDF reports,
please install reportlab: pip install reportlab
"""
            temp_file.write(content.encode())
        except Exception as e:
            logger.error(f"Error generating transaction report PDF: {e}")
            # Fallback content
            content = f"Error generating transaction report: {str(e)}"
            temp_file.write(content.encode())

    def _generate_balance_report_pdf(self, temp_file, generated_report, balances, date_from, date_to):
        """Generate balance report PDF with actual data."""
        logger.info(f"Generating balance report PDF with {len(balances)} balance entries")
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
            from decimal import Decimal

            # Create PDF document
            doc = SimpleDocTemplate(temp_file.name, pagesize=A4, topMargin=0.5*inch)
            styles = getSampleStyleSheet()
            story = []

            # Header
            header_style = ParagraphStyle(
                'Header',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=20,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#000d28')
            )
            story.append(Paragraph("ARENA DOVIZ EXCHANGE", header_style))
            story.append(Paragraph("BALANCE REPORT", header_style))
            story.append(Spacer(1, 20))

            # Report Information
            info_data = [
                ['Report Date:', date_to.strftime('%Y-%m-%d')],
                ['Generated On:', timezone.now().strftime('%Y-%m-%d %H:%M:%S')],
                ['Total Accounts:', str(len(balances))],
            ]

            info_table = Table(info_data, colWidths=[2*inch, 4*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8f9fa')),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            story.append(info_table)
            story.append(Spacer(1, 20))

            # Balance Table
            if balances:
                # Table headers
                headers = ['Customer', 'Location', 'Currency', 'Balance', 'Status']
                data = [headers]

                # Add balance data
                for balance in balances:
                    balance_amount = balance.balance or Decimal('0')

                    row = [
                        balance.customer.get_display_name()[:25] if balance.customer else 'N/A',
                        balance.location.name[:20] if balance.location else 'N/A',
                        balance.currency.code if balance.currency else 'N/A',
                        f"{balance_amount:.2f}",
                        'Credit' if balance_amount >= 0 else 'Debit'
                    ]
                    data.append(row)

                # Create table
                table = Table(data, colWidths=[2*inch, 1.5*inch, 0.8*inch, 1*inch, 0.8*inch])
                table.setStyle(TableStyle([
                    # Header row
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#000d28')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

                    # Data rows
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

                    # Alternating row colors
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
                ]))

                # Color negative balances red
                for i, balance in enumerate(balances, 1):
                    if hasattr(balance, 'balance') and balance.balance and balance.balance < 0:
                        table.setStyle(TableStyle([
                            ('TEXTCOLOR', (3, i), (3, i), colors.red),
                        ]))

                story.append(table)
            else:
                story.append(Paragraph("No balance records found for the specified criteria.", styles['Normal']))

            # Summary
            if balances:
                story.append(Spacer(1, 20))
                story.append(Paragraph("Summary by Currency", styles['Heading2']))

                # Group balances by currency
                currency_totals = {}
                for balance in balances:
                    currency_code = balance.currency.code if balance.currency else 'N/A'
                    if currency_code not in currency_totals:
                        currency_totals[currency_code] = Decimal('0')
                    currency_totals[currency_code] += balance.balance or Decimal('0')

                # Summary table
                summary_data = [['Currency', 'Total Balance']]
                for currency, total in currency_totals.items():
                    summary_data.append([currency, f"{total:.2f}"])

                summary_table = Table(summary_data, colWidths=[2*inch, 2*inch])
                summary_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#000d28')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
                ]))
                story.append(summary_table)

            # Footer
            story.append(Spacer(1, 30))
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=8,
                alignment=TA_CENTER,
                textColor=colors.grey
            )
            story.append(Paragraph("Generated by Arena Doviz Exchange System", footer_style))

            # Build PDF
            doc.build(story)

        except ImportError:
            # Fallback if reportlab is not available
            logger.warning("reportlab not available for balance report generation")
            content = f"""
ARENA DOVIZ EXCHANGE
BALANCE REPORT

Report Date: {date_to}
Generated: {timezone.now().isoformat()}

Balance Count: {len(balances)}

This is a text-based report. To generate PDF reports,
please install reportlab: pip install reportlab
"""
            temp_file.write(content.encode())
        except Exception as e:
            logger.error(f"Error generating balance report PDF: {e}")
            # Fallback content
            content = f"Error generating balance report: {str(e)}"
            temp_file.write(content.encode())

    def _generate_customer_activity_report(self, generated_report, date_from, date_to, parameters):
        """Generate customer activity report with actual data."""
        import tempfile
        import os
        from django.utils import timezone
        from apps.transactions.models import Transaction
        from apps.customers.models import Customer
        from apps.locations.models import Location
        from apps.currencies.models import Currency

        # Check if specific customer is selected
        customer_id = parameters.get('customer_id') or parameters.get('customer_report_id')
        if customer_id:
            # Generate report for specific customer
            try:
                customer = Customer.objects.get(id=customer_id, is_deleted=False)
                customers = [customer]
                report_title = f"Customer Report - {customer.get_display_name()}"
            except Customer.DoesNotExist:
                # Fallback to all customers if specific customer not found
                customers = Customer.objects.filter(
                    transactions__created_at__date__range=[date_from, date_to],
                    is_deleted=False
                ).distinct()
                report_title = "Customer Activity Report"
        else:
            # Get all customers with transactions in the date range
            customers = Customer.objects.filter(
                transactions__created_at__date__range=[date_from, date_to],
                is_deleted=False
            ).distinct()
            report_title = "Customer Activity Report"

        # Filter by location if specified
        location_id = parameters.get('location')
        if location_id:
            if customer_id:
                # For specific customer, just note the location filter
                pass
            else:
                customers = customers.filter(transactions__location_id=location_id)

        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w+b', delete=False, suffix='.pdf') as temp_file:
            self._generate_customer_activity_pdf(temp_file, generated_report, customers, date_from, date_to, parameters, report_title)
            file_path = temp_file.name

        # Update generated report
        file_size = os.path.getsize(file_path)
        customer_count = len(customers) if isinstance(customers, list) else customers.count()
        generated_report.mark_completed(file_path, file_size, customer_count)

    def _generate_customer_activity_pdf(self, temp_file, generated_report, customers, date_from, date_to, parameters=None, report_title="Customer Activity Report"):
        """Generate customer activity PDF with actual data."""
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.lib import colors
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.platypus import PageBreak
            from django.db.models import Sum, Count
            from apps.transactions.models import Transaction

            # Create PDF document
            doc = SimpleDocTemplate(temp_file.name, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Arena Doviz colors
            primary_color = colors.Color(0, 0.05, 0.16)  # #000d28
            secondary_color = colors.Color(0.42, 0, 0)   # #6a0000
            tertiary_color = colors.Color(0, 0.19, 0.13) # #013121

            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                textColor=primary_color,
                alignment=1  # Center
            )
            story.append(Paragraph(report_title, title_style))
            story.append(Spacer(1, 20))

            # Report info
            info_style = ParagraphStyle(
                'InfoStyle',
                parent=styles['Normal'],
                fontSize=10,
                textColor=colors.black
            )

            # Check if this is a single customer report
            is_single_customer = isinstance(customers, list) and len(customers) == 1
            customer_count = len(customers) if isinstance(customers, list) else customers.count()

            if is_single_customer:
                customer = customers[0]
                report_info = f"""
                <b>Customer:</b> {customer.get_display_name()}<br/>
                <b>Customer Code:</b> {customer.customer_code or 'N/A'}<br/>
                <b>Report Period:</b> {date_from.strftime('%Y-%m-%d')} to {date_to.strftime('%Y-%m-%d')}<br/>
                <b>Generated By:</b> {generated_report.generated_by.get_full_name() or generated_report.generated_by.username}<br/>
                <b>Generated At:</b> {generated_report.created_at.strftime('%Y-%m-%d %H:%M:%S')}
                """
            else:
                report_info = f"""
                <b>Report Period:</b> {date_from.strftime('%Y-%m-%d')} to {date_to.strftime('%Y-%m-%d')}<br/>
                <b>Generated By:</b> {generated_report.generated_by.get_full_name() or generated_report.generated_by.username}<br/>
                <b>Generated At:</b> {generated_report.created_at.strftime('%Y-%m-%d %H:%M:%S')}<br/>
                <b>Total Customers:</b> {customer_count}
                """
            story.append(Paragraph(report_info, info_style))
            story.append(Spacer(1, 20))

            if is_single_customer:
                # Generate detailed transaction report for single customer
                self._generate_single_customer_transactions(story, customers[0], date_from, date_to, parameters, styles, primary_color, secondary_color)
            else:
                # Generate customer activity summary table for multiple customers
                self._generate_customer_activity_table(story, customers, date_from, date_to, styles, primary_color)

            # Footer
            story.append(Spacer(1, 30))
            footer_style = ParagraphStyle(
                'FooterStyle',
                parent=styles['Normal'],
                fontSize=8,
                textColor=colors.grey,
                alignment=1
            )
            story.append(Paragraph("Generated by Arena Doviz Exchange System", footer_style))

            # Build PDF
            doc.build(story)

        except Exception as e:
            logger.error(f"Error generating customer activity PDF: {e}")
            content = f"Error generating customer activity report: {str(e)}"
            temp_file.write(content.encode())

    def _generate_profit_loss_report(self, generated_report, date_from, date_to, parameters):
        """Generate profit & loss report with actual data."""
        import tempfile
        import os
        from django.db.models import Q, Sum, Count
        from apps.transactions.models import Transaction
        from apps.locations.models import Location
        from apps.currencies.models import Currency
        from decimal import Decimal

        logger.info(f"Generating profit & loss report for {date_from} to {date_to}")

        # Get all transactions in date range
        transactions = Transaction.objects.filter(
            created_at__date__gte=date_from,
            created_at__date__lte=date_to,
            is_deleted=False
        ).select_related('from_currency', 'to_currency', 'location')

        # Filter by location if specified
        location_id = parameters.get('location')
        if location_id:
            transactions = transactions.filter(location_id=location_id)

        logger.info(f"Found {transactions.count()} transactions for profit & loss analysis")

        # Generate PDF with profit & loss data
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            self._generate_profit_loss_pdf(temp_file, generated_report, transactions, date_from, date_to)
            file_path = temp_file.name

        # Mark as completed
        file_size = os.path.getsize(file_path)
        generated_report.mark_completed(file_path, file_size, transactions.count())

    def _generate_profit_loss_pdf(self, temp_file, generated_report, transactions, date_from, date_to):
        """Generate profit & loss PDF with actual data."""
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch, cm
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
            from decimal import Decimal
            from collections import defaultdict

            # Create PDF document
            doc = SimpleDocTemplate(
                temp_file.name,
                pagesize=A4,
                topMargin=1*inch,
                bottomMargin=1*inch,
                leftMargin=0.75*inch,
                rightMargin=0.75*inch
            )

            # Get styles
            styles = getSampleStyleSheet()
            story = []

            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#000d28')
            )

            story.append(Paragraph("ARENA DOVIZ EXCHANGE", title_style))
            story.append(Paragraph("PROFIT & LOSS REPORT", title_style))
            story.append(Spacer(1, 20))

            # Period info
            period_style = ParagraphStyle(
                'Period',
                parent=styles['Normal'],
                fontSize=12,
                alignment=TA_CENTER,
                spaceAfter=20
            )
            story.append(Paragraph(f"Period: {date_from} to {date_to}", period_style))
            story.append(Spacer(1, 20))

            # Calculate profit/loss by currency
            currency_totals = defaultdict(lambda: {'revenue': Decimal('0'), 'cost': Decimal('0'), 'profit': Decimal('0'), 'count': 0})

            for transaction in transactions:
                currency_totals[transaction.from_currency.code]['count'] += 1
                # This is a simplified calculation - in reality you'd need more complex business logic
                if hasattr(transaction, 'commission_amount') and transaction.commission_amount:
                    currency_totals[transaction.from_currency.code]['revenue'] += transaction.commission_amount
                    currency_totals[transaction.from_currency.code]['profit'] += transaction.commission_amount

            # Create summary table
            table_data = [['Currency', 'Transactions', 'Revenue', 'Cost', 'Profit', 'Margin %']]

            for currency, data in currency_totals.items():
                margin = (data['profit'] / data['revenue'] * 100) if data['revenue'] > 0 else 0
                table_data.append([
                    currency,
                    str(data['count']),
                    f"{data['revenue']:.2f}",
                    f"{data['cost']:.2f}",
                    f"{data['profit']:.2f}",
                    f"{margin:.1f}%"
                ])

            # Create table
            table = Table(table_data, colWidths=[1*inch, 1*inch, 1.2*inch, 1.2*inch, 1.2*inch, 1*inch])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#000d28')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(table)
            story.append(Spacer(1, 30))

            # Summary
            total_transactions = transactions.count()
            story.append(Paragraph(f"<b>Total Transactions:</b> {total_transactions}", styles['Normal']))
            story.append(Paragraph(f"<b>Report Generated:</b> {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))

            # Build PDF
            doc.build(story)

        except Exception as e:
            logger.error(f"Error generating profit & loss PDF: {e}")
            content = f"Error generating profit & loss report: {str(e)}"
            temp_file.write(content.encode())

    def _generate_customer_activity_table(self, story, customers, date_from, date_to, styles, primary_color):
        """Generate customer activity summary table."""
        from reportlab.lib import colors
        from reportlab.lib.units import inch
        from reportlab.platypus import Table, TableStyle, Spacer, Paragraph
        from reportlab.lib.styles import ParagraphStyle
        from django.db.models import Sum
        from django.utils import timezone
        from apps.transactions.models import Transaction

        # Customer activity table
        table_data = [['Customer', 'Total Transactions', 'Total Volume (USD)', 'Last Transaction', 'Status']]

        for customer in customers:
                # Get customer transactions in date range
                customer_transactions = Transaction.objects.filter(
                    customer=customer,
                    created_at__date__range=[date_from, date_to],
                    is_deleted=False
                )

                total_transactions = customer_transactions.count()
                total_volume = customer_transactions.filter(
                    status=Transaction.Status.COMPLETED
                ).aggregate(total=Sum('from_amount'))['total'] or 0

                last_transaction = customer_transactions.order_by('-created_at').first()
                last_transaction_date = last_transaction.created_at.strftime('%Y-%m-%d') if last_transaction else 'N/A'

                # Determine customer status
                recent_transactions = customer_transactions.filter(
                    created_at__date__gte=(date_to - timezone.timedelta(days=7))
                ).count()
                status = 'Active' if recent_transactions > 0 else 'Inactive'

                table_data.append([
                    customer.get_display_name(),
                    str(total_transactions),
                    f"${total_volume:,.2f}",
                    last_transaction_date,
                    status
                ])

        # Create table
        table = Table(table_data, colWidths=[2*inch, 1.2*inch, 1.5*inch, 1.2*inch, 1*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), primary_color),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(table)
        story.append(Spacer(1, 20))

        # Summary statistics
        total_volume_all = sum(float(row[2].replace('$', '').replace(',', '')) for row in table_data[1:])
        total_transactions_all = sum(int(row[1]) for row in table_data[1:])
        active_customers = sum(1 for row in table_data[1:] if row[4] == 'Active')
        customer_count = len(table_data) - 1

        info_style = ParagraphStyle(
            'InfoStyle',
            parent=styles['Normal'],
            fontSize=10,
            textColor=colors.black
        )

        summary_text = f"""
        <b>Summary Statistics:</b><br/>
        • Total Active Customers: {active_customers}<br/>
        • Total Inactive Customers: {customer_count - active_customers}<br/>
        • Total Transaction Volume: ${total_volume_all:,.2f}<br/>
        • Total Transactions: {total_transactions_all}<br/>
        • Average Volume per Customer: ${total_volume_all/customer_count if customer_count > 0 else 0:,.2f}
        """
        story.append(Paragraph(summary_text, info_style))

    def _generate_single_customer_transactions(self, story, customer, date_from, date_to, parameters, styles, primary_color, secondary_color):
        """Generate detailed transaction list for a single customer."""
        from reportlab.lib import colors
        from reportlab.lib.units import inch
        from reportlab.platypus import Table, TableStyle, Spacer, Paragraph
        from reportlab.lib.styles import ParagraphStyle
        from django.db.models import Sum
        from apps.transactions.models import Transaction

        # Get customer transactions in date range
        transactions = Transaction.objects.filter(
            customer=customer,
            created_at__date__range=[date_from, date_to],
            is_deleted=False
        ).order_by('-created_at')

        # Filter by location if specified
        location_id = parameters.get('location') if parameters else None
        if location_id:
            transactions = transactions.filter(location_id=location_id)

        # Customer summary
        total_transactions = transactions.count()
        completed_transactions = transactions.filter(status=Transaction.Status.COMPLETED)
        total_volume = completed_transactions.aggregate(total=Sum('from_amount'))['total'] or 0
        total_commission = completed_transactions.aggregate(total=Sum('commission_amount'))['total'] or 0

        # Customer summary section
        summary_style = ParagraphStyle(
            'SummaryStyle',
            parent=styles['Normal'],
            fontSize=12,
            textColor=primary_color,
            spaceAfter=15
        )

        customer_summary = f"""
        <b>Customer Summary:</b><br/>
        • Total Transactions: {total_transactions}<br/>
        • Completed Transactions: {completed_transactions.count()}<br/>
        • Total Volume: ${total_volume:,.2f}<br/>
        • Total Commission: ${total_commission:,.2f}<br/>
        • Average Transaction: ${total_volume/completed_transactions.count() if completed_transactions.count() > 0 else 0:,.2f}
        """
        story.append(Paragraph(customer_summary, summary_style))
        story.append(Spacer(1, 15))

        # Transaction details table
        if transactions.exists():
            # Table header
            table_data = [['Date', 'Transaction #', 'Type', 'From Amount', 'To Amount', 'Rate', 'Commission', 'Status']]

            for transaction in transactions:
                table_data.append([
                    transaction.created_at.strftime('%Y-%m-%d'),
                    transaction.transaction_number,
                    transaction.transaction_type.name if transaction.transaction_type else 'N/A',
                    f"{transaction.from_currency.code} {transaction.from_amount:,.2f}" if transaction.from_currency else f"{transaction.from_amount:,.2f}",
                    f"{transaction.to_currency.code} {transaction.to_amount:,.2f}" if transaction.to_currency else f"{transaction.to_amount:,.2f}",
                    f"{transaction.exchange_rate:,.4f}" if transaction.exchange_rate else 'N/A',
                    f"${transaction.commission_amount:,.2f}" if transaction.commission_amount else '$0.00',
                    transaction.get_status_display()
                ])

            # Create transactions table
            transactions_table = Table(table_data, colWidths=[0.8*inch, 1.2*inch, 1*inch, 1.2*inch, 1.2*inch, 0.8*inch, 0.8*inch, 0.8*inch])
            transactions_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), primary_color),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))

            story.append(Paragraph("<b>Transaction Details:</b>", summary_style))
            story.append(transactions_table)
        else:
            story.append(Paragraph("No transactions found for the selected period.", styles['Normal']))


class ReportScheduleViewSet(viewsets.ModelViewSet):
    """ViewSet for ReportSchedule management."""

    queryset = ReportSchedule.objects.filter(is_deleted=False)
    serializer_class = ReportScheduleSerializer
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['next_run_at']

    def get_queryset(self):
        """Filter schedules based on user permissions."""
        user = self.request.user
        queryset = self.queryset

        # Only admins and accountants can manage schedules
        if not user.can_manage_users() and not user.is_accountant():
            return ReportSchedule.objects.none()

        # Filter by active status
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(is_active=True)

        return queryset

    @action(detail=False, methods=['get'])
    def due_schedules(self, request):
        """Get schedules that are due to run."""
        due_schedules = ReportSchedule.get_due_schedules()
        serializer = self.get_serializer(due_schedules, many=True)
        return Response(serializer.data)


class ReportDataViewSet(viewsets.ViewSet):
    """ViewSet for report data generation without file creation."""

    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def balance_report(self, request):
        """Generate balance report data."""
        location_id = request.query_params.get('location_id')
        currency_code = request.query_params.get('currency_code')

        try:
            # Get customer balances
            from apps.customers.models import Customer
            customers = Customer.objects.filter(is_deleted=False, status='active')

            customer_balances = []
            for customer in customers:
                balances = calculate_customer_balance(customer.id, currency_code, location_id)
                if balances:
                    customer_balances.append({
                        'customer': customer.get_display_name(),
                        'customer_id': str(customer.id),
                        'balances': balances
                    })

            # Get company balances
            company_balances = calculate_company_balance(currency_code, location_id)

            # Get locations and currencies for reference
            from apps.locations.models import Location
            from apps.currencies.models import Currency

            locations = [
                {'id': str(loc.id), 'name': loc.name}
                for loc in Location.objects.filter(is_active=True, is_deleted=False)
            ]

            currencies = [
                {'id': str(curr.id), 'code': curr.code, 'symbol': curr.symbol}
                for curr in Currency.objects.filter(is_active=True, is_deleted=False)
            ]

            # Calculate totals by currency
            total_balances_by_currency = {}
            # This would be implemented based on the actual balance calculation logic

            report_data = {
                'report_date': timezone.now(),
                'customer_balances': customer_balances,
                'company_balances': company_balances,
                'total_balances_by_currency': total_balances_by_currency,
                'locations': locations,
                'currencies': currencies
            }

            serializer = BalanceReportSerializer(report_data)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error generating balance report: {e}")
            return Response(
                {'error': f'Error generating balance report: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def transaction_report(self, request):
        """Generate transaction report data."""
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')
        location_id = request.query_params.get('location_id')
        currency_code = request.query_params.get('currency_code')

        if not date_from or not date_to:
            return Response(
                {'error': 'date_from and date_to parameters are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from apps.transactions.models import Transaction
            from datetime import datetime

            # Parse dates
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()

            # Get transactions
            transactions = Transaction.objects.filter(
                created_at__date__gte=date_from_obj,
                created_at__date__lte=date_to_obj,
                is_deleted=False
            )

            # Apply filters
            if location_id:
                transactions = transactions.filter(location_id=location_id)
            if currency_code:
                transactions = transactions.filter(
                    Q(from_currency__code=currency_code) | Q(to_currency__code=currency_code)
                )

            # Serialize transaction data
            from apps.transactions.serializers import TransactionListSerializer
            transaction_data = TransactionListSerializer(transactions, many=True).data

            # Calculate summary statistics
            summary = {
                'total_transactions': transactions.count(),
                'completed_transactions': transactions.filter(status='completed').count(),
                'total_volume': transactions.filter(status='completed').aggregate(
                    total=Sum('from_amount')
                )['total'] or 0,
                'total_commission': transactions.filter(status='completed').aggregate(
                    total=Sum('commission_amount')
                )['total'] or 0
            }

            report_data = {
                'report_date': timezone.now(),
                'date_from': date_from_obj,
                'date_to': date_to_obj,
                'transactions': transaction_data,
                'summary': summary,
                'totals_by_currency': {},  # Would be calculated
                'totals_by_location': {},  # Would be calculated
                'profit_analysis': {}  # Would be calculated
            }

            serializer = TransactionReportSerializer(report_data)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error generating transaction report: {e}")
            return Response(
                {'error': f'Error generating transaction report: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
