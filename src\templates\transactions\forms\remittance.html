{% extends 'transactions/base_form.html' %}
{% load i18n %}

{% block additional_basic_fields %}
<div class="col-md-6">
    <div class="mb-3">
        <label for="beneficiary_name" class="form-label">{% trans "Beneficiary Name" %} <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="beneficiary_name" name="beneficiary_name" required placeholder="{% trans 'Full name of beneficiary...' %}">
    </div>
</div>
{% endblock %}

{% block transaction_specific_fields %}
<!-- Remittance Details Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-globe"></i>
            {% trans "Remittance Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "Send Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Send Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.000001" required placeholder="0.00">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="exchange_rate" class="form-label">{% trans "Exchange Rate" %}</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" step="0.000001" placeholder="0.000000">
                        <button class="btn btn-outline-secondary" type="button" id="get-current-rate">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="to_currency" class="form-label">{% trans "Receive Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="to_currency" name="to_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="to_amount" class="form-label">{% trans "Receive Amount" %}</label>
                    <input type="number" class="form-control" id="to_amount" name="to_amount" step="0.000001" placeholder="0.00" readonly>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="commission_amount" class="form-label">
                        {% trans "Service Fee" %}
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="calculateRemittanceCommission()">
                            <i class="bi bi-calculator"></i> {% trans "Auto Calculate" %}
                        </button>
                    </label>
                    <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.000001" placeholder="0.00">
                    <div class="form-text" id="commission_info"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Beneficiary Information Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-person-lines-fill"></i>
            {% trans "Beneficiary Information" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="beneficiary_phone" class="form-label">{% trans "Beneficiary Phone" %}</label>
                    <input type="tel" class="form-control" id="beneficiary_phone" name="beneficiary_phone" placeholder="{% trans 'Phone number...' %}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="beneficiary_id" class="form-label">{% trans "Beneficiary ID Number" %}</label>
                    <input type="text" class="form-control" id="beneficiary_id" name="beneficiary_id" placeholder="{% trans 'ID/Passport number...' %}">
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="beneficiary_address" class="form-label">{% trans "Beneficiary Address" %} <span class="text-danger">*</span></label>
            <textarea class="form-control" id="beneficiary_address" name="beneficiary_address" rows="3" required placeholder="{% trans 'Complete address of beneficiary...' %}"></textarea>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="destination_country" class="form-label">{% trans "Destination Country" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="destination_country" name="destination_country" required>
                        <option value="">{% trans "Select country..." %}</option>
                        <option value="IR">{% trans "Iran" %}</option>
                        <option value="AE">{% trans "UAE" %}</option>
                        <option value="TR">{% trans "Turkey" %}</option>
                        <option value="CN">{% trans "China" %}</option>
                        <option value="US">{% trans "United States" %}</option>
                        <option value="GB">{% trans "United Kingdom" %}</option>
                        <option value="DE">{% trans "Germany" %}</option>
                        <option value="FR">{% trans "France" %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="destination_city" class="form-label">{% trans "Destination City" %} <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="destination_city" name="destination_city" required placeholder="{% trans 'City name...' %}">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delivery Method Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-truck"></i>
            {% trans "Delivery Method" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="delivery_method" class="form-label">{% trans "Delivery Method" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="delivery_method" name="delivery_method" required>
                        <option value="">{% trans "Select delivery method..." %}</option>
                        <option value="bank_transfer">{% trans "Bank Transfer" %}</option>
                        <option value="cash_pickup">{% trans "Cash Pickup" %}</option>
                        <option value="home_delivery">{% trans "Home Delivery" %}</option>
                        <option value="mobile_wallet">{% trans "Mobile Wallet" %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="pickup_location" class="form-label">{% trans "Pickup Location" %}</label>
                    <input type="text" class="form-control" id="pickup_location" name="pickup_location" placeholder="{% trans 'Agent location or address...' %}">
                </div>
            </div>
        </div>
        
        <div class="mb-3" id="bank-details" style="display: none;">
            <label for="bank_details" class="form-label">{% trans "Bank Details" %}</label>
            <textarea class="form-control" id="bank_details" name="bank_details" rows="3" placeholder="{% trans 'Bank name, account number, IBAN, SWIFT code...' %}"></textarea>
        </div>
        
        <div class="mb-3" id="mobile-wallet-details" style="display: none;">
            <label for="mobile_wallet" class="form-label">{% trans "Mobile Wallet Number" %}</label>
            <input type="text" class="form-control" id="mobile_wallet" name="mobile_wallet" placeholder="{% trans 'Mobile wallet number...' %}">
        </div>
    </div>
</div>

<!-- Purpose of Remittance -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-question-circle"></i>
            {% trans "Purpose of Remittance" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label for="remittance_purpose" class="form-label">{% trans "Purpose" %} <span class="text-danger">*</span></label>
            <select class="form-select" id="remittance_purpose" name="remittance_purpose" required>
                <option value="">{% trans "Select purpose..." %}</option>
                <option value="family_support">{% trans "Family Support" %}</option>
                <option value="education">{% trans "Education" %}</option>
                <option value="medical">{% trans "Medical Expenses" %}</option>
                <option value="business">{% trans "Business" %}</option>
                <option value="investment">{% trans "Investment" %}</option>
                <option value="other">{% trans "Other" %}</option>
            </select>
        </div>
        
        <div class="mb-3" id="purpose-details" style="display: none;">
            <label for="purpose_description" class="form-label">{% trans "Purpose Details" %}</label>
            <textarea class="form-control" id="purpose_description" name="purpose_description" rows="2" placeholder="{% trans 'Please provide more details...' %}"></textarea>
        </div>
    </div>
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="/static/js/transactions/remittance.js"></script>
{% endblock %}
