"""
Management command to set up default commission rules for Arena Doviz.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.transactions.commission_utils import commission_rule_manager
from apps.locations.models import Location
from apps.currencies.models import Currency
from apps.transactions.commission_models import CommissionRule
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up default commission rules for Arena Doviz Exchange'

    def add_arguments(self, parser):
        parser.add_argument(
            '--location',
            type=str,
            help='Specific location code to create rules for (optional)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing rules',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up commission rules...'))
        
        try:
            with transaction.atomic():
                # Get locations
                if options['location']:
                    locations = Location.objects.filter(code=options['location'])
                    if not locations.exists():
                        self.stdout.write(
                            self.style.ERROR(f'Location with code "{options["location"]}" not found')
                        )
                        return
                else:
                    locations = Location.objects.filter(is_active=True)
                
                # Create global rules first
                self.stdout.write('Creating global commission rules...')
                global_rules = self._create_global_rules(options['force'])
                
                # Create location-specific rules
                for location in locations:
                    self.stdout.write(f'Creating rules for location: {location.name}')
                    location_rules = self._create_location_rules(location, options['force'])
                
                self.stdout.write(
                    self.style.SUCCESS('Commission rules setup completed successfully!')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error setting up commission rules: {str(e)}')
            )
            logger.error(f"Commission rules setup failed: {str(e)}")

    def _create_global_rules(self, force=False):
        """Create global commission rules."""
        rules_created = []
        
        # Get USD currency for default rules
        try:
            usd_currency = Currency.objects.get(code='USD', is_active=True)
        except Currency.DoesNotExist:
            self.stdout.write(self.style.WARNING('USD currency not found, using first available currency'))
            usd_currency = Currency.objects.filter(is_active=True).first()
            if not usd_currency:
                self.stdout.write(self.style.ERROR('No active currencies found'))
                return rules_created

        # Default exchange commission rule
        rule_data = {
            'name': 'Global Exchange Commission',
            'description': 'Default commission for all currency exchange transactions',
            'applicable_for': CommissionRule.ApplicableFor.CURRENCY_EXCHANGE,
            'commission_type': CommissionRule.CommissionType.PERCENTAGE,
            'percentage_rate': Decimal('1.5'),  # 1.5%
            'commission_currency': usd_currency,
            'is_active': True,
            'priority': 100
        }
        
        rule, created = self._get_or_create_rule(rule_data, force)
        if created:
            rules_created.append(rule)
            self.stdout.write(f'  ✓ Created: {rule.name}')
        else:
            self.stdout.write(f'  - Exists: {rule.name}')

        # Default transfer commission rule
        rule_data = {
            'name': 'Global Transfer Commission',
            'description': 'Default commission for bank transfer transactions',
            'applicable_for': CommissionRule.ApplicableFor.BANK_TRANSFER,
            'commission_type': CommissionRule.CommissionType.FIXED_AMOUNT,
            'fixed_amount': Decimal('10.00'),  # $10 fixed
            'commission_currency': usd_currency,
            'is_active': True,
            'priority': 100
        }
        
        rule, created = self._get_or_create_rule(rule_data, force)
        if created:
            rules_created.append(rule)
            self.stdout.write(f'  ✓ Created: {rule.name}')
        else:
            self.stdout.write(f'  - Exists: {rule.name}')

        # High-value transaction commission (tiered)
        rule_data = {
            'name': 'High Value Transaction Commission',
            'description': 'Reduced commission for high-value transactions',
            'applicable_for': CommissionRule.ApplicableFor.CURRENCY_EXCHANGE,
            'commission_type': CommissionRule.CommissionType.PERCENTAGE,
            'percentage_rate': Decimal('1.0'),  # 1.0% for high value
            'min_amount': Decimal('10000.00'),  # $10,000+
            'commission_currency': usd_currency,
            'is_active': True,
            'priority': 50  # Higher priority than default
        }
        
        rule, created = self._get_or_create_rule(rule_data, force)
        if created:
            rules_created.append(rule)
            self.stdout.write(f'  ✓ Created: {rule.name}')
        else:
            self.stdout.write(f'  - Exists: {rule.name}')

        return rules_created

    def _create_location_rules(self, location, force=False):
        """Create location-specific commission rules."""
        rules_created = []
        
        # Location-specific exchange rule with slightly different rate
        rule_data = {
            'name': f'{location.name} Exchange Commission',
            'description': f'Location-specific commission for {location.name}',
            'location': location,
            'applicable_for': CommissionRule.ApplicableFor.CURRENCY_EXCHANGE,
            'commission_type': CommissionRule.CommissionType.PERCENTAGE,
            'percentage_rate': Decimal('1.2'),  # 1.2% for location-specific
            'is_active': True,
            'priority': 75  # Higher priority than global rules
        }
        
        rule, created = self._get_or_create_rule(rule_data, force)
        if created:
            rules_created.append(rule)
            self.stdout.write(f'  ✓ Created: {rule.name}')
        else:
            self.stdout.write(f'  - Exists: {rule.name}')

        return rules_created

    def _get_or_create_rule(self, rule_data, force=False):
        """Get or create a commission rule."""
        name = rule_data['name']
        location = rule_data.get('location')
        
        # Check if rule exists
        existing_rule = CommissionRule.objects.filter(
            name=name,
            location=location,
            is_deleted=False
        ).first()
        
        if existing_rule and not force:
            return existing_rule, False
        
        if existing_rule and force:
            # Update existing rule
            for key, value in rule_data.items():
                if key != 'name':  # Don't update name
                    setattr(existing_rule, key, value)
            existing_rule.save()
            return existing_rule, True
        
        # Create new rule
        rule = CommissionRule.objects.create(**rule_data)
        return rule, True
