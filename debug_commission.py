#!/usr/bin/env python
import os
import sys
import django

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')
django.setup()

from tests.base import BaseTestCase, TestDataMixin
from apps.transactions.commission_models import CommissionRule
from decimal import Decimal

class TestDebug(BaseTestCase, TestDataMixin):
    def debug_commission(self):
        self.setUp()
        
        # Create commission rule
        rule = self.create_commission_rule(
            location=self.location,
            from_currency=self.usd,
            percentage_rate=Decimal('2.0')
        )
        print(f'Created rule: {rule}')
        print(f'Rule location: {rule.location}')
        print(f'Rule from_currency: {rule.from_currency}')
        print(f'Rule percentage_rate: {rule.percentage_rate}')
        print(f'Rule is_active: {rule.is_active}')
        print(f'Rule applicable_for: {rule.applicable_for}')
        
        # Create transaction
        transaction = self.create_transaction(commission_amount=Decimal('0'))
        print(f'Transaction location: {transaction.location}')
        print(f'Transaction from_currency: {transaction.from_currency}')
        print(f'Transaction from_amount: {transaction.from_amount}')
        print(f'Transaction type: {transaction.transaction_type}')
        
        # Test rule application
        applies = rule.applies_to_transaction(transaction)
        print(f'Rule applies to transaction: {applies}')
        
        # Test commission calculation
        from apps.transactions.commission_utils import commission_calculator
        result = commission_calculator.calculate_commission(transaction)
        print(f'Commission result: {result}')

if __name__ == '__main__':
    test = TestDebug()
    test.debug_commission()
