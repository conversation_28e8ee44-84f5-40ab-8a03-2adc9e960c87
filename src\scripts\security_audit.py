#!/usr/bin/env python3
"""
Arena Doviz Security Audit Script
"""
import os
import sys
import subprocess
import json
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')
import django
django.setup()

from django.conf import settings
from django.contrib.auth import get_user_model

User = get_user_model()

class SecurityAuditor:
    """Security audit for Arena Doviz"""
    
    def __init__(self):
        self.findings = []
        self.recommendations = []
        
    def run_security_audit(self):
        """Run comprehensive security audit"""
        print("🔒 Arena Doviz Security Audit")
        print("=" * 50)
        
        # Check Django settings
        self.audit_django_settings()
        
        # Check user accounts
        self.audit_user_accounts()
        
        # Check file permissions
        self.audit_file_permissions()
        
        # Check dependencies
        self.audit_dependencies()
        
        # Check database security
        self.audit_database_security()
        
        # Generate report
        return self.generate_security_report()
    
    def audit_django_settings(self):
        """Audit Django security settings"""
        print("\n🔧 Auditing Django Settings...")
        
        # Check DEBUG setting
        if settings.DEBUG:
            self.findings.append({
                'level': 'HIGH',
                'category': 'Configuration',
                'issue': 'DEBUG mode is enabled in production',
                'recommendation': 'Set DEBUG = False in production settings'
            })
        
        # Check SECRET_KEY
        if hasattr(settings, 'SECRET_KEY'):
            if len(settings.SECRET_KEY) < 50:
                self.findings.append({
                    'level': 'HIGH',
                    'category': 'Configuration',
                    'issue': 'SECRET_KEY is too short',
                    'recommendation': 'Use a longer, more complex SECRET_KEY'
                })
        
        # Check ALLOWED_HOSTS
        if '*' in settings.ALLOWED_HOSTS:
            self.findings.append({
                'level': 'MEDIUM',
                'category': 'Configuration',
                'issue': 'ALLOWED_HOSTS contains wildcard',
                'recommendation': 'Restrict ALLOWED_HOSTS to specific domains'
            })
        
        # Check HTTPS settings
        if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
            self.findings.append({
                'level': 'MEDIUM',
                'category': 'HTTPS',
                'issue': 'HTTPS redirect not enforced',
                'recommendation': 'Enable SECURE_SSL_REDIRECT for production'
            })
        
        # Check session security
        if not getattr(settings, 'SESSION_COOKIE_SECURE', False):
            self.findings.append({
                'level': 'MEDIUM',
                'category': 'Session',
                'issue': 'Session cookies not marked as secure',
                'recommendation': 'Set SESSION_COOKIE_SECURE = True'
            })
        
        # Check CSRF protection
        if not getattr(settings, 'CSRF_COOKIE_SECURE', False):
            self.findings.append({
                'level': 'MEDIUM',
                'category': 'CSRF',
                'issue': 'CSRF cookies not marked as secure',
                'recommendation': 'Set CSRF_COOKIE_SECURE = True'
            })
        
        print("✅ Django settings audit complete")
    
    def audit_user_accounts(self):
        """Audit user accounts and permissions"""
        print("\n👥 Auditing User Accounts...")
        
        # Check for default passwords
        weak_passwords = ['admin', 'password', '123456', 'admin123']
        
        for user in User.objects.filter(is_active=True):
            # Check for weak usernames
            if user.username in ['admin', 'administrator', 'root']:
                self.findings.append({
                    'level': 'MEDIUM',
                    'category': 'User Account',
                    'issue': f'Weak username detected: {user.username}',
                    'recommendation': 'Use non-obvious usernames for admin accounts'
                })
            
            # Check for users without email
            if not user.email:
                self.findings.append({
                    'level': 'LOW',
                    'category': 'User Account',
                    'issue': f'User {user.username} has no email address',
                    'recommendation': 'Ensure all users have valid email addresses'
                })
        
        # Check superuser count
        superuser_count = User.objects.filter(is_superuser=True, is_active=True).count()
        if superuser_count > 3:
            self.findings.append({
                'level': 'MEDIUM',
                'category': 'User Account',
                'issue': f'Too many superusers: {superuser_count}',
                'recommendation': 'Limit the number of superuser accounts'
            })
        
        print(f"✅ User accounts audit complete ({User.objects.count()} users)")
    
    def audit_file_permissions(self):
        """Audit file and directory permissions"""
        print("\n📁 Auditing File Permissions...")
        
        # Check sensitive files
        sensitive_files = [
            '.env',
            '.env.production',
            'db.sqlite3',
            'db_production.sqlite3'
        ]
        
        for filename in sensitive_files:
            filepath = Path(filename)
            if filepath.exists():
                # Check if file is readable by others
                stat_info = filepath.stat()
                permissions = oct(stat_info.st_mode)[-3:]
                
                if permissions[-1] != '0':  # Others have permissions
                    self.findings.append({
                        'level': 'HIGH',
                        'category': 'File Permissions',
                        'issue': f'{filename} is readable by others ({permissions})',
                        'recommendation': f'Set restrictive permissions: chmod 600 {filename}'
                    })
        
        print("✅ File permissions audit complete")
    
    def audit_dependencies(self):
        """Audit Python dependencies for vulnerabilities"""
        print("\n📦 Auditing Dependencies...")
        
        try:
            # Run safety check if available
            result = subprocess.run(
                ['safety', 'check', '--json'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                vulnerabilities = json.loads(result.stdout)
                for vuln in vulnerabilities:
                    self.findings.append({
                        'level': 'HIGH',
                        'category': 'Dependencies',
                        'issue': f'Vulnerable package: {vuln.get("package", "unknown")}',
                        'recommendation': f'Update to version {vuln.get("analyzed_version", "latest")}'
                    })
            
        except (subprocess.TimeoutExpired, FileNotFoundError, json.JSONDecodeError):
            self.recommendations.append(
                'Install and run "safety" tool to check for vulnerable dependencies: pip install safety'
            )
        
        print("✅ Dependencies audit complete")
    
    def audit_database_security(self):
        """Audit database security settings"""
        print("\n🗄️ Auditing Database Security...")

        db_config = settings.DATABASES['default']

        # Check database engine
        if 'sqlite' in db_config['ENGINE']:
            self.findings.append({
                'level': 'MEDIUM',
                'category': 'Database',
                'issue': 'Using SQLite in production',
                'recommendation': 'Consider using PostgreSQL for better security and performance'
            })

        # Check for default database credentials
        if db_config.get('PASSWORD') in ['', 'password', 'admin']:
            self.findings.append({
                'level': 'HIGH',
                'category': 'Database',
                'issue': 'Weak or default database password',
                'recommendation': 'Use a strong, unique database password'
            })

        print("✅ Database security audit complete")
    
    def generate_security_report(self):
        """Generate comprehensive security report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_findings': len(self.findings),
            'findings_by_level': {
                'HIGH': len([f for f in self.findings if f['level'] == 'HIGH']),
                'MEDIUM': len([f for f in self.findings if f['level'] == 'MEDIUM']),
                'LOW': len([f for f in self.findings if f['level'] == 'LOW'])
            },
            'findings': self.findings,
            'recommendations': self.recommendations
        }
        
        return report
    
    def print_security_report(self, report):
        """Print formatted security report"""
        print("\n" + "=" * 60)
        print("🔒 SECURITY AUDIT REPORT")
        print("=" * 60)
        
        print(f"Audit Time: {report['timestamp']}")
        print(f"Total Findings: {report['total_findings']}")
        
        levels = report['findings_by_level']
        print(f"   HIGH: {levels['HIGH']}")
        print(f"   MEDIUM: {levels['MEDIUM']}")
        print(f"   LOW: {levels['LOW']}")
        
        if report['findings']:
            print(f"\n🚨 SECURITY FINDINGS:")
            
            for finding in sorted(report['findings'], key=lambda x: {'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}[x['level']], reverse=True):
                level = finding['level']
                category = finding['category']
                issue = finding['issue']
                recommendation = finding['recommendation']
                
                level_emoji = {'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}
                print(f"\n{level_emoji[level]} [{level}] {category}")
                print(f"   Issue: {issue}")
                print(f"   Fix: {recommendation}")
        
        if report['recommendations']:
            print(f"\n💡 GENERAL RECOMMENDATIONS:")
            for rec in report['recommendations']:
                print(f"   - {rec}")
        
        # Security score
        total_possible = 100
        deductions = {
            'HIGH': 20,
            'MEDIUM': 10,
            'LOW': 5
        }
        
        score = total_possible
        for level, count in levels.items():
            score -= deductions[level] * count
        
        score = max(0, score)
        
        print(f"\n📊 SECURITY SCORE: {score}/100")
        
        if score >= 90:
            print("🎉 Excellent security posture!")
        elif score >= 70:
            print("✅ Good security, minor improvements needed")
        elif score >= 50:
            print("⚠️ Moderate security, several issues to address")
        else:
            print("🚨 Poor security, immediate action required")

def main():
    """Main function"""
    auditor = SecurityAuditor()
    report = auditor.run_security_audit()
    auditor.print_security_report(report)
    
    # Save report
    report_file = f"security_audit_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: {report_file}")

if __name__ == "__main__":
    main()
