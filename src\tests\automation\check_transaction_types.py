"""
Check and fix transaction types
"""
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
import django
django.setup()

from apps.transactions.models import TransactionType

def check_and_fix_transaction_types():
    """Check and fix transaction types"""
    print("🔍 Checking current transaction types...")
    
    # Get current types
    current_types = TransactionType.objects.all()
    print(f"Found {len(current_types)} transaction types:")
    
    for tt in current_types:
        print(f"  - {tt.code}: {tt.name} (Active: {tt.is_active})")
    
    # Check if INTERNAL_TRANSFER exists
    internal_transfer = TransactionType.objects.filter(code='INTERNAL_TRANSFER').first()
    
    if not internal_transfer:
        print("\n⚠️ INTERNAL_TRANSFER type not found. Creating it...")
        
        # Create INTERNAL_TRANSFER type
        internal_transfer = TransactionType.objects.create(
            code='INTERNAL_TRANSFER',
            name='Internal Transfer',
            description='Transfer between customers within the same exchange office',
            is_exchange=False,
            requires_approval=False,
            is_active=True,
            sort_order=10
        )
        
        print(f"✅ Created INTERNAL_TRANSFER transaction type: {internal_transfer}")
    else:
        print(f"✅ INTERNAL_TRANSFER already exists: {internal_transfer}")
    
    # Check other required types
    required_types = {
        'EXCHANGE': 'Currency Exchange',
        'DEPOSIT': 'Cash Deposit', 
        'WITHDRAWAL': 'Cash Withdrawal',
        'EXTERNAL_TRANSFER': 'External Transfer',
        'INTERNATIONAL_TRANSFER': 'International Transfer',
        'REMITTANCE': 'Remittance',
        'ADJUSTMENT': 'Balance Adjustment'
    }
    
    created_count = 0
    
    for code, name in required_types.items():
        if not TransactionType.objects.filter(code=code).exists():
            print(f"⚠️ Creating missing transaction type: {code}")
            
            TransactionType.objects.create(
                code=code,
                name=name,
                description=f'{name} transaction',
                is_exchange=code == 'EXCHANGE',
                requires_approval=code in ['EXTERNAL_TRANSFER', 'INTERNATIONAL_TRANSFER'],
                is_active=True,
                sort_order=len(TransactionType.objects.all()) + 1
            )
            created_count += 1
    
    if created_count > 0:
        print(f"✅ Created {created_count} missing transaction types")
    else:
        print("✅ All required transaction types exist")
    
    # Final check
    print(f"\n📊 Final transaction types count: {TransactionType.objects.count()}")
    
    return True

if __name__ == "__main__":
    check_and_fix_transaction_types()
