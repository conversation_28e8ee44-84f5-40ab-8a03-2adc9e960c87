"""
Arena Doviz Automated Issue Detection and Debugging System
"""
import asyncio
import json
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

from config import TestConfig, LOGS_DIR, ERROR_PATTERNS, JS_ERROR_PATTERNS

logger = logging.getLogger("arena_issue_detector")

@dataclass
class Issue:
    """Detected issue structure"""
    id: str
    severity: str  # 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW'
    category: str  # 'FUNCTIONAL', 'PERFORMANCE', 'SECURITY', 'UI', 'API'
    title: str
    description: str
    source: str  # 'browser', 'api', 'database', 'system'
    timestamp: datetime
    affected_components: List[str]
    error_messages: List[str]
    stack_traces: List[str]
    reproduction_steps: List[str]
    suggested_fixes: List[str]
    related_logs: List[Dict[str, Any]]
    test_scenario: Optional[str] = None
    
@dataclass
class QAEntry:
    """Q&A documentation entry"""
    question: str
    answer: str
    category: str
    timestamp: datetime
    related_issues: List[str]
    test_evidence: List[str]
    
class IssueDetector:
    """Automated issue detection and debugging system"""
    
    def __init__(self):
        self.detected_issues: List[Issue] = []
        self.qa_entries: List[QAEntry] = []
        self.issue_patterns = self._load_issue_patterns()
        self.fix_suggestions = self._load_fix_suggestions()
        self.issue_counter = 0
    
    def _load_issue_patterns(self) -> Dict[str, Any]:
        """Load issue detection patterns"""
        return {
            'api_errors': {
                'patterns': [
                    r'HTTP (\d{3}) Error',
                    r'API call failed',
                    r'Connection refused',
                    r'Timeout error',
                    r'ValidationError',
                    r'IntegrityError',
                    r'DatabaseError'
                ],
                'severity': 'HIGH',
                'category': 'API'
            },
            'javascript_errors': {
                'patterns': [
                    r'Uncaught \w+Error',
                    r'TypeError.*undefined',
                    r'ReferenceError.*not defined',
                    r'Failed to fetch',
                    r'Network Error',
                    r'console\.error'
                ],
                'severity': 'MEDIUM',
                'category': 'UI'
            },
            'form_validation_errors': {
                'patterns': [
                    r'This field is required',
                    r'Invalid.*format',
                    r'Value must be.*',
                    r'Validation failed',
                    r'Form submission failed'
                ],
                'severity': 'MEDIUM',
                'category': 'FUNCTIONAL'
            },
            'database_errors': {
                'patterns': [
                    r'Database connection.*failed',
                    r'SQL.*error',
                    r'Constraint.*violation',
                    r'Deadlock detected',
                    r'Table.*doesn\'t exist'
                ],
                'severity': 'CRITICAL',
                'category': 'DATABASE'
            },
            'performance_issues': {
                'patterns': [
                    r'Response time.*exceeded',
                    r'Slow query detected',
                    r'Memory usage.*high',
                    r'CPU usage.*high',
                    r'Timeout.*occurred'
                ],
                'severity': 'MEDIUM',
                'category': 'PERFORMANCE'
            },
            'security_issues': {
                'patterns': [
                    r'Authentication.*failed',
                    r'Unauthorized access',
                    r'Permission denied',
                    r'CSRF.*error',
                    r'SQL injection.*detected'
                ],
                'severity': 'CRITICAL',
                'category': 'SECURITY'
            }
        }
    
    def _load_fix_suggestions(self) -> Dict[str, List[str]]:
        """Load automated fix suggestions"""
        return {
            'commission_amount_validation': [
                'Ensure commission_amount field is properly validated as a decimal number',
                'Add null/empty string handling for commission_amount in form submission',
                'Implement client-side validation for numeric fields',
                'Check commission calculation function for proper return types'
            ],
            'customer_dropdown_loading': [
                'Verify customer API endpoint is accessible and returns proper data',
                'Check authentication headers in AJAX requests',
                'Implement proper error handling for dropdown loading failures',
                'Add loading indicators for better user experience'
            ],
            'exchange_rate_api_error': [
                'Check database for exchange rate data availability',
                'Verify exchange rate API endpoint implementation',
                'Ensure proper currency and location parameter validation',
                'Check for database query optimization issues'
            ],
            'balance_calculation_error': [
                'Verify customer balance API returns correct field names',
                'Check for proper decimal precision in balance calculations',
                'Ensure transaction amount validation is working correctly',
                'Verify double-entry bookkeeping implementation'
            ],
            'form_submission_error': [
                'Check all required fields are properly validated',
                'Verify CSRF token is included in form submissions',
                'Ensure proper error message display for validation failures',
                'Check for JavaScript form validation conflicts'
            ],
            'api_authentication_error': [
                'Verify JWT token is properly included in API requests',
                'Check token expiration and refresh mechanism',
                'Ensure proper CORS configuration for API endpoints',
                'Verify user permissions for requested operations'
            ]
        }
    
    async def analyze_logs(self, log_entries: List[Dict[str, Any]]) -> List[Issue]:
        """Analyze log entries and detect issues"""
        logger.info("🔍 Analyzing logs for issues...")
        
        new_issues = []
        
        for log_entry in log_entries:
            issues = await self._analyze_single_log_entry(log_entry)
            new_issues.extend(issues)
        
        # Group similar issues
        grouped_issues = self._group_similar_issues(new_issues)
        
        # Add to detected issues
        self.detected_issues.extend(grouped_issues)
        
        logger.info(f"🔍 Detected {len(grouped_issues)} new issues from log analysis")
        return grouped_issues
    
    async def _analyze_single_log_entry(self, log_entry: Dict[str, Any]) -> List[Issue]:
        """Analyze a single log entry for issues"""
        issues = []
        message = log_entry.get('message', '')
        level = log_entry.get('level', 'INFO')
        source = log_entry.get('source', 'unknown')
        timestamp = datetime.fromisoformat(log_entry.get('timestamp', datetime.now().isoformat()))
        
        # Check against known patterns
        for issue_type, config in self.issue_patterns.items():
            for pattern in config['patterns']:
                if re.search(pattern, message, re.IGNORECASE):
                    issue = await self._create_issue_from_pattern(
                        issue_type, pattern, message, level, source, timestamp, log_entry
                    )
                    if issue:
                        issues.append(issue)
        
        return issues
    
    async def _create_issue_from_pattern(self, issue_type: str, pattern: str, message: str, 
                                       level: str, source: str, timestamp: datetime, 
                                       log_entry: Dict[str, Any]) -> Optional[Issue]:
        """Create an issue from a detected pattern"""
        try:
            self.issue_counter += 1
            issue_id = f"ISSUE_{self.issue_counter:04d}"
            
            config = self.issue_patterns[issue_type]
            
            # Determine affected components
            affected_components = self._identify_affected_components(message, source)
            
            # Generate reproduction steps
            reproduction_steps = self._generate_reproduction_steps(issue_type, message, log_entry)
            
            # Get suggested fixes
            suggested_fixes = self._get_suggested_fixes(issue_type, message)
            
            # Create issue
            issue = Issue(
                id=issue_id,
                severity=config['severity'],
                category=config['category'],
                title=self._generate_issue_title(issue_type, message),
                description=self._generate_issue_description(issue_type, message, log_entry),
                source=source,
                timestamp=timestamp,
                affected_components=affected_components,
                error_messages=[message],
                stack_traces=self._extract_stack_traces(log_entry),
                reproduction_steps=reproduction_steps,
                suggested_fixes=suggested_fixes,
                related_logs=[log_entry]
            )
            
            return issue
            
        except Exception as e:
            logger.error(f"Error creating issue from pattern: {str(e)}")
            return None
    
    def _identify_affected_components(self, message: str, source: str) -> List[str]:
        """Identify affected system components"""
        components = []
        
        # Component detection patterns
        component_patterns = {
            'Customer Management': [r'customer', r'client', r'user'],
            'Transaction Processing': [r'transaction', r'transfer', r'exchange', r'deposit', r'withdrawal'],
            'Currency Exchange': [r'exchange.*rate', r'currency', r'conversion'],
            'Balance Management': [r'balance', r'account', r'funds'],
            'Commission Calculation': [r'commission', r'fee', r'charge'],
            'Authentication': [r'auth', r'login', r'token', r'permission'],
            'API Layer': [r'api', r'endpoint', r'request', r'response'],
            'Database': [r'database', r'sql', r'query', r'table'],
            'User Interface': [r'form', r'dropdown', r'button', r'field'],
            'Reporting': [r'report', r'statement', r'export']
        }
        
        for component, patterns in component_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    components.append(component)
                    break
        
        # Add source-based component
        if source == 'javascript':
            components.append('User Interface')
        elif source == 'api':
            components.append('API Layer')
        elif source == 'database':
            components.append('Database')
        
        return list(set(components))  # Remove duplicates
    
    def _generate_reproduction_steps(self, issue_type: str, message: str, log_entry: Dict[str, Any]) -> List[str]:
        """Generate reproduction steps for the issue"""
        steps = []
        
        # Common steps based on issue type
        if 'api' in issue_type.lower():
            steps.extend([
                "1. Navigate to the affected page/form",
                "2. Fill out the form with test data",
                "3. Submit the form and observe the API call",
                "4. Check browser console and network tab for errors"
            ])
        elif 'javascript' in issue_type.lower():
            steps.extend([
                "1. Open browser developer tools",
                "2. Navigate to the affected page",
                "3. Perform the action that triggers the error",
                "4. Check console for JavaScript errors"
            ])
        elif 'form' in issue_type.lower():
            steps.extend([
                "1. Navigate to the form page",
                "2. Try to submit the form with invalid/missing data",
                "3. Observe validation error messages",
                "4. Check if form submission is blocked appropriately"
            ])
        elif 'database' in issue_type.lower():
            steps.extend([
                "1. Perform the action that triggers database interaction",
                "2. Check application logs for database errors",
                "3. Verify database connection and query execution",
                "4. Check for data integrity issues"
            ])
        
        # Add specific context from log entry
        if 'url' in log_entry:
            steps.append(f"5. Specific URL involved: {log_entry['url']}")
        
        return steps
    
    def _get_suggested_fixes(self, issue_type: str, message: str) -> List[str]:
        """Get suggested fixes for the issue"""
        fixes = []
        
        # Check for specific fix patterns
        for fix_key, fix_list in self.fix_suggestions.items():
            if fix_key.replace('_', ' ').lower() in message.lower() or fix_key in issue_type.lower():
                fixes.extend(fix_list)
        
        # Generic fixes based on issue type
        if 'api' in issue_type.lower():
            fixes.extend([
                'Check API endpoint availability and response format',
                'Verify authentication and authorization',
                'Review API error handling and status codes'
            ])
        elif 'javascript' in issue_type.lower():
            fixes.extend([
                'Review JavaScript code for syntax errors',
                'Check for undefined variables or functions',
                'Verify proper event handling and DOM manipulation'
            ])
        elif 'database' in issue_type.lower():
            fixes.extend([
                'Check database connection configuration',
                'Review SQL queries for syntax and performance',
                'Verify database schema and constraints'
            ])
        
        return list(set(fixes))  # Remove duplicates
    
    def _generate_issue_title(self, issue_type: str, message: str) -> str:
        """Generate a descriptive title for the issue"""
        # Extract key information from message
        if 'commission_amount' in message.lower():
            return "Commission Amount Validation Error"
        elif 'customer' in message.lower() and 'dropdown' in message.lower():
            return "Customer Dropdown Loading Issue"
        elif 'exchange rate' in message.lower():
            return "Exchange Rate API Error"
        elif 'balance' in message.lower():
            return "Balance Calculation Error"
        elif 'form' in message.lower() and 'validation' in message.lower():
            return "Form Validation Error"
        elif 'authentication' in message.lower():
            return "Authentication Error"
        else:
            # Generic title based on issue type
            return f"{issue_type.replace('_', ' ').title()} Issue"
    
    def _generate_issue_description(self, issue_type: str, message: str, log_entry: Dict[str, Any]) -> str:
        """Generate a detailed description for the issue"""
        description = f"Issue detected in {log_entry.get('source', 'unknown')} component.\n\n"
        description += f"Error Message: {message}\n\n"
        
        if 'context' in log_entry:
            context = log_entry['context']
            description += "Additional Context:\n"
            for key, value in context.items():
                description += f"- {key}: {value}\n"
        
        description += f"\nTimestamp: {log_entry.get('timestamp', 'unknown')}"
        description += f"\nLog Level: {log_entry.get('level', 'unknown')}"
        
        return description
    
    def _extract_stack_traces(self, log_entry: Dict[str, Any]) -> List[str]:
        """Extract stack traces from log entry"""
        stack_traces = []
        
        if 'stack_trace' in log_entry:
            stack_traces.append(log_entry['stack_trace'])
        
        # Look for stack traces in message
        message = log_entry.get('message', '')
        if 'Traceback' in message or 'at ' in message:
            stack_traces.append(message)
        
        return stack_traces
    
    def _group_similar_issues(self, issues: List[Issue]) -> List[Issue]:
        """Group similar issues together"""
        grouped = []
        processed = set()
        
        for i, issue in enumerate(issues):
            if i in processed:
                continue
            
            # Find similar issues
            similar_indices = [i]
            for j, other_issue in enumerate(issues[i+1:], i+1):
                if j in processed:
                    continue
                
                if self._are_issues_similar(issue, other_issue):
                    similar_indices.append(j)
                    processed.add(j)
            
            # Merge similar issues
            if len(similar_indices) > 1:
                merged_issue = self._merge_issues([issues[idx] for idx in similar_indices])
                grouped.append(merged_issue)
            else:
                grouped.append(issue)
            
            processed.add(i)
        
        return grouped
    
    def _are_issues_similar(self, issue1: Issue, issue2: Issue) -> bool:
        """Check if two issues are similar"""
        # Same category and severity
        if issue1.category != issue2.category:
            return False
        
        # Similar titles (70% similarity)
        title_similarity = self._calculate_similarity(issue1.title, issue2.title)
        if title_similarity < 0.7:
            return False
        
        # Similar error messages
        for msg1 in issue1.error_messages:
            for msg2 in issue2.error_messages:
                if self._calculate_similarity(msg1, msg2) > 0.8:
                    return True
        
        return False
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts"""
        # Simple word-based similarity
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def _merge_issues(self, issues: List[Issue]) -> Issue:
        """Merge multiple similar issues into one"""
        base_issue = issues[0]
        
        # Combine error messages
        all_error_messages = []
        for issue in issues:
            all_error_messages.extend(issue.error_messages)
        
        # Combine related logs
        all_related_logs = []
        for issue in issues:
            all_related_logs.extend(issue.related_logs)
        
        # Combine affected components
        all_components = []
        for issue in issues:
            all_components.extend(issue.affected_components)
        
        # Update base issue
        base_issue.error_messages = list(set(all_error_messages))
        base_issue.related_logs = all_related_logs
        base_issue.affected_components = list(set(all_components))
        base_issue.description += f"\n\nThis issue occurred {len(issues)} times with similar patterns."
        
        return base_issue
    
    async def generate_qa_documentation(self) -> List[QAEntry]:
        """Generate Q&A documentation from detected issues"""
        logger.info("📝 Generating Q&A documentation...")
        
        qa_entries = []
        
        for issue in self.detected_issues:
            qa_entry = await self._create_qa_entry_from_issue(issue)
            if qa_entry:
                qa_entries.append(qa_entry)
        
        self.qa_entries.extend(qa_entries)
        
        logger.info(f"📝 Generated {len(qa_entries)} Q&A entries")
        return qa_entries
    
    async def _create_qa_entry_from_issue(self, issue: Issue) -> Optional[QAEntry]:
        """Create a Q&A entry from an issue"""
        try:
            # Generate question
            question = f"What causes the {issue.title.lower()}?"
            
            # Generate answer
            answer = f"This issue occurs when {issue.description}\n\n"
            answer += "Root Causes:\n"
            for i, msg in enumerate(issue.error_messages[:3], 1):  # Limit to 3 messages
                answer += f"{i}. {msg}\n"
            
            answer += "\nSuggested Solutions:\n"
            for i, fix in enumerate(issue.suggested_fixes[:5], 1):  # Limit to 5 fixes
                answer += f"{i}. {fix}\n"
            
            answer += f"\nAffected Components: {', '.join(issue.affected_components)}"
            answer += f"\nSeverity: {issue.severity}"
            
            qa_entry = QAEntry(
                question=question,
                answer=answer,
                category=issue.category,
                timestamp=datetime.now(),
                related_issues=[issue.id],
                test_evidence=[f"Issue detected at {issue.timestamp}"]
            )
            
            return qa_entry
            
        except Exception as e:
            logger.error(f"Error creating Q&A entry: {str(e)}")
            return None
    
    def get_issues_by_severity(self, severity: str) -> List[Issue]:
        """Get issues by severity level"""
        return [issue for issue in self.detected_issues if issue.severity == severity]
    
    def get_issues_by_category(self, category: str) -> List[Issue]:
        """Get issues by category"""
        return [issue for issue in self.detected_issues if issue.category == category]
    
    def export_issues_report(self, filepath: Path) -> None:
        """Export issues report to JSON file"""
        report = {
            'export_timestamp': datetime.now().isoformat(),
            'total_issues': len(self.detected_issues),
            'issues_by_severity': {
                'CRITICAL': len(self.get_issues_by_severity('CRITICAL')),
                'HIGH': len(self.get_issues_by_severity('HIGH')),
                'MEDIUM': len(self.get_issues_by_severity('MEDIUM')),
                'LOW': len(self.get_issues_by_severity('LOW'))
            },
            'issues_by_category': {
                'FUNCTIONAL': len(self.get_issues_by_category('FUNCTIONAL')),
                'PERFORMANCE': len(self.get_issues_by_category('PERFORMANCE')),
                'SECURITY': len(self.get_issues_by_category('SECURITY')),
                'UI': len(self.get_issues_by_category('UI')),
                'API': len(self.get_issues_by_category('API'))
            },
            'detected_issues': [asdict(issue) for issue in self.detected_issues],
            'qa_documentation': [asdict(qa) for qa in self.qa_entries]
        }
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📊 Issues report exported to {filepath}")
