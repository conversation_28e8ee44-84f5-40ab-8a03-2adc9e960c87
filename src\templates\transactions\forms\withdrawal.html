{% extends 'transactions/base_form.html' %}
{% load i18n %}

{% block transaction_specific_fields %}
<!-- Cash Withdrawal Details Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-cash-stack"></i>
            {% trans "Cash Withdrawal Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Withdrawal Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.000001" required placeholder="0.00">
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="commission_amount" class="form-label">
                        {% trans "Withdrawal Fee" %}
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="calculateWithdrawalCommission()">
                            <i class="bi bi-calculator"></i> {% trans "Auto Calculate" %}
                        </button>
                    </label>
                    <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.000001" placeholder="0.00">
                    <div class="form-text" id="commission_info"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="available_balance" class="form-label">{% trans "Available Balance" %}</label>
                    <input type="text" class="form-control" id="available_balance" name="available_balance" readonly placeholder="{% trans 'Loading...' %}">
                    <div class="form-text">{% trans "Customer's current balance in selected currency" %}</div>
                </div>
            </div>
        </div>
        
        <!-- Balance Check Alert -->
        <div class="alert alert-warning" id="insufficient-balance-alert" style="display: none;">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>{% trans "Warning:" %}</strong> {% trans "Withdrawal amount exceeds available balance." %}
        </div>
    </div>
</div>

<!-- Authorization Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-person-check"></i>
            {% trans "Authorization & Verification" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="authorized_by" class="form-label">{% trans "Authorized By" %}</label>
                    <input type="text" class="form-control" id="authorized_by" name="authorized_by" placeholder="{% trans 'Staff member name...' %}" readonly>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="withdrawal_method" class="form-label">{% trans "Withdrawal Method" %}</label>
                    <select class="form-select" id="withdrawal_method" name="withdrawal_method">
                        <option value="cash">{% trans "Cash" %}</option>
                        <option value="bank_transfer">{% trans "Bank Transfer" %}</option>
                        <option value="check">{% trans "Check" %}</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="delivery_method" class="form-label">{% trans "Delivery Method" %}</label>
                    <select class="form-select" id="delivery_method" name="delivery_method">
                        <option value="in_person">{% trans "In Person" %}</option>
                        <option value="courier">{% trans "Courier" %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3" id="courier-field" style="display: none;">
                    <label for="courier" class="form-label">{% trans "Courier" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="courier" name="courier">
                        <option value="">{% trans "Select courier..." %}</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="mb-3" id="bank-transfer-details" style="display: none;">
            <label for="bank_account" class="form-label">{% trans "Bank Account Details" %}</label>
            <textarea class="form-control" id="bank_account" name="bank_account" rows="3" placeholder="{% trans 'Bank name, account number, routing details...' %}"></textarea>
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="customer_id_verified" name="customer_id_verified" required>
                <label class="form-check-label" for="customer_id_verified">
                    {% trans "Customer identification has been verified" %} <span class="text-danger">*</span>
                </label>
            </div>
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="signature_verified" name="signature_verified" required>
                <label class="form-check-label" for="signature_verified">
                    {% trans "Customer signature has been verified" %} <span class="text-danger">*</span>
                </label>
            </div>
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="cash_dispensed" name="cash_dispensed">
                <label class="form-check-label" for="cash_dispensed">
                    {% trans "Cash has been dispensed to customer" %}
                </label>
            </div>
        </div>
    </div>
</div>

<!-- Important Notice -->
<div class="alert alert-danger">
    <i class="bi bi-exclamation-triangle"></i>
    <strong>{% trans "Important:" %}</strong> {% trans "This withdrawal will be deducted from the customer's account balance immediately upon approval. Please ensure all verification steps are completed." %}
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="/static/js/transactions/withdrawal.js"></script>
{% endblock %}
