#!/usr/bin/env python
"""
Comprehensive testing script for Arena Doviz system fixes.
This script validates all the fixes implemented for the reported issues.
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Setup Django environment
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arena_doviz.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from apps.customers.models import Customer
from apps.transactions.models import Transaction, TransactionType
from apps.currencies.models import Currency, ExchangeRate
from apps.locations.models import Location

User = get_user_model()


class ArenaDoviFixesTestSuite:
    """Comprehensive test suite for all Arena Doviz fixes."""
    
    def __init__(self):
        self.client = Client()
        self.base_url = 'http://localhost:8000'
        self.test_results = []
        
    def log_test(self, test_name, status, message=""):
        """Log test result."""
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_symbol = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_symbol} {test_name}: {message}")
    
    def test_authentication_fixes(self):
        """Test authentication and token handling fixes."""
        print("\n🔐 Testing Authentication Fixes...")
        
        try:
            # Test JWT token endpoint
            response = self.client.post('/api/v1/accounts/jwt/token/', {
                'username': 'admin',
                'password': 'Arena2024!'
            })
            
            if response.status_code == 200:
                self.log_test("JWT Token Generation", "PASS", "JWT tokens working correctly")
            else:
                self.log_test("JWT Token Generation", "FAIL", f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("JWT Token Generation", "FAIL", str(e))
    
    def test_api_endpoints(self):
        """Test API endpoint fixes."""
        print("\n🔗 Testing API Endpoint Fixes...")
        
        # Create test user and login
        try:
            user = User.objects.get(username='admin')
            self.client.force_login(user)
            
            # Test customer stats endpoint
            customer = Customer.objects.first()
            if customer:
                response = self.client.get(f'/api/v1/customers/customers/{customer.id}/stats/')
                if response.status_code == 200:
                    self.log_test("Customer Stats API", "PASS", "Individual customer stats endpoint working")
                else:
                    self.log_test("Customer Stats API", "FAIL", f"Status: {response.status_code}")
            else:
                self.log_test("Customer Stats API", "SKIP", "No customers found for testing")
            
            # Test reports generation API
            response = self.client.post('/api/v1/reports/generated/generate/', {
                'report_type': 'balance_summary',
                'format': 'pdf',
                'parameters': {
                    'date_from': '2024-01-01',
                    'date_to': '2024-12-31'
                }
            }, content_type='application/json')
            
            if response.status_code in [200, 201]:
                self.log_test("Reports Generation API", "PASS", "Reports API working correctly")
            else:
                self.log_test("Reports Generation API", "FAIL", f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("API Endpoints", "FAIL", str(e))
    
    def test_frontend_fixes(self):
        """Test frontend JavaScript fixes."""
        print("\n🖥️ Testing Frontend Fixes...")
        
        try:
            # Test transaction add page
            response = self.client.get('/transactions/add/')
            if response.status_code == 200:
                content = response.content.decode()
                if 'getAuthToken' in content:
                    self.log_test("Transaction Add Page", "PASS", "getAuthToken function present")
                else:
                    self.log_test("Transaction Add Page", "FAIL", "getAuthToken function missing")
            else:
                self.log_test("Transaction Add Page", "FAIL", f"Status: {response.status_code}")
            
            # Test customer balance page
            customer = Customer.objects.first()
            if customer:
                response = self.client.get(f'/customers/{customer.id}/balance/')
                if response.status_code == 200:
                    self.log_test("Customer Balance Page", "PASS", "Customer balance page accessible")
                else:
                    self.log_test("Customer Balance Page", "FAIL", f"Status: {response.status_code}")
            
            # Test customer edit page
            if customer:
                response = self.client.get(f'/customers/{customer.id}/edit/')
                if response.status_code == 200:
                    self.log_test("Customer Edit Page", "PASS", "Customer edit page accessible")
                else:
                    self.log_test("Customer Edit Page", "FAIL", f"Status: {response.status_code}")
                    
        except Exception as e:
            self.log_test("Frontend Fixes", "FAIL", str(e))
    
    def test_exchange_rate_system(self):
        """Test exchange rate system fixes."""
        print("\n💱 Testing Exchange Rate System...")
        
        try:
            # Test exchange rates API
            response = self.client.get('/api/v1/currencies/rates/')
            if response.status_code == 200:
                data = response.json()
                if 'results' in data:
                    self.log_test("Exchange Rates API", "PASS", f"Found {len(data['results'])} rates")
                else:
                    self.log_test("Exchange Rates API", "FAIL", "No results in response")
            else:
                self.log_test("Exchange Rates API", "FAIL", f"Status: {response.status_code}")
            
            # Test pending approval endpoint
            response = self.client.get('/api/v1/currencies/rates/pending_approval/')
            if response.status_code == 200:
                self.log_test("Pending Rates API", "PASS", "Pending approval endpoint working")
            else:
                self.log_test("Pending Rates API", "FAIL", f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Exchange Rate System", "FAIL", str(e))
    
    def test_user_roles(self):
        """Test user role management system."""
        print("\n👥 Testing User Role Management...")
        
        try:
            # Test each role exists
            roles = ['admin', 'accountant', 'branch_employee', 'viewer', 'courier']
            for role in roles:
                user = User.objects.filter(role=role).first()
                if user:
                    self.log_test(f"Role {role}", "PASS", f"User {user.username} has {role} role")
                else:
                    self.log_test(f"Role {role}", "FAIL", f"No user found with {role} role")
            
            # Test role permissions
            admin_user = User.objects.filter(role='admin').first()
            if admin_user:
                if admin_user.can_manage_users():
                    self.log_test("Admin Permissions", "PASS", "Admin can manage users")
                else:
                    self.log_test("Admin Permissions", "FAIL", "Admin cannot manage users")
            
            viewer_user = User.objects.filter(role='viewer').first()
            if viewer_user:
                if not viewer_user.can_manage_users():
                    self.log_test("Viewer Permissions", "PASS", "Viewer cannot manage users")
                else:
                    self.log_test("Viewer Permissions", "FAIL", "Viewer can manage users")
                    
        except Exception as e:
            self.log_test("User Role Management", "FAIL", str(e))
    
    def test_ui_design_consistency(self):
        """Test UI/UX design consistency."""
        print("\n🎨 Testing UI Design Consistency...")
        
        try:
            # Test login page design
            response = self.client.get('/accounts/login/')
            if response.status_code == 200:
                content = response.content.decode()
                if '#000d28' in content:  # Primary color
                    self.log_test("Login Page Design", "PASS", "Primary color scheme applied")
                else:
                    self.log_test("Login Page Design", "FAIL", "Primary color scheme missing")
                
                if 'border-radius' not in content:
                    self.log_test("Sharp Design", "PASS", "No rounded corners found")
                else:
                    self.log_test("Sharp Design", "WARN", "Some rounded corners may still exist")
            else:
                self.log_test("Login Page Design", "FAIL", f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("UI Design Consistency", "FAIL", str(e))
    
    def test_transaction_management(self):
        """Test transaction management fixes."""
        print("\n💳 Testing Transaction Management...")
        
        try:
            # Test transaction types exist
            transaction_types = TransactionType.objects.count()
            if transaction_types > 0:
                self.log_test("Transaction Types", "PASS", f"Found {transaction_types} transaction types")
            else:
                self.log_test("Transaction Types", "FAIL", "No transaction types found")
            
            # Test transaction detail page
            transaction = Transaction.objects.first()
            if transaction:
                response = self.client.get(f'/transactions/{transaction.id}/')
                if response.status_code == 200:
                    content = response.content.decode()
                    if 'timeline' in content:
                        self.log_test("Transaction History", "PASS", "Transaction history timeline implemented")
                    else:
                        self.log_test("Transaction History", "FAIL", "Transaction history timeline missing")
                else:
                    self.log_test("Transaction Detail Page", "FAIL", f"Status: {response.status_code}")
            else:
                self.log_test("Transaction Management", "SKIP", "No transactions found for testing")
                
        except Exception as e:
            self.log_test("Transaction Management", "FAIL", str(e))
    
    def run_all_tests(self):
        """Run all test suites."""
        print("🚀 Starting Arena Doviz Comprehensive Test Suite...")
        print("=" * 60)
        
        self.test_authentication_fixes()
        self.test_api_endpoints()
        self.test_frontend_fixes()
        self.test_exchange_rate_system()
        self.test_user_roles()
        self.test_ui_design_consistency()
        self.test_transaction_management()
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate test summary report."""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        skipped_tests = len([r for r in self.test_results if r['status'] == 'SKIP'])
        warnings = len([r for r in self.test_results if r['status'] == 'WARN'])
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⏭️ Skipped: {skipped_tests}")
        print(f"⚠️ Warnings: {warnings}")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  • {result['test']}: {result['message']}")
        
        # Save detailed report
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        if success_rate >= 80:
            print("\n🎉 Arena Doviz system fixes are working well!")
        else:
            print("\n⚠️ Some issues need attention. Please review failed tests.")


if __name__ == '__main__':
    test_suite = ArenaDoviFixesTestSuite()
    test_suite.run_all_tests()
