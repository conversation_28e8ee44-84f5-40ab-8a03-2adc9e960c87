#!/bin/bash
# Arena Doviz Complete Database Backup Script

set -euo pipefail

# Configuration from environment or defaults
DB_NAME="${DB_NAME:-arena_doviz_prod}"
DB_USER="${DB_USER:-arena_user}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
BACKUP_DIR="${BACKUP_DIR:-/var/backups/arena-doviz}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
ENCRYPTION_KEY="${ARENA_ENCRYPTION_KEY:-}"
LOG_FILE="/var/log/arena-doviz/backup.log"

# Create timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DATE=$(date +%Y-%m-%d)
BACKUP_FILE="$BACKUP_DIR/arena_doviz_backup_$TIMESTAMP.sql"
COMPRESSED_FILE="$BACKUP_FILE.gz"
ENCRYPTED_FILE="$COMPRESSED_FILE.enc"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Cleanup function
cleanup() {
    if [[ -f "$BACKUP_FILE" ]]; then
        rm -f "$BACKUP_FILE"
    fi
    if [[ -f "$COMPRESSED_FILE" && -f "$ENCRYPTED_FILE" ]]; then
        rm -f "$COMPRESSED_FILE"
    fi
}

trap cleanup EXIT

# Start backup process
log "Starting Arena Doviz database backup"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR" || error_exit "Failed to create backup directory"
mkdir -p "$(dirname "$LOG_FILE")" || error_exit "Failed to create log directory"

# Check if PostgreSQL is running
if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
    error_exit "PostgreSQL is not ready or database is not accessible"
fi

log "PostgreSQL connection verified"

# Create database dump
log "Creating database dump..."
if ! pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
    --verbose \
    --no-password \
    --format=custom \
    --compress=9 \
    --file="$BACKUP_FILE" 2>>"$LOG_FILE"; then
    error_exit "Database dump failed"
fi

log "Database dump created: $BACKUP_FILE"

# Verify dump file
if [[ ! -f "$BACKUP_FILE" || ! -s "$BACKUP_FILE" ]]; then
    error_exit "Backup file is empty or doesn't exist"
fi

BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
log "Backup file size: $BACKUP_SIZE"

# Compress the backup
log "Compressing backup..."
if ! gzip -9 "$BACKUP_FILE"; then
    error_exit "Compression failed"
fi

log "Backup compressed: $COMPRESSED_FILE"

# Encrypt the backup if encryption key is provided
if [[ -n "$ENCRYPTION_KEY" ]]; then
    log "Encrypting backup..."
    if ! openssl enc -aes-256-cbc -salt -in "$COMPRESSED_FILE" -out "$ENCRYPTED_FILE" -k "$ENCRYPTION_KEY"; then
        error_exit "Encryption failed"
    fi
    
    # Remove unencrypted file
    rm -f "$COMPRESSED_FILE"
    FINAL_BACKUP="$ENCRYPTED_FILE"
    log "Backup encrypted: $ENCRYPTED_FILE"
else
    FINAL_BACKUP="$COMPRESSED_FILE"
    log "Warning: Backup not encrypted (no encryption key provided)"
fi

# Verify final backup
FINAL_SIZE=$(du -h "$FINAL_BACKUP" | cut -f1)
log "Final backup size: $FINAL_SIZE"

# Create backup metadata
METADATA_FILE="$BACKUP_DIR/backup_metadata_$TIMESTAMP.json"
cat > "$METADATA_FILE" << EOF
{
    "timestamp": "$TIMESTAMP",
    "date": "$DATE",
    "database": "$DB_NAME",
    "host": "$DB_HOST",
    "port": "$DB_PORT",
    "user": "$DB_USER",
    "backup_file": "$(basename "$FINAL_BACKUP")",
    "size": "$FINAL_SIZE",
    "encrypted": $([ -n "$ENCRYPTION_KEY" ] && echo "true" || echo "false"),
    "compression": "gzip",
    "format": "custom",
    "pg_dump_version": "$(pg_dump --version | head -n1)",
    "server_version": "$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c 'SELECT version();' | xargs)"
}
EOF

log "Backup metadata created: $METADATA_FILE"

# Create symbolic link to latest backup
LATEST_LINK="$BACKUP_DIR/latest_backup"
ln -sf "$(basename "$FINAL_BACKUP")" "$LATEST_LINK"
ln -sf "$(basename "$METADATA_FILE")" "$BACKUP_DIR/latest_metadata.json"

log "Latest backup symlinks updated"

# Clean up old backups
log "Cleaning up old backups (retention: $BACKUP_RETENTION_DAYS days)..."
find "$BACKUP_DIR" -name "arena_doviz_backup_*.sql.gz*" -mtime +$BACKUP_RETENTION_DAYS -delete
find "$BACKUP_DIR" -name "backup_metadata_*.json" -mtime +$BACKUP_RETENTION_DAYS -delete

# Count remaining backups
BACKUP_COUNT=$(find "$BACKUP_DIR" -name "arena_doviz_backup_*.sql.gz*" | wc -l)
log "Cleanup complete. $BACKUP_COUNT backups remaining"

# Test backup integrity (for unencrypted backups)
if [[ -z "$ENCRYPTION_KEY" ]]; then
    log "Testing backup integrity..."
    if pg_restore --list "$FINAL_BACKUP" >/dev/null 2>&1; then
        log "Backup integrity test passed"
    else
        log "Warning: Backup integrity test failed"
    fi
fi

# Send notification (if configured)
if command -v mail >/dev/null 2>&1 && [[ -n "${ALERT_EMAIL:-}" ]]; then
    echo "Arena Doviz database backup completed successfully.
    
Backup Details:
- Timestamp: $TIMESTAMP
- Database: $DB_NAME
- Size: $FINAL_SIZE
- Location: $FINAL_BACKUP
- Encrypted: $([ -n "$ENCRYPTION_KEY" ] && echo "Yes" || echo "No")
- Retention: $BACKUP_RETENTION_DAYS days
- Backups remaining: $BACKUP_COUNT

Log file: $LOG_FILE" | mail -s "Arena Doviz Backup Success - $DATE" "$ALERT_EMAIL"
fi

# Update backup status file
STATUS_FILE="$BACKUP_DIR/backup_status.json"
cat > "$STATUS_FILE" << EOF
{
    "last_backup": "$TIMESTAMP",
    "last_backup_file": "$(basename "$FINAL_BACKUP")",
    "last_backup_size": "$FINAL_SIZE",
    "status": "success",
    "total_backups": $BACKUP_COUNT,
    "next_cleanup": "$(date -d "+$BACKUP_RETENTION_DAYS days" +%Y-%m-%d)"
}
EOF

log "Backup completed successfully"
log "Backup file: $FINAL_BACKUP"
log "Metadata: $METADATA_FILE"
log "Status: $STATUS_FILE"

exit 0
