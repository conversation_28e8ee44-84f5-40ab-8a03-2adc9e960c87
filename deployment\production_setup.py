#!/usr/bin/env python
"""
Production setup script for Arena Doviz Exchange Accounting System.
Handles encryption key generation, environment setup, and deployment validation.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
from cryptography.fernet import <PERSON><PERSON><PERSON>


def generate_encryption_key():
    """Generate a new encryption key for Arena Doviz."""
    key = Fernet.generate_key().decode()
    print(f"Generated new encryption key: {key}")
    return key


def set_environment_variable(key, value, permanent=True):
    """Set environment variable based on the operating system."""
    system = platform.system().lower()
    
    if system == "windows":
        if permanent:
            # Set permanent environment variable on Windows
            subprocess.run(['setx', key, value], check=True)
            print(f"✓ Set permanent environment variable {key} on Windows")
        else:
            os.environ[key] = value
            print(f"✓ Set temporary environment variable {key}")
    else:
        # Linux/macOS
        os.environ[key] = value
        if permanent:
            # Add to shell profile
            shell_profile = Path.home() / '.bashrc'
            if not shell_profile.exists():
                shell_profile = Path.home() / '.bash_profile'
            
            export_line = f'export {key}="{value}"\n'
            
            # Check if already exists
            if shell_profile.exists():
                with open(shell_profile, 'r') as f:
                    content = f.read()
                if f'export {key}=' not in content:
                    with open(shell_profile, 'a') as f:
                        f.write(export_line)
                    print(f"✓ Added {key} to {shell_profile}")
                else:
                    print(f"✓ {key} already exists in {shell_profile}")
            else:
                with open(shell_profile, 'w') as f:
                    f.write(export_line)
                print(f"✓ Created {shell_profile} with {key}")


def create_env_file(encryption_key):
    """Create .env file for development/testing."""
    env_file = Path('.env')
    env_content = f"""# Arena Doviz Environment Configuration
# Generated on {os.popen('date').read().strip()}

# Security
SECRET_KEY=django-insecure-change-me-in-production-{os.urandom(16).hex()}
ARENA_ENCRYPTION_KEY={encryption_key}

# Database
DATABASE_URL=sqlite:///db.sqlite3

# Debug (set to False in production)
DEBUG=False

# Allowed hosts (comma-separated)
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Email configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# HTTPS settings (set to true when using HTTPS)
ARENA_USE_HTTPS=false

# WhatsApp integration
WHATSAPP_INTEGRATION=desktop
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print(f"✓ Created {env_file} with configuration")


def validate_encryption_setup():
    """Validate that encryption is working properly."""
    try:
        # Add src to path
        src_path = Path(__file__).parent.parent / 'src'
        sys.path.insert(0, str(src_path))
        
        # Set Django settings
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')
        
        import django
        django.setup()
        
        from apps.core.encryption import encrypt_data, decrypt_data
        
        # Test encryption/decryption
        test_data = "Arena Doviz Test Data 2025"
        encrypted = encrypt_data(test_data)
        decrypted = decrypt_data(encrypted)
        
        if decrypted == test_data:
            print("✓ Encryption validation successful")
            return True
        else:
            print("✗ Encryption validation failed - decrypted data doesn't match")
            return False
            
    except Exception as e:
        print(f"✗ Encryption validation failed: {e}")
        return False


def main():
    """Main setup function."""
    print("Arena Doviz Production Setup")
    print("=" * 40)
    
    # Check if encryption key already exists
    existing_key = os.environ.get('ARENA_ENCRYPTION_KEY')
    
    if existing_key:
        print(f"✓ Found existing encryption key: {existing_key[:20]}...")
        use_existing = input("Use existing key? (y/n): ").lower().strip()
        
        if use_existing == 'y':
            encryption_key = existing_key
        else:
            encryption_key = generate_encryption_key()
    else:
        print("No existing encryption key found.")
        encryption_key = generate_encryption_key()
    
    # Setup options
    print("\nSetup Options:")
    print("1. Set permanent system environment variable")
    print("2. Create .env file for development")
    print("3. Both")
    
    choice = input("Choose option (1-3): ").strip()
    
    if choice in ['1', '3']:
        try:
            set_environment_variable('ARENA_ENCRYPTION_KEY', encryption_key, permanent=True)
        except Exception as e:
            print(f"✗ Failed to set environment variable: {e}")
            print("You may need to run as administrator/sudo")
    
    if choice in ['2', '3']:
        create_env_file(encryption_key)
    
    # Validate setup
    print("\nValidating encryption setup...")
    if validate_encryption_setup():
        print("\n✓ Production setup completed successfully!")
        print("\nNext steps:")
        print("1. Restart your terminal/command prompt")
        print("2. Run: python run_production.py")
        print("3. Access the application at http://localhost:8000")
    else:
        print("\n✗ Setup validation failed. Please check the configuration.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
