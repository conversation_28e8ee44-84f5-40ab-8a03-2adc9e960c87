{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Add Customer" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/css/intlTelInput.css">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-plus"></i>
                        {% trans "Add New Customer" %}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'core_web:dashboard' %}">{% trans "Dashboard" %}</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'customers_web:list' %}">{% trans "Customers" %}</a></li>
                            <li class="breadcrumb-item active">{% trans "Add Customer" %}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'customers_web:list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> {% trans "Back to Customers" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Customer Information" %}</h5>
                </div>
                <div class="card-body">
                    <form id="customerForm" method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Customer Type Selection -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label class="form-label">{% trans "Customer Type" %} <span class="text-danger">*</span></label>
                                <div class="btn-group w-100" role="group" aria-label="Customer Type">
                                    <input type="radio" class="btn-check" name="customer_type" id="individual" value="individual" checked>
                                    <label class="btn btn-outline-primary" for="individual">
                                        <i class="bi bi-person"></i> {% trans "Individual" %}
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="customer_type" id="corporate" value="corporate">
                                    <label class="btn btn-outline-primary" for="corporate">
                                        <i class="bi bi-building"></i> {% trans "Corporate" %}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="first_name" class="form-label">{% trans "First Name" %} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="last_name" class="form-label">{% trans "Last Name" %} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>

                        <!-- Corporate Fields (hidden by default) -->
                        <div id="corporateFields" style="display: none;">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="company_name" class="form-label">{% trans "Company Name" %}</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name">
                                </div>
                                <div class="col-md-6">
                                    <label for="tax_number" class="form-label">{% trans "Tax Number" %}</label>
                                    <input type="text" class="form-control" id="tax_number" name="tax_number">
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">{% trans "Email" %}</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6">
                                <label for="phone_number" class="form-label">{% trans "Phone Number" %} <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="phone_number" name="phone_number" required>
                            </div>
                        </div>

                        <!-- Identification -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="id_type" class="form-label">{% trans "ID Type" %} <span class="text-danger">*</span></label>
                                <select class="form-select" id="id_type" name="id_type" required>
                                    <option value="">{% trans "Select ID Type" %}</option>
                                    <option value="passport">{% trans "Passport" %}</option>
                                    <option value="national_id">{% trans "National ID" %}</option>
                                    <option value="driving_license">{% trans "Driving License" %}</option>
                                    <option value="residence_permit">{% trans "Residence Permit" %}</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="id_number" class="form-label">{% trans "ID Number" %} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="id_number" name="id_number" required>
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="address" class="form-label">{% trans "Address" %}</label>
                                <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="city" class="form-label">{% trans "City" %}</label>
                                <input type="text" class="form-control" id="city" name="city">
                            </div>
                            <div class="col-md-4">
                                <label for="country" class="form-label">{% trans "Country" %}</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="">{% trans "Select Country" %}</option>
                                    <option value="TR">{% trans "Turkey" %}</option>
                                    <option value="IR">{% trans "Iran" %}</option>
                                    <option value="AE">{% trans "UAE" %}</option>
                                    <option value="CN">{% trans "China" %}</option>
                                    <option value="US">{% trans "United States" %}</option>
                                    <option value="GB">{% trans "United Kingdom" %}</option>
                                    <option value="DE">{% trans "Germany" %}</option>
                                    <option value="FR">{% trans "France" %}</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="postal_code" class="form-label">{% trans "Postal Code" %}</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code">
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="date_of_birth" class="form-label">{% trans "Date of Birth" %}</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                            </div>
                            <div class="col-md-6">
                                <label for="nationality" class="form-label">{% trans "Nationality" %}</label>
                                <input type="text" class="form-control" id="nationality" name="nationality">
                            </div>
                        </div>

                        <!-- Risk Assessment -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="risk_level" class="form-label">{% trans "Risk Level" %}</label>
                                <select class="form-select" id="risk_level" name="risk_level">
                                    <option value="low">{% trans "Low Risk" %}</option>
                                    <option value="medium" selected>{% trans "Medium Risk" %}</option>
                                    <option value="high">{% trans "High Risk" %}</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="source_of_funds" class="form-label">{% trans "Source of Funds" %}</label>
                                <input type="text" class="form-control" id="source_of_funds" name="source_of_funds" placeholder="{% trans 'e.g., Salary, Business, Investment' %}">
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="notes" class="form-label">{% trans "Notes" %}</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="{% trans 'Additional information about the customer...' %}"></textarea>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{% url 'customers_web:list' %}" class="btn btn-secondary">
                                        <i class="bi bi-x-circle"></i> {% trans "Cancel" %}
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle"></i> {% trans "Save Customer" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/intlTelInput.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize international phone input
    const phoneInput = document.querySelector("#phone_number");
    const iti = window.intlTelInput(phoneInput, {
        initialCountry: "tr",
        preferredCountries: ["tr", "ir", "ae", "cn"],
        utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js"
    });

    // Handle customer type change
    const customerTypeRadios = document.querySelectorAll('input[name="customer_type"]');
    const corporateFields = document.getElementById('corporateFields');
    const firstNameLabel = document.querySelector('label[for="first_name"]');
    const lastNameLabel = document.querySelector('label[for="last_name"]');

    customerTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'corporate') {
                corporateFields.style.display = 'block';
                firstNameLabel.textContent = '{% trans "Contact First Name" %}';
                lastNameLabel.textContent = '{% trans "Contact Last Name" %}';
            } else {
                corporateFields.style.display = 'none';
                firstNameLabel.textContent = '{% trans "First Name" %}';
                lastNameLabel.textContent = '{% trans "Last Name" %}';
            }
        });
    });

    // Form submission
    document.getElementById('customerForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get the full phone number including country code
        const fullPhoneNumber = iti.getNumber();
        phoneInput.value = fullPhoneNumber;
        
        // Validate required fields
        const requiredFields = this.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            showAlert('danger', '{% trans "Please fill in all required fields." %}');
            return;
        }
        
        // Submit form via AJAX to API endpoint
        const formData = new FormData(this);
        const data = Object.fromEntries(formData.entries());

        const headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        };

        // Add JWT token if available
        const accessToken = localStorage.getItem('arena_access_token');
        if (accessToken) {
            headers['Authorization'] = 'Bearer ' + accessToken;
        }

        fetch('/api/v1/customers/customers/', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw { status: response.status, data: errorData };
                });
            }
            return response.json();
        })
        .then(data => {
            showAlert('success', '{% trans "Customer added successfully!" %}');
            setTimeout(() => {
                window.location.href = '{% url "customers_web:list" %}';
            }, 1500);
        })
        .catch(error => {
            console.error('Error:', error);

            let errorMessage = '{% trans "An error occurred while saving the customer." %}';

            if (error.data) {
                if (error.data.error) {
                    errorMessage = error.data.error;
                }

                if (error.data.validation_errors) {
                    errorMessage += '<br><br>{% trans "Validation errors:" %}<br>';
                    for (let field in error.data.validation_errors) {
                        const fieldErrors = error.data.validation_errors[field];
                        if (Array.isArray(fieldErrors)) {
                            errorMessage += `<strong>${field}:</strong> ${fieldErrors.join(', ')}<br>`;
                        } else {
                            errorMessage += `<strong>${field}:</strong> ${fieldErrors}<br>`;
                        }
                    }
                }

                if (error.data.missing_fields) {
                    errorMessage += '<br>{% trans "Missing required fields:" %} ' + error.data.missing_fields.join(', ');
                }
            }

            showAlert('danger', errorMessage);
        });
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
