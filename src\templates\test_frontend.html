{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Frontend Testing - Arena Doviz{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h2 mb-4">
                <i class="bi bi-bug"></i>
                Frontend Testing Dashboard
            </h1>
        </div>
    </div>

    <!-- Test Results -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-check-circle"></i>
                        Test Results
                    </h5>
                </div>
                <div class="card-body">
                    <div id="test-results">
                        <p class="text-muted">Click "Run All Tests" to start testing...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-play-circle"></i>
                        Test Controls
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100 mb-2" onclick="runAllTests()">
                                <i class="bi bi-play-fill"></i>
                                Run All Tests
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-secondary w-100 mb-2" onclick="testAuthentication()">
                                <i class="bi bi-shield-check"></i>
                                Test Auth
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-success w-100 mb-2" onclick="testAPIEndpoints()">
                                <i class="bi bi-cloud-arrow-up"></i>
                                Test APIs
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100 mb-2" onclick="clearResults()">
                                <i class="bi bi-trash"></i>
                                Clear Results
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Individual Test Sections -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-gear"></i>
                        System Tests
                    </h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-outline-primary btn-sm mb-2 w-100" onclick="testJavaScriptLibraries()">
                        Test JavaScript Libraries
                    </button>
                    <button class="btn btn-outline-primary btn-sm mb-2 w-100" onclick="testCSS()">
                        Test CSS Loading
                    </button>
                    <button class="btn btn-outline-primary btn-sm mb-2 w-100" onclick="testLocalStorage()">
                        Test Local Storage
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-database"></i>
                        Data Tests
                    </h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-outline-success btn-sm mb-2 w-100" onclick="testTransactionAPI()">
                        Test Transaction API
                    </button>
                    <button class="btn btn-outline-success btn-sm mb-2 w-100" onclick="testCustomerAPI()">
                        Test Customer API
                    </button>
                    <button class="btn btn-outline-success btn-sm mb-2 w-100" onclick="testDashboardAPI()">
                        Test Dashboard API
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let testResults = [];

function addTestResult(testName, status, message, details = null) {
    const timestamp = new Date().toLocaleTimeString();
    testResults.push({
        name: testName,
        status: status,
        message: message,
        details: details,
        timestamp: timestamp
    });
    updateTestDisplay();
}

function updateTestDisplay() {
    const container = document.getElementById('test-results');
    if (testResults.length === 0) {
        container.innerHTML = '<p class="text-muted">No tests run yet...</p>';
        return;
    }

    let html = '<div class="test-results">';
    testResults.forEach((result, index) => {
        const statusClass = result.status === 'PASS' ? 'success' : 
                           result.status === 'FAIL' ? 'danger' : 'warning';
        const icon = result.status === 'PASS' ? 'check-circle' : 
                    result.status === 'FAIL' ? 'x-circle' : 'exclamation-triangle';
        
        html += `
            <div class="alert alert-${statusClass} alert-sm mb-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <i class="bi bi-${icon} me-2"></i>
                        <strong>${result.name}</strong>
                        <small class="text-muted ms-2">${result.timestamp}</small>
                    </div>
                    <span class="badge bg-${statusClass}">${result.status}</span>
                </div>
                <div class="mt-1">${result.message}</div>
                ${result.details ? `<details class="mt-2"><summary>Details</summary><pre class="mt-1">${result.details}</pre></details>` : ''}
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function clearResults() {
    testResults = [];
    updateTestDisplay();
}

// Authentication Tests
function testAuthentication() {
    addTestResult('Authentication Check', 'INFO', 'Testing JWT authentication...');
    
    try {
        const accessToken = ArenaDoviz.auth.getAccessToken();
        const refreshToken = ArenaDoviz.auth.getRefreshToken();
        
        if (accessToken) {
            addTestResult('JWT Access Token', 'PASS', 'Access token found in localStorage');
        } else {
            addTestResult('JWT Access Token', 'FAIL', 'No access token found - user may need to log in');
        }
        
        if (refreshToken) {
            addTestResult('JWT Refresh Token', 'PASS', 'Refresh token found in localStorage');
        } else {
            addTestResult('JWT Refresh Token', 'WARN', 'No refresh token found');
        }
        
        const isAuthenticated = ArenaDoviz.auth.isAuthenticated();
        addTestResult('Authentication Status', isAuthenticated ? 'PASS' : 'FAIL', 
                     `User is ${isAuthenticated ? 'authenticated' : 'not authenticated'}`);
                     
    } catch (error) {
        addTestResult('Authentication Check', 'FAIL', 'Error checking authentication', error.toString());
    }
}

// JavaScript Library Tests
function testJavaScriptLibraries() {
    addTestResult('JavaScript Libraries', 'INFO', 'Testing required libraries...');
    
    // Test jQuery
    if (typeof $ !== 'undefined') {
        addTestResult('jQuery', 'PASS', `jQuery ${$.fn.jquery} loaded successfully`);
    } else {
        addTestResult('jQuery', 'FAIL', 'jQuery not loaded');
    }
    
    // Test Bootstrap
    if (typeof bootstrap !== 'undefined') {
        addTestResult('Bootstrap JS', 'PASS', 'Bootstrap JavaScript loaded successfully');
    } else {
        addTestResult('Bootstrap JS', 'FAIL', 'Bootstrap JavaScript not loaded');
    }
    
    // Test DataTables
    if (typeof $.fn.DataTable !== 'undefined') {
        addTestResult('DataTables', 'PASS', 'DataTables loaded successfully');
    } else {
        addTestResult('DataTables', 'FAIL', 'DataTables not loaded');
    }
    
    // Test ArenaDoviz object
    if (typeof ArenaDoviz !== 'undefined') {
        addTestResult('ArenaDoviz Core', 'PASS', 'ArenaDoviz object loaded successfully');
    } else {
        addTestResult('ArenaDoviz Core', 'FAIL', 'ArenaDoviz object not loaded');
    }
}

// CSS Tests
function testCSS() {
    addTestResult('CSS Loading', 'INFO', 'Testing CSS files...');
    
    // Test if design system CSS is loaded by checking for custom properties
    const testElement = document.createElement('div');
    testElement.style.color = 'var(--arena-primary)';
    document.body.appendChild(testElement);
    
    const computedStyle = window.getComputedStyle(testElement);
    const primaryColor = computedStyle.color;
    
    document.body.removeChild(testElement);
    
    if (primaryColor && primaryColor !== 'var(--arena-primary)') {
        addTestResult('Design System CSS', 'PASS', `Primary color resolved to: ${primaryColor}`);
    } else {
        addTestResult('Design System CSS', 'FAIL', 'Design system CSS variables not loaded');
    }
}

// Local Storage Tests
function testLocalStorage() {
    addTestResult('Local Storage', 'INFO', 'Testing local storage functionality...');
    
    try {
        const testKey = 'arena_test_key';
        const testValue = 'test_value_' + Date.now();
        
        localStorage.setItem(testKey, testValue);
        const retrievedValue = localStorage.getItem(testKey);
        localStorage.removeItem(testKey);
        
        if (retrievedValue === testValue) {
            addTestResult('Local Storage', 'PASS', 'Local storage read/write working correctly');
        } else {
            addTestResult('Local Storage', 'FAIL', 'Local storage read/write failed');
        }
    } catch (error) {
        addTestResult('Local Storage', 'FAIL', 'Local storage not available', error.toString());
    }
}

// API Tests
function testTransactionAPI() {
    addTestResult('Transaction API', 'INFO', 'Testing transaction API endpoint...');
    
    ArenaDoviz.api.request('GET', 'transactions/transactions/?page_size=1')
        .then(data => {
            addTestResult('Transaction API', 'PASS', 
                         `API working - Found ${data.count} transactions`, 
                         JSON.stringify(data, null, 2));
        })
        .catch(error => {
            addTestResult('Transaction API', 'FAIL', 'Transaction API failed', error.toString());
        });
}

function testCustomerAPI() {
    addTestResult('Customer API', 'INFO', 'Testing customer API endpoint...');
    
    ArenaDoviz.api.request('GET', 'customers/customers/?page_size=1')
        .then(data => {
            addTestResult('Customer API', 'PASS', 
                         `API working - Found ${data.count} customers`, 
                         JSON.stringify(data, null, 2));
        })
        .catch(error => {
            addTestResult('Customer API', 'FAIL', 'Customer API failed', error.toString());
        });
}

function testDashboardAPI() {
    addTestResult('Dashboard API', 'INFO', 'Testing dashboard API endpoints...');
    
    // Test stats endpoint
    ArenaDoviz.api.request('GET', 'core/dashboard/stats/')
        .then(data => {
            addTestResult('Dashboard Stats API', 'PASS', 'Dashboard stats API working', 
                         JSON.stringify(data, null, 2));
        })
        .catch(error => {
            addTestResult('Dashboard Stats API', 'FAIL', 'Dashboard stats API failed', error.toString());
        });
    
    // Test chart data endpoint
    ArenaDoviz.api.request('GET', 'core/dashboard/chart_data/?days=7')
        .then(data => {
            addTestResult('Dashboard Chart API', 'PASS', 'Dashboard chart API working', 
                         JSON.stringify(data, null, 2));
        })
        .catch(error => {
            addTestResult('Dashboard Chart API', 'FAIL', 'Dashboard chart API failed', error.toString());
        });
}

function testAPIEndpoints() {
    testTransactionAPI();
    testCustomerAPI();
    testDashboardAPI();
}

function runAllTests() {
    clearResults();
    addTestResult('Test Suite', 'INFO', 'Starting comprehensive frontend tests...');
    
    // Run all tests
    testAuthentication();
    testJavaScriptLibraries();
    testCSS();
    testLocalStorage();
    
    // API tests with slight delay to avoid overwhelming the server
    setTimeout(() => {
        testAPIEndpoints();
    }, 1000);
    
    addTestResult('Test Suite', 'INFO', 'All tests initiated. Check results above.');
}

// Auto-run basic tests on page load
$(document).ready(function() {
    setTimeout(() => {
        addTestResult('Page Load', 'PASS', 'Frontend testing page loaded successfully');
        testAuthentication();
        testJavaScriptLibraries();
    }, 500);
});
</script>
{% endblock %}
