#!/usr/bin/env python
"""
Debug script for Money Transfer forms issues
"""
import os
import sys
import django
from django.conf import settings

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency
from apps.transactions.models import TransactionType
import json

def test_api_endpoints():
    """Test the API endpoints that the transfer forms use"""
    
    # Create a test client
    client = Client()
    
    # Get or create a test user
    User = get_user_model()
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'is_staff': True,
            'is_active': True
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"Created test user: {user.username}")
    else:
        print(f"Using existing test user: {user.username}")
    
    # Login the user
    login_success = client.login(username='testuser', password='testpass123')
    print(f"Login successful: {login_success}")
    
    if not login_success:
        print("Failed to login, trying to create session manually...")
        from django.contrib.sessions.models import Session
        from django.contrib.auth.models import AnonymousUser
        # Force login
        client.force_login(user)
        print("Force login completed")
    
    print("\n" + "="*50)
    print("TESTING API ENDPOINTS")
    print("="*50)
    
    # Test customers endpoint
    print("\n1. Testing Customers API:")
    response = client.get('/api/v1/customers/customers/')
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   Count: {data.get('count', 0)}")
        if data.get('results'):
            print(f"   First customer: {data['results'][0].get('display_name', 'N/A')}")
    else:
        print(f"   Error: {response.content.decode()}")
    
    # Test locations endpoint
    print("\n2. Testing Locations API:")
    response = client.get('/api/v1/locations/locations/')
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   Count: {data.get('count', 0)}")
        if data.get('results'):
            print(f"   First location: {data['results'][0].get('name', 'N/A')}")
    else:
        print(f"   Error: {response.content.decode()}")
    
    # Test currencies endpoint
    print("\n3. Testing Currencies API:")
    response = client.get('/api/v1/currencies/currencies/')
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   Count: {data.get('count', 0)}")
        if data.get('results'):
            print(f"   First currency: {data['results'][0].get('code', 'N/A')}")
    else:
        print(f"   Error: {response.content.decode()}")
    
    # Test transaction types endpoint
    print("\n4. Testing Transaction Types API:")
    response = client.get('/api/v1/transactions/types/')
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   Count: {data.get('count', 0)}")
        if data.get('results'):
            transfer_types = [t for t in data['results'] if 'TRANSFER' in t.get('code', '')]
            print(f"   Transfer types found: {len(transfer_types)}")
            for t in transfer_types:
                print(f"     - {t.get('code')}: {t.get('name')}")
    else:
        print(f"   Error: {response.content.decode()}")

def check_database_data():
    """Check if we have the required data in the database"""
    
    print("\n" + "="*50)
    print("CHECKING DATABASE DATA")
    print("="*50)
    
    # Check customers
    customer_count = Customer.objects.filter(is_deleted=False).count()
    print(f"\nCustomers: {customer_count}")
    if customer_count > 0:
        customers = Customer.objects.filter(is_deleted=False)[:3]
        for customer in customers:
            print(f"  - {customer.get_display_name()} ({customer.customer_code})")
    
    # Check locations
    location_count = Location.objects.filter(is_deleted=False).count()
    print(f"\nLocations: {location_count}")
    if location_count > 0:
        locations = Location.objects.filter(is_deleted=False)[:3]
        for location in locations:
            print(f"  - {location.name} ({location.code})")
    
    # Check currencies
    currency_count = Currency.objects.filter(is_active=True).count()
    print(f"\nCurrencies: {currency_count}")
    if currency_count > 0:
        currencies = Currency.objects.filter(is_active=True)[:3]
        for currency in currencies:
            print(f"  - {currency.code}: {currency.name}")
    
    # Check transaction types
    transaction_type_count = TransactionType.objects.filter(is_active=True).count()
    print(f"\nTransaction Types: {transaction_type_count}")
    if transaction_type_count > 0:
        transfer_types = TransactionType.objects.filter(
            is_active=True, 
            code__icontains='TRANSFER'
        )
        print(f"Transfer Types: {transfer_types.count()}")
        for tt in transfer_types:
            print(f"  - {tt.code}: {tt.name}")

def create_sample_data():
    """Create sample data if missing"""
    
    print("\n" + "="*50)
    print("CREATING SAMPLE DATA")
    print("="*50)
    
    # Create sample customers if none exist
    if Customer.objects.filter(is_deleted=False).count() == 0:
        print("Creating sample customers...")
        customers_data = [
            {
                'first_name': 'John',
                'last_name': 'Doe',
                'customer_code': 'CUST001',
                'phone': '+1234567890',
                'email': '<EMAIL>'
            },
            {
                'first_name': 'Jane',
                'last_name': 'Smith',
                'customer_code': 'CUST002',
                'phone': '+1234567891',
                'email': '<EMAIL>'
            },
            {
                'first_name': 'Ali',
                'last_name': 'Hassan',
                'customer_code': 'CUST003',
                'phone': '+1234567892',
                'email': '<EMAIL>'
            }
        ]
        
        for customer_data in customers_data:
            customer = Customer.objects.create(**customer_data)
            print(f"  Created: {customer.get_display_name()}")
    
    # Create sample locations if none exist
    if Location.objects.filter(is_deleted=False).count() == 0:
        print("Creating sample locations...")
        locations_data = [
            {
                'name': 'Istanbul Office',
                'code': 'IST',
                'address': 'Istanbul, Turkey',
                'sort_order': 1
            },
            {
                'name': 'Dubai Office',
                'code': 'DXB',
                'address': 'Dubai, UAE',
                'sort_order': 2
            },
            {
                'name': 'Tehran Office',
                'code': 'THR',
                'address': 'Tehran, Iran',
                'sort_order': 3
            }
        ]
        
        for location_data in locations_data:
            location = Location.objects.create(**location_data)
            print(f"  Created: {location.name}")
    
    # Create sample currencies if none exist
    if Currency.objects.filter(is_active=True).count() == 0:
        print("Creating sample currencies...")
        currencies_data = [
            {
                'code': 'USD',
                'name': 'US Dollar',
                'symbol': '$',
                'decimal_places': 2,
                'is_active': True
            },
            {
                'code': 'AED',
                'name': 'UAE Dirham',
                'symbol': 'د.إ',
                'decimal_places': 2,
                'is_active': True
            },
            {
                'code': 'IRR',
                'name': 'Iranian Rial',
                'symbol': '﷼',
                'decimal_places': 0,
                'is_active': True
            }
        ]
        
        for currency_data in currencies_data:
            currency = Currency.objects.create(**currency_data)
            print(f"  Created: {currency.code}")
    
    # Create sample transaction types if none exist
    if TransactionType.objects.filter(is_active=True).count() == 0:
        print("Creating sample transaction types...")
        transaction_types_data = [
            {
                'code': 'INTERNAL_TRANSFER',
                'name': 'Internal Transfer',
                'description': 'Transfer between customers within the system',
                'is_active': True
            },
            {
                'code': 'EXTERNAL_TRANSFER',
                'name': 'External Transfer',
                'description': 'Transfer to external bank accounts',
                'is_active': True
            },
            {
                'code': 'INTERNATIONAL_TRANSFER',
                'name': 'International Transfer',
                'description': 'Cross-border international transfers',
                'is_active': True
            }
        ]
        
        for tt_data in transaction_types_data:
            tt = TransactionType.objects.create(**tt_data)
            print(f"  Created: {tt.code}")

if __name__ == '__main__':
    print("Arena Doviz Transfer Forms Debug Script")
    print("="*50)
    
    try:
        # Check database data first
        check_database_data()
        
        # Create sample data if needed
        create_sample_data()
        
        # Test API endpoints
        test_api_endpoints()
        
        print("\n" + "="*50)
        print("DEBUG COMPLETED")
        print("="*50)
        
    except Exception as e:
        print(f"Error during debugging: {e}")
        import traceback
        traceback.print_exc()
