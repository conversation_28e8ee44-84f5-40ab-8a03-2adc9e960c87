"""
Arena Doviz Financial Accuracy Verification System
"""
import asyncio
import json
import logging
import requests
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict

from config import TestConfig

logger = logging.getLogger("arena_financial")

@dataclass
class BalanceSnapshot:
    """Customer balance snapshot"""
    customer_id: str
    currency_code: str
    balance: Decimal
    timestamp: datetime
    transaction_id: Optional[str] = None

@dataclass
class TransactionVerification:
    """Transaction verification result"""
    transaction_id: str
    transaction_type: str
    status: str  # 'VERIFIED', 'FAILED', 'ERROR'
    checks_performed: List[str]
    errors: List[str]
    balance_before: Dict[str, Decimal]
    balance_after: Dict[str, Decimal]
    expected_balance_change: Dict[str, Decimal]
    actual_balance_change: Dict[str, Decimal]
    commission_verification: Dict[str, Any]
    exchange_rate_verification: Dict[str, Any]
    double_entry_verification: Dict[str, Any]

@dataclass
class AccountingEntry:
    """Double-entry accounting entry"""
    account: str
    debit: Decimal
    credit: Decimal
    currency: str
    transaction_id: str
    timestamp: datetime

class FinancialVerificationSystem:
    """Comprehensive financial accuracy verification"""
    
    def __init__(self):
        self.api_base_url = TestConfig.BASE_URL + "/api/v1"
        self.auth_headers = self._get_auth_headers()
        self.balance_snapshots: List[BalanceSnapshot] = []
        self.verification_results: List[TransactionVerification] = []
        
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for API calls"""
        # This would typically get a JWT token
        return {
            'Authorization': f'Bearer {self._get_jwt_token()}',
            'Content-Type': 'application/json'
        }
    
    def _get_jwt_token(self) -> str:
        """Get JWT token for API authentication"""
        # Simplified token retrieval - in real implementation, this would authenticate
        try:
            response = requests.post(f"{self.api_base_url}/auth/token/", {
                'username': TestConfig.TEST_USERNAME,
                'password': TestConfig.TEST_PASSWORD
            })
            if response.status_code == 200:
                return response.json().get('access', '')
        except Exception as e:
            logger.error(f"Failed to get JWT token: {str(e)}")
        return ''
    
    async def capture_balance_snapshot(self, customer_id: str, transaction_id: str = None) -> List[BalanceSnapshot]:
        """Capture current balance snapshot for a customer"""
        try:
            response = requests.get(
                f"{self.api_base_url}/customers/customers/{customer_id}/balance/",
                headers=self.auth_headers
            )
            
            if response.status_code != 200:
                logger.error(f"Failed to get customer balance: {response.status_code}")
                return []
            
            balances = response.json()
            snapshots = []
            
            for balance_data in balances:
                snapshot = BalanceSnapshot(
                    customer_id=customer_id,
                    currency_code=balance_data['currency_code'],
                    balance=Decimal(str(balance_data['balance'])),
                    timestamp=datetime.now(),
                    transaction_id=transaction_id
                )
                snapshots.append(snapshot)
                self.balance_snapshots.append(snapshot)
            
            logger.info(f"📊 Captured balance snapshot for customer {customer_id}: {len(snapshots)} currencies")
            return snapshots
            
        except Exception as e:
            logger.error(f"Error capturing balance snapshot: {str(e)}")
            return []
    
    async def verify_transaction(self, transaction_id: str) -> TransactionVerification:
        """Verify a transaction's financial accuracy"""
        logger.info(f"🔍 Verifying transaction: {transaction_id}")
        
        verification = TransactionVerification(
            transaction_id=transaction_id,
            transaction_type='',
            status='VERIFIED',
            checks_performed=[],
            errors=[],
            balance_before={},
            balance_after={},
            expected_balance_change={},
            actual_balance_change={},
            commission_verification={},
            exchange_rate_verification={},
            double_entry_verification={}
        )
        
        try:
            # Get transaction details
            transaction = await self._get_transaction_details(transaction_id)
            if not transaction:
                verification.status = 'ERROR'
                verification.errors.append('Transaction not found')
                return verification
            
            verification.transaction_type = transaction.get('transaction_type', {}).get('code', '')
            customer_id = transaction.get('customer', {}).get('id', '')
            
            # Get balance snapshots before and after transaction
            balance_before = await self._get_balance_before_transaction(customer_id, transaction_id)
            balance_after = await self._get_balance_after_transaction(customer_id, transaction_id)
            
            verification.balance_before = balance_before
            verification.balance_after = balance_after
            
            # Calculate actual balance changes
            verification.actual_balance_change = self._calculate_balance_change(balance_before, balance_after)
            
            # Verify different aspects
            await self._verify_balance_changes(verification, transaction)
            await self._verify_commission_calculation(verification, transaction)
            await self._verify_exchange_rate_application(verification, transaction)
            await self._verify_double_entry_bookkeeping(verification, transaction)
            
            # Determine overall status
            if verification.errors:
                verification.status = 'FAILED'
            
        except Exception as e:
            verification.status = 'ERROR'
            verification.errors.append(f"Verification error: {str(e)}")
            logger.error(f"Transaction verification failed: {str(e)}")
        
        self.verification_results.append(verification)
        return verification
    
    async def _get_transaction_details(self, transaction_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed transaction information"""
        try:
            response = requests.get(
                f"{self.api_base_url}/transactions/transactions/{transaction_id}/",
                headers=self.auth_headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get transaction details: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting transaction details: {str(e)}")
            return None
    
    async def _get_balance_before_transaction(self, customer_id: str, transaction_id: str) -> Dict[str, Decimal]:
        """Get customer balance before a specific transaction"""
        # Find the most recent balance snapshot before this transaction
        relevant_snapshots = [
            s for s in self.balance_snapshots 
            if s.customer_id == customer_id and s.transaction_id != transaction_id
        ]
        
        if not relevant_snapshots:
            # If no snapshots available, get current balance and assume it's before
            current_snapshots = await self.capture_balance_snapshot(customer_id)
            return {s.currency_code: s.balance for s in current_snapshots}
        
        # Group by currency and get the most recent for each
        balance_before = {}
        for currency in set(s.currency_code for s in relevant_snapshots):
            currency_snapshots = [s for s in relevant_snapshots if s.currency_code == currency]
            latest_snapshot = max(currency_snapshots, key=lambda x: x.timestamp)
            balance_before[currency] = latest_snapshot.balance
        
        return balance_before
    
    async def _get_balance_after_transaction(self, customer_id: str, transaction_id: str) -> Dict[str, Decimal]:
        """Get customer balance after a specific transaction"""
        # Capture current balance as "after" balance
        current_snapshots = await self.capture_balance_snapshot(customer_id, transaction_id)
        return {s.currency_code: s.balance for s in current_snapshots}
    
    def _calculate_balance_change(self, balance_before: Dict[str, Decimal], balance_after: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """Calculate the change in balance"""
        balance_change = {}
        
        # Get all currencies involved
        all_currencies = set(balance_before.keys()) | set(balance_after.keys())
        
        for currency in all_currencies:
            before = balance_before.get(currency, Decimal('0'))
            after = balance_after.get(currency, Decimal('0'))
            balance_change[currency] = after - before
        
        return balance_change
    
    async def _verify_balance_changes(self, verification: TransactionVerification, transaction: Dict[str, Any]) -> None:
        """Verify that balance changes are correct"""
        verification.checks_performed.append('balance_changes')
        
        try:
            transaction_type = transaction.get('transaction_type', {}).get('code', '')
            from_currency = transaction.get('from_currency', {}).get('code', '')
            to_currency = transaction.get('to_currency', {}).get('code', '')
            from_amount = Decimal(str(transaction.get('from_amount', 0)))
            to_amount = Decimal(str(transaction.get('to_amount', 0)))
            commission_amount = Decimal(str(transaction.get('commission_amount', 0)))
            
            # Calculate expected balance changes based on transaction type
            expected_changes = {}
            
            if transaction_type == 'DEPOSIT':
                expected_changes[from_currency] = from_amount - commission_amount
            elif transaction_type == 'WITHDRAWAL':
                expected_changes[from_currency] = -(from_amount + commission_amount)
            elif transaction_type == 'EXCHANGE':
                expected_changes[from_currency] = -(from_amount + commission_amount)
                expected_changes[to_currency] = to_amount
            elif transaction_type in ['INTERNAL_TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNATIONAL_TRANSFER']:
                expected_changes[from_currency] = -(from_amount + commission_amount)
            elif transaction_type == 'ADJUSTMENT':
                adjustment_type = transaction.get('adjustment_type', 'credit')
                if adjustment_type == 'credit':
                    expected_changes[from_currency] = from_amount
                else:
                    expected_changes[from_currency] = -from_amount
            
            verification.expected_balance_change = expected_changes
            
            # Compare expected vs actual changes
            for currency, expected_change in expected_changes.items():
                actual_change = verification.actual_balance_change.get(currency, Decimal('0'))
                
                # Allow for small rounding differences
                tolerance = Decimal('0.000001')
                if abs(actual_change - expected_change) > tolerance:
                    verification.errors.append(
                        f"Balance change mismatch for {currency}: expected {expected_change}, actual {actual_change}"
                    )
            
        except Exception as e:
            verification.errors.append(f"Balance verification error: {str(e)}")
    
    async def _verify_commission_calculation(self, verification: TransactionVerification, transaction: Dict[str, Any]) -> None:
        """Verify commission calculation accuracy"""
        verification.checks_performed.append('commission_calculation')
        
        try:
            transaction_type = transaction.get('transaction_type', {}).get('code', '')
            from_amount = Decimal(str(transaction.get('from_amount', 0)))
            commission_amount = Decimal(str(transaction.get('commission_amount', 0)))
            
            # Get commission rules for this transaction type
            expected_commission = await self._calculate_expected_commission(
                transaction_type, from_amount, transaction
            )
            
            verification.commission_verification = {
                'expected_commission': str(expected_commission),
                'actual_commission': str(commission_amount),
                'difference': str(abs(commission_amount - expected_commission)),
                'within_tolerance': abs(commission_amount - expected_commission) <= Decimal('0.01')
            }
            
            if not verification.commission_verification['within_tolerance']:
                verification.errors.append(
                    f"Commission calculation error: expected {expected_commission}, actual {commission_amount}"
                )
            
        except Exception as e:
            verification.errors.append(f"Commission verification error: {str(e)}")
    
    async def _verify_exchange_rate_application(self, verification: TransactionVerification, transaction: Dict[str, Any]) -> None:
        """Verify exchange rate application"""
        verification.checks_performed.append('exchange_rate_application')
        
        try:
            transaction_type = transaction.get('transaction_type', {}).get('code', '')
            
            # Only verify for transactions that involve currency exchange
            if transaction_type not in ['EXCHANGE', 'INTERNATIONAL_TRANSFER', 'REMITTANCE']:
                verification.exchange_rate_verification = {'applicable': False}
                return
            
            from_currency = transaction.get('from_currency', {}).get('code', '')
            to_currency = transaction.get('to_currency', {}).get('code', '')
            from_amount = Decimal(str(transaction.get('from_amount', 0)))
            to_amount = Decimal(str(transaction.get('to_amount', 0)))
            exchange_rate = Decimal(str(transaction.get('exchange_rate', 1)))
            
            # Calculate expected to_amount
            expected_to_amount = from_amount * exchange_rate
            
            verification.exchange_rate_verification = {
                'applicable': True,
                'from_currency': from_currency,
                'to_currency': to_currency,
                'exchange_rate': str(exchange_rate),
                'expected_to_amount': str(expected_to_amount),
                'actual_to_amount': str(to_amount),
                'difference': str(abs(to_amount - expected_to_amount)),
                'within_tolerance': abs(to_amount - expected_to_amount) <= Decimal('0.01')
            }
            
            if not verification.exchange_rate_verification['within_tolerance']:
                verification.errors.append(
                    f"Exchange rate application error: expected {expected_to_amount}, actual {to_amount}"
                )
            
        except Exception as e:
            verification.errors.append(f"Exchange rate verification error: {str(e)}")
    
    async def _verify_double_entry_bookkeeping(self, verification: TransactionVerification, transaction: Dict[str, Any]) -> None:
        """Verify double-entry bookkeeping entries"""
        verification.checks_performed.append('double_entry_bookkeeping')
        
        try:
            transaction_id = transaction.get('id', '')
            
            # Get accounting entries for this transaction
            accounting_entries = await self._get_accounting_entries(transaction_id)
            
            if not accounting_entries:
                verification.errors.append("No accounting entries found for transaction")
                return
            
            # Verify that debits equal credits for each currency
            currency_totals = {}
            
            for entry in accounting_entries:
                currency = entry.currency
                if currency not in currency_totals:
                    currency_totals[currency] = {'debits': Decimal('0'), 'credits': Decimal('0')}
                
                currency_totals[currency]['debits'] += entry.debit
                currency_totals[currency]['credits'] += entry.credit
            
            verification.double_entry_verification = {
                'entries_count': len(accounting_entries),
                'currency_balances': {},
                'balanced': True
            }
            
            for currency, totals in currency_totals.items():
                debits = totals['debits']
                credits = totals['credits']
                difference = abs(debits - credits)
                
                verification.double_entry_verification['currency_balances'][currency] = {
                    'debits': str(debits),
                    'credits': str(credits),
                    'difference': str(difference),
                    'balanced': difference <= Decimal('0.01')
                }
                
                if difference > Decimal('0.01'):
                    verification.double_entry_verification['balanced'] = False
                    verification.errors.append(
                        f"Double-entry imbalance in {currency}: debits {debits}, credits {credits}"
                    )
            
        except Exception as e:
            verification.errors.append(f"Double-entry verification error: {str(e)}")
    
    async def _calculate_expected_commission(self, transaction_type: str, amount: Decimal, transaction: Dict[str, Any]) -> Decimal:
        """Calculate expected commission based on rules"""
        # Simplified commission calculation - in real system, this would query commission rules
        commission_rates = {
            'EXCHANGE': Decimal('0.002'),  # 0.2%
            'DEPOSIT': Decimal('0.0005'),  # 0.05%
            'WITHDRAWAL': Decimal('0.0005'),  # 0.05%
            'INTERNAL_TRANSFER': Decimal('0.001'),  # 0.1%
            'EXTERNAL_TRANSFER': Decimal('0.0015'),  # 0.15%
            'INTERNATIONAL_TRANSFER': Decimal('0.003'),  # 0.3%
            'REMITTANCE': Decimal('0.0025'),  # 0.25%
            'ADJUSTMENT': Decimal('0')  # No commission
        }
        
        rate = commission_rates.get(transaction_type, Decimal('0.001'))
        return (amount * rate).quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP)
    
    async def _get_accounting_entries(self, transaction_id: str) -> List[AccountingEntry]:
        """Get accounting entries for a transaction"""
        try:
            # This would query the accounting/journal entries table
            # For now, return empty list as placeholder
            return []
            
        except Exception as e:
            logger.error(f"Error getting accounting entries: {str(e)}")
            return []
    
    def generate_financial_report(self) -> Dict[str, Any]:
        """Generate comprehensive financial verification report"""
        total_verifications = len(self.verification_results)
        verified = len([v for v in self.verification_results if v.status == 'VERIFIED'])
        failed = len([v for v in self.verification_results if v.status == 'FAILED'])
        errors = len([v for v in self.verification_results if v.status == 'ERROR'])
        
        # Analyze error patterns
        error_patterns = {}
        for verification in self.verification_results:
            for error in verification.errors:
                error_type = error.split(':')[0] if ':' in error else error
                error_patterns[error_type] = error_patterns.get(error_type, 0) + 1
        
        return {
            'summary': {
                'total_verifications': total_verifications,
                'verified': verified,
                'failed': failed,
                'errors': errors,
                'accuracy_rate': (verified / total_verifications * 100) if total_verifications > 0 else 0
            },
            'error_patterns': error_patterns,
            'verification_details': [asdict(v) for v in self.verification_results],
            'balance_snapshots': [asdict(s) for s in self.balance_snapshots]
        }
