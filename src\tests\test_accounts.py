"""
Tests for Arena Doviz Accounts app.
Tests user management, authentication, permissions, and audit logging.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from rest_framework import status
from rest_framework_simplejwt.tokens import Re<PERSON>resh<PERSON>oken
from datetime import timed<PERSON><PERSON>
from .base import BaseTestCase, BaseAPITestCase, TestDataMixin, assert_audit_log_created
from apps.accounts.models import UserSession, AuditLog
from apps.accounts.serializers import CustomTokenObtainPairSerializer
from apps.accounts.utils import log_user_action, get_client_ip

User = get_user_model()


class UserModelTest(BaseTestCase):
    """Test User model functionality."""
    
    def test_user_creation(self):
        """Test creating a user with all required fields."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            role=User.Role.BRANCH_EMPLOYEE,
            location=self.location
        )
        
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, User.Role.BRANCH_EMPLOYEE)
        self.assertEqual(user.location, self.location)
        self.assertTrue(user.check_password('testpass123'))
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
    
    def test_superuser_creation(self):
        """Test creating a superuser."""
        user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        self.assertEqual(user.role, User.Role.ADMIN)
    
    def test_user_display_name(self):
        """Test user display name generation."""
        user = self.create_test_user('testuser', first_name='John', last_name='Doe')
        self.assertEqual(user.get_display_name(), 'John Doe')
        
        user_no_name = self.create_test_user('noname', first_name='', last_name='')
        self.assertEqual(user_no_name.get_display_name(), 'noname')
    
    def test_user_permissions(self):
        """Test role-based permissions."""
        # Admin permissions
        self.assertTrue(self.admin_user.can_manage_users())
        self.assertTrue(self.admin_user.can_approve_transactions())
        self.assertTrue(self.admin_user.can_complete_transactions())
        self.assertTrue(self.admin_user.can_view_all_locations())
        
        # Accountant permissions
        self.assertFalse(self.accountant_user.can_manage_users())
        self.assertTrue(self.accountant_user.can_approve_transactions())
        self.assertTrue(self.accountant_user.can_complete_transactions())
        self.assertFalse(self.accountant_user.can_view_all_locations())
        
        # Employee permissions
        self.assertFalse(self.employee_user.can_manage_users())
        self.assertFalse(self.employee_user.can_approve_transactions())
        self.assertTrue(self.employee_user.can_complete_transactions())
        self.assertFalse(self.employee_user.can_view_all_locations())
        
        # Viewer permissions
        self.assertFalse(self.viewer_user.can_manage_users())
        self.assertFalse(self.viewer_user.can_approve_transactions())
        self.assertFalse(self.viewer_user.can_complete_transactions())
        self.assertFalse(self.viewer_user.can_view_all_locations())
    
    def test_account_locking(self):
        """Test account locking mechanism."""
        user = self.create_test_user('locktest')
        
        # Initially not locked
        self.assertFalse(user.is_account_locked())
        
        # Simulate failed login attempts
        user.failed_login_attempts = 5
        user.account_locked_until = timezone.now() + timedelta(minutes=30)
        user.save()
        
        self.assertTrue(user.is_account_locked())
        
        # Test automatic unlock after time expires
        user.account_locked_until = timezone.now() - timedelta(minutes=1)
        user.save()
        
        self.assertFalse(user.is_account_locked())
    
    def test_user_validation(self):
        """Test user model validation."""
        # Test duplicate username
        with self.assertRaises(Exception):
            User.objects.create_user(
                username=self.admin_user.username,
                email='<EMAIL>',
                password='testpass123'
            )
        
        # Test duplicate email
        with self.assertRaises(Exception):
            User.objects.create_user(
                username='different_user',
                email=self.admin_user.email,
                password='testpass123'
            )


class UserSessionModelTest(BaseTestCase):
    """Test UserSession model functionality."""
    
    def test_session_creation(self):
        """Test creating a user session."""
        session = UserSession.objects.create(
            user=self.admin_user,
            session_key='test_session_key',
            ip_address='127.0.0.1',
            user_agent='Test User Agent'
        )
        
        self.assertEqual(session.user, self.admin_user)
        self.assertEqual(session.session_key, 'test_session_key')
        self.assertEqual(session.ip_address, '127.0.0.1')
        self.assertTrue(session.is_active)
    
    def test_session_expiry(self):
        """Test session expiry functionality."""
        session = UserSession.objects.create(
            user=self.admin_user,
            session_key='test_session_key',
            ip_address='127.0.0.1',
            expires_at=timezone.now() - timedelta(hours=1)
        )
        
        self.assertTrue(session.is_expired())
        
        # Test active session
        active_session = UserSession.objects.create(
            user=self.admin_user,
            session_key='active_session_key',
            ip_address='127.0.0.1',
            expires_at=timezone.now() + timedelta(hours=1)
        )
        
        self.assertFalse(active_session.is_expired())


class AuditLogModelTest(BaseTestCase):
    """Test AuditLog model functionality."""
    
    def test_audit_log_creation(self):
        """Test creating an audit log entry."""
        log_entry = AuditLog.objects.create(
            user=self.admin_user,
            action='test_action',
            model_name='TestModel',
            object_id='123',
            ip_address='127.0.0.1',
            additional_data={'test': 'data'}
        )

        self.assertEqual(log_entry.user, self.admin_user)
        self.assertEqual(log_entry.action, 'test_action')
        self.assertEqual(log_entry.model_name, 'TestModel')
        self.assertEqual(log_entry.object_id, '123')
        self.assertEqual(log_entry.additional_data, {'test': 'data'})
    
    def test_log_user_action_utility(self):
        """Test log_user_action utility function."""
        log_user_action(
            user=self.admin_user,
            action='test_utility_action',
            model_name='TestModel',
            object_id='456',
            ip_address='***********',
            additional_data={'utility': 'test'}
        )

        log_entry = AuditLog.objects.filter(
            user=self.admin_user,
            action='test_utility_action'
        ).first()

        self.assertIsNotNone(log_entry)
        self.assertEqual(log_entry.model_name, 'TestModel')
        self.assertEqual(log_entry.object_id, '456')
        self.assertEqual(log_entry.ip_address, '***********')


class JWTAuthenticationTest(BaseAPITestCase):
    """Test JWT authentication functionality."""

    def setUp(self):
        """Set up test environment."""
        super().setUp()

        # Set up encryption key for tests
        from apps.core.encryption import generate_encryption_key
        from unittest.mock import patch
        import os

        self.encryption_key = generate_encryption_key()
        self.env_patcher = patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': self.encryption_key})
        self.env_patcher.start()

    def tearDown(self):
        """Clean up test environment."""
        self.env_patcher.stop()
        super().tearDown()

    def test_jwt_token_obtain(self):
        """Test obtaining JWT tokens."""
        url = '/api/v1/accounts/jwt/token/'
        data = {
            'username': self.admin_user.username,
            'password': 'testpass123'
        }
        
        response = self.client.post(url, data)
        self.assertAPISuccess(response)
        
        # Check response contains required fields
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        
        # Check user data
        user_data = response.data['user']
        self.assertEqual(user_data['username'], self.admin_user.username)
        self.assertEqual(user_data['role'], self.admin_user.role)
        self.assertIn('permissions', user_data)
    
    def test_jwt_token_refresh(self):
        """Test refreshing JWT tokens."""
        # Get initial tokens
        refresh = RefreshToken.for_user(self.admin_user)
        
        url = '/api/v1/accounts/jwt/token/refresh/'
        data = {'refresh': str(refresh)}
        
        response = self.client.post(url, data)
        self.assertAPISuccess(response)
        
        self.assertIn('access', response.data)
    
    def test_jwt_token_verify(self):
        """Test verifying JWT tokens."""
        refresh = RefreshToken.for_user(self.admin_user)
        access_token = refresh.access_token
        
        url = '/api/v1/accounts/jwt/token/verify/'
        data = {'token': str(access_token)}
        
        response = self.client.post(url, data)
        self.assertAPISuccess(response)
    
    def test_jwt_authentication_with_locked_account(self):
        """Test JWT authentication with locked account."""
        # Lock the account
        self.admin_user.failed_login_attempts = 5
        self.admin_user.account_locked_until = timezone.now() + timedelta(minutes=30)
        self.admin_user.save()
        
        url = '/api/v1/accounts/jwt/token/'
        data = {
            'username': self.admin_user.username,
            'password': 'testpass123'
        }
        
        response = self.client.post(url, data)
        self.assertAPIError(response, 400)
    
    def test_custom_jwt_serializer(self):
        """Test custom JWT serializer functionality."""
        serializer = CustomTokenObtainPairSerializer()
        
        # Test token generation with custom claims
        token = serializer.get_token(self.admin_user)
        
        self.assertEqual(token['username'], self.admin_user.username)
        self.assertEqual(token['role'], self.admin_user.role)
        self.assertEqual(token['location_id'], str(self.admin_user.location.id))


class UserAPITest(BaseAPITestCase):
    """Test User API endpoints."""

    def setUp(self):
        """Set up test environment."""
        super().setUp()

        # Set up encryption key for tests
        from apps.core.encryption import generate_encryption_key
        from unittest.mock import patch
        import os

        self.encryption_key = generate_encryption_key()
        self.env_patcher = patch.dict(os.environ, {'ARENA_ENCRYPTION_KEY': self.encryption_key})
        self.env_patcher.start()

    def tearDown(self):
        """Clean up test environment."""
        self.env_patcher.stop()
        super().tearDown()

    def test_user_list(self):
        """Test listing users."""
        url = '/api/v1/accounts/users/'
        response = self.client.get(url)
        self.assertAPISuccess(response)

        # Check that users are returned
        self.assertGreater(len(response.data['results']), 0)

    def test_user_create(self):
        """Test creating a user via API."""
        url = '/api/v1/accounts/users/'
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'newpass123',
            'password_confirm': 'newpass123',
            'first_name': 'New',
            'last_name': 'User',
            'role': User.Role.BRANCH_EMPLOYEE,
            'location': str(self.location.id)
        }
        
        response = self.client.post(url, data)
        self.assertAPISuccess(response, 201)
        
        # Verify user was created
        user = User.objects.get(username='newuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, User.Role.BRANCH_EMPLOYEE)
        
        # Verify audit log was created
        assert_audit_log_created(self, self.admin_user, 'create', model_name='User', object_id=str(user.id))
    
    def test_user_update(self):
        """Test updating a user via API."""
        user = self.create_test_user('updatetest')
        url = f'/api/v1/accounts/users/{user.id}/'
        data = {
            'first_name': 'Updated',
            'last_name': 'Name'
        }
        
        response = self.client.patch(url, data)
        self.assertAPISuccess(response)
        
        # Verify user was updated
        user.refresh_from_db()
        self.assertEqual(user.first_name, 'Updated')
        self.assertEqual(user.last_name, 'Name')
    
    def test_user_permissions_required(self):
        """Test that proper permissions are required for user management."""
        # Test with employee user (should not have permission)
        self.authenticate_as(self.employee_user)
        
        url = '/api/v1/accounts/users/'
        data = {
            'username': 'unauthorized',
            'email': '<EMAIL>',
            'password': 'strongpass123',
            'password_confirm': 'strongpass123',
            'first_name': 'Test',
            'last_name': 'User',
            'role': User.Role.BRANCH_EMPLOYEE
        }
        
        response = self.client.post(url, data)
        self.assertAPIPermissionDenied(response)
    
    def test_user_profile(self):
        """Test user profile endpoint."""
        url = '/api/v1/accounts/users/profile/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Check profile data
        self.assertEqual(response.data['username'], self.admin_user.username)
        self.assertEqual(response.data['role'], self.admin_user.role)
    
    def test_change_password(self):
        """Test password change endpoint."""
        url = '/api/v1/accounts/users/change_password/'
        data = {
            'old_password': 'testpass123',
            'new_password': 'newpass456',
            'new_password_confirm': 'newpass456'
        }
        
        response = self.client.post(url, data)
        self.assertAPISuccess(response)
        
        # Verify password was changed
        self.admin_user.refresh_from_db()
        self.assertTrue(self.admin_user.check_password('newpass456'))
    
    def test_user_stats(self):
        """Test user statistics endpoint."""
        url = '/api/v1/accounts/users/stats/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Check stats structure
        self.assertIn('total_users', response.data)
        self.assertIn('active_users', response.data)
        self.assertIn('users_by_role', response.data)
        self.assertIn('users_by_location', response.data)


class AuthenticationUtilsTest(BaseTestCase):
    """Test authentication utility functions."""
    
    def test_get_client_ip(self):
        """Test getting client IP address from request."""
        from django.test import RequestFactory
        
        factory = RequestFactory()
        
        # Test with X-Forwarded-For header
        request = factory.get('/')
        request.META['HTTP_X_FORWARDED_FOR'] = '***********, ********'
        
        ip = get_client_ip(request)
        self.assertEqual(ip, '***********')
        
        # Test with REMOTE_ADDR
        request = factory.get('/')
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        
        ip = get_client_ip(request)
        self.assertEqual(ip, '127.0.0.1')
        
        # Test with no IP information
        request = factory.get('/')
        # Clear any automatically set IP information
        if 'REMOTE_ADDR' in request.META:
            del request.META['REMOTE_ADDR']
        if 'HTTP_X_FORWARDED_FOR' in request.META:
            del request.META['HTTP_X_FORWARDED_FOR']
        ip = get_client_ip(request)
        self.assertEqual(ip, 'unknown')
