2025-08-17 00:41:30,604 - apps.reports.signals - INFO - New report template created: Transaction Summary Report
2025-08-17 00:41:30,605 - apps.reports.views - INFO - Created default template for report type: transaction_summary
2025-08-17 00:41:30,649 - apps.reports.signals - INFO - New report generated: Transaction Summary Report
2025-08-17 00:41:30,697 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-17 00:41:30,698 - apps.reports.models - INFO - Report generation completed: Transaction Summary Report - 2025-08-17 00:41
2025-08-17 00:41:30,698 - apps.reports.views - INFO - Report generation started: Transaction Summary Report - 2025-08-17 00:41 by admin (admin)
2025-08-17 00:48:12,561 - apps.reports.signals - INFO - New report generated: Transaction Summary Report
2025-08-17 00:48:12,654 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-17 00:48:12,672 - apps.reports.models - INFO - Report generation completed: Transaction Summary Report - 2025-08-17 00:48
2025-08-17 00:48:12,672 - apps.reports.views - INFO - Report generation started: Transaction Summary Report - 2025-08-17 00:48 by admin (admin)
2025-08-17 00:48:17,921 - apps.reports.signals - INFO - New report template created: Balance Report Report
2025-08-17 00:48:17,923 - apps.reports.views - INFO - Created default template for report type: balance_report
2025-08-17 00:48:17,952 - apps.reports.signals - INFO - New report generated: Balance Report Report
2025-08-17 00:48:17,995 - apps.reports.signals - INFO - Generated report updated: Balance Report Report
2025-08-17 00:48:17,996 - apps.reports.models - INFO - Report generation completed: Balance Report Report - 2025-08-17 00:48
2025-08-17 00:48:17,996 - apps.reports.views - INFO - Report generation started: Balance Report Report - 2025-08-17 00:48 by admin (admin)
2025-08-17 00:48:21,089 - apps.reports.signals - INFO - New report template created: Customer Report Report
2025-08-17 00:48:21,091 - apps.reports.views - INFO - Created default template for report type: customer_report
2025-08-17 00:48:21,115 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 00:48:21,142 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 00:48:21,150 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 00:48
2025-08-17 00:48:21,151 - apps.reports.views - INFO - Report generation started: Customer Report Report - 2025-08-17 00:48 by admin (admin)
2025-08-17 00:50:27,717 - apps.reports.signals - INFO - New report generated: Transaction Summary Report
2025-08-17 00:50:27,743 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-17 00:50:27,744 - apps.reports.models - INFO - Report generation completed: Transaction Summary Report - 2025-08-17 00:50
2025-08-17 00:50:27,746 - apps.reports.views - INFO - Report generation started: Transaction Summary Report - 2025-08-17 00:50 by admin (admin)
2025-08-17 00:51:20,483 - apps.reports.signals - INFO - New report generated: Balance Report Report
2025-08-17 00:51:20,517 - apps.reports.signals - INFO - Generated report updated: Balance Report Report
2025-08-17 00:51:20,517 - apps.reports.models - INFO - Report generation completed: Balance Report Report - 2025-08-17 00:51
2025-08-17 00:51:20,518 - apps.reports.views - INFO - Report generation started: Balance Report Report - 2025-08-17 00:51 by admin (admin)
2025-08-17 00:51:26,757 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 00:51:26,979 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 00:51:26,986 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 00:51
2025-08-17 00:51:26,986 - apps.reports.views - INFO - Report generation started: Customer Report Report - 2025-08-17 00:51 by admin (admin)
2025-08-17 01:01:29,344 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 01:01:29,361 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 01:01:29,362 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 01:01
2025-08-17 01:01:29,362 - apps.reports.views - INFO - Report generation started: Customer Report Report - 2025-08-17 01:01 by admin (admin)
2025-08-17 01:09:21,626 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 01:09:21,627 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-17 01:01 (download #1)
2025-08-17 01:09:21,633 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-17 01:01 by admin (admin)
2025-08-17 01:09:37,784 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 01:09:37,794 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 01:09:37,825 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 01:09
2025-08-17 01:09:37,826 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-17 01:09 - 2025-08-17 08:09 by admin (admin)
2025-08-17 01:09:46,700 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-17 01:09:46,702 - apps.reports.models - INFO - Report downloaded: Transaction Summary Report - 2025-08-17 00:41 (download #1)
2025-08-17 01:09:46,732 - apps.reports.views - INFO - Report downloaded: Transaction Summary Report - 2025-08-17 00:41 by admin (admin)
2025-08-17 01:11:23,841 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 01:12:39,545 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 01:12:39,575 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-17 01:01 (download #2)
2025-08-17 01:12:39,601 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-17 01:01 by admin (admin)
2025-08-17 01:16:35,968 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 01:16:36,278 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 01:16:36,279 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 01:16
2025-08-17 01:16:36,279 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-17 01:16 - 2025-08-17 08:16 by admin (admin)
2025-08-17 01:16:43,787 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 01:16:43,802 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-17 01:01 (download #3)
2025-08-17 01:16:43,825 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-17 01:01 by admin (admin)
2025-08-17 01:53:07,413 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 01:53:07,779 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 01:53:07,781 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 01:53
2025-08-17 01:53:07,782 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-17 01:53 - 2025-08-17 08:53 by admin (admin)
2025-08-17 01:54:38,384 - apps.reports.signals - INFO - New report generated: Transaction Summary Report
2025-08-17 01:54:38,420 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-17 01:54:38,422 - apps.reports.models - INFO - Report generation completed: Transaction Summary Report - 2025-08-17 01:54
2025-08-17 01:54:38,423 - apps.reports.views - INFO - Report generation completed: Transaction Summary Report - 2025-08-17 01:54 - 2025-08-17 08:54 by admin (admin)
2025-08-17 01:55:54,017 - apps.reports.signals - INFO - New report generated: Balance Report Report
2025-08-17 01:55:54,075 - apps.reports.signals - INFO - Generated report updated: Balance Report Report
2025-08-17 01:55:54,077 - apps.reports.models - INFO - Report generation completed: Balance Report Report - 2025-08-17 01:55
2025-08-17 01:55:54,078 - apps.reports.views - INFO - Report generation completed: Balance Report Report - 2025-08-17 01:55 - 2025-08-17 08:55 by admin (admin)
2025-08-17 01:56:01,589 - apps.reports.signals - INFO - Generated report updated: Balance Report Report
2025-08-17 01:56:01,591 - apps.reports.models - INFO - Report downloaded: Balance Report Report - 2025-08-17 01:55 (download #1)
2025-08-17 01:56:01,610 - apps.reports.views - INFO - Report downloaded: Balance Report Report - 2025-08-17 01:55 by admin (admin)
2025-08-17 02:07:14,921 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 02:07:14,955 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 02:07:14,958 - apps.reports.models - ERROR - Report generation failed: Customer Report Report - 2025-08-17 02:07 - No module named 'apps.balances'
2025-08-17 02:07:14,959 - apps.reports.views - ERROR - Report generation failed: No module named 'apps.balances'
2025-08-17 02:07:24,511 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 02:07:24,531 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 02:07:24,531 - apps.reports.models - ERROR - Report generation failed: Customer Report Report - 2025-08-17 02:07 - No module named 'apps.balances'
2025-08-17 02:07:24,531 - apps.reports.views - ERROR - Report generation failed: No module named 'apps.balances'
2025-08-17 02:35:55,761 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 02:35:55,949 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 02:35:55,950 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 02:35
2025-08-17 02:35:55,950 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-17 02:35 - 2025-08-17 09:35 by admin (admin)
2025-08-17 02:36:02,739 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 02:36:02,742 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-17 02:30 (download #1)
2025-08-17 02:36:02,746 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-17 02:30 by admin (admin)
2025-08-17 02:46:49,744 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 02:46:49,757 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 02:46:49,765 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 02:46
2025-08-17 02:46:49,766 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-17 02:46 - 2025-08-17 09:46 by admin (admin)
2025-08-17 03:00:31,310 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 03:00:31,311 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-17 02:53 (download #1)
2025-08-17 03:00:31,314 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-17 02:53 by admin (admin)
2025-08-17 03:09:03,475 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 03:09:03,670 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 03:09:03,671 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 03:09
2025-08-17 03:09:03,671 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-17 03:09 - 2025-08-17 10:09 by admin (admin)
2025-08-17 03:09:12,464 - apps.reports.signals - INFO - New report generated: Balance Report Report
2025-08-17 03:09:12,478 - apps.reports.views - ERROR - Error generating report with data: Cannot call select_related() after .values() or .values_list()
2025-08-17 03:09:12,486 - apps.reports.signals - INFO - Generated report updated: Balance Report Report
2025-08-17 03:09:12,488 - apps.reports.models - INFO - Report generation completed: Balance Report Report - 2025-08-17 03:09
2025-08-17 03:09:12,488 - apps.reports.views - INFO - Report generation completed: Balance Report Report - 2025-08-17 03:09 - 2025-08-17 10:09 by admin (admin)
2025-08-17 03:09:27,492 - apps.reports.signals - INFO - New report generated: Transaction Summary Report
2025-08-17 03:09:27,683 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-17 03:09:27,691 - apps.reports.models - INFO - Report generation completed: Transaction Summary Report - 2025-08-17 03:09
2025-08-17 03:09:27,691 - apps.reports.views - INFO - Report generation completed: Transaction Summary Report - 2025-08-17 03:09 - 2025-08-17 10:09 by admin (admin)
2025-08-17 03:12:19,819 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-17 03:12:19,996 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 03:12:19,997 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-17 03:12
2025-08-17 03:12:19,997 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-17 03:12 - 2025-08-17 10:12 by admin (admin)
2025-08-17 03:17:10,934 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-17 03:17:10,936 - apps.reports.models - INFO - Report downloaded: Transaction Summary Report - 2025-08-17 03:09 (download #1)
2025-08-17 03:17:10,943 - apps.reports.views - INFO - Report downloaded: Transaction Summary Report - 2025-08-17 03:09 by admin (admin)
2025-08-17 03:17:21,451 - apps.reports.signals - INFO - Generated report updated: Balance Report Report
2025-08-17 03:17:21,565 - apps.reports.models - INFO - Report downloaded: Balance Report Report - 2025-08-17 03:09 (download #1)
2025-08-17 03:17:21,719 - apps.reports.views - INFO - Report downloaded: Balance Report Report - 2025-08-17 03:09 by admin (admin)
2025-08-17 03:17:29,701 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 03:17:29,703 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-17 03:09 (download #1)
2025-08-17 03:17:29,708 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-17 03:09 by admin (admin)
2025-08-17 08:55:40,003 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-17 08:55:40,005 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-17 03:12 (download #1)
2025-08-17 08:55:40,010 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-17 03:12 by admin (admin)
2025-08-18 01:14:49,422 - apps.reports.signals - INFO - New report template created: Profit Loss Report
2025-08-18 01:14:49,423 - apps.reports.views - INFO - Created default template for report type: profit_loss
2025-08-18 01:14:49,429 - apps.reports.signals - INFO - New report generated: Profit Loss Report
2025-08-18 01:14:49,645 - apps.reports.signals - INFO - Generated report updated: Profit Loss Report
2025-08-18 01:14:49,646 - apps.reports.models - INFO - Report generation completed: Profit Loss Report - 2025-08-18 01:14
2025-08-18 01:14:49,646 - apps.reports.views - INFO - Report generation completed: Profit Loss Report - 2025-08-18 01:14 - 2025-08-18 08:14 by admin (admin)
2025-08-18 01:15:01,850 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-18 01:15:01,851 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-17 03:12 (download #2)
2025-08-18 01:15:01,855 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-17 03:12 by admin (admin)
2025-08-18 01:16:06,433 - apps.reports.signals - INFO - Generated report updated: Profit Loss Report
2025-08-18 01:16:06,434 - apps.reports.models - INFO - Report downloaded: Profit Loss Report - 2025-08-18 01:14 (download #1)
2025-08-18 01:16:06,438 - apps.reports.views - INFO - Report downloaded: Profit Loss Report - 2025-08-18 01:14 by admin (admin)
2025-08-18 01:16:19,572 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-18 01:16:19,598 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-18 01:16:19,599 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-18 01:16
2025-08-18 01:16:19,599 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-18 01:16 - 2025-08-18 08:16 by admin (admin)
2025-08-18 01:17:12,887 - apps.reports.signals - INFO - New report generated: Transaction Summary Report
2025-08-18 01:17:12,908 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-18 01:17:12,910 - apps.reports.models - INFO - Report generation completed: Transaction Summary Report - 2025-08-18 01:17
2025-08-18 01:17:12,910 - apps.reports.views - INFO - Report generation completed: Transaction Summary Report - 2025-08-18 01:17 - 2025-08-18 08:17 by admin (admin)
2025-08-18 01:32:36,239 - apps.reports.signals - INFO - New report template created: Customer Statement
2025-08-18 01:32:36,244 - apps.reports.signals - INFO - New report template created: Balance Summary Report
2025-08-18 01:32:36,250 - apps.reports.signals - INFO - New report template created: Transaction Report
2025-08-18 01:32:36,282 - apps.reports.signals - INFO - New report template created: Daily Summary Report
2025-08-18 01:32:36,289 - apps.reports.signals - INFO - Report template updated: Profit & Loss Report
2025-08-18 01:44:18,523 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-18 01:44:19,005 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-18 01:44:19,009 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-18 01:44
2025-08-18 01:44:19,010 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-18 01:44 - 2025-08-18 08:44 by admin (admin)
2025-08-18 01:44:30,292 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-18 01:44:30,293 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-18 01:44 (download #1)
2025-08-18 01:44:30,298 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-18 01:44 by admin (admin)
2025-08-18 01:56:36,958 - apps.reports.signals - INFO - Report template updated: Customer Statement
2025-08-18 01:56:36,963 - apps.reports.signals - INFO - Report template updated: Balance Summary Report
2025-08-18 01:56:36,969 - apps.reports.signals - INFO - Report template updated: Transaction Report
2025-08-18 01:56:36,974 - apps.reports.signals - INFO - Report template updated: Daily Summary Report
2025-08-18 01:56:36,999 - apps.reports.signals - INFO - Report template updated: Profit & Loss Report
2025-08-18 02:02:57,761 - apps.reports.signals - INFO - Report template updated: Customer Statement
2025-08-18 02:02:57,769 - apps.reports.signals - INFO - Report template updated: Balance Summary Report
2025-08-18 02:02:57,774 - apps.reports.signals - INFO - Report template updated: Transaction Report
2025-08-18 02:02:57,780 - apps.reports.signals - INFO - Report template updated: Daily Summary Report
2025-08-18 02:02:57,788 - apps.reports.signals - INFO - Report template updated: Profit & Loss Report
2025-08-18 02:24:39,501 - apps.reports.signals - INFO - New report generated: Customer Statement
2025-08-18 02:24:39,505 - apps.reports.views - ERROR - Error generating customer statement: Customer ID is required for customer statement
2025-08-18 02:24:39,694 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-18 02:24:39,696 - apps.reports.models - INFO - Report generation completed: Customer Statement - 2025-08-18 02:24
2025-08-18 02:24:39,696 - apps.reports.views - INFO - Report generation completed: Customer Statement - 2025-08-18 02:24 - 2025-08-18 09:24 by admin (admin)
2025-08-18 02:24:45,942 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-18 02:24:45,944 - apps.reports.models - INFO - Report downloaded: Customer Statement - 2025-08-18 02:24 (download #1)
2025-08-18 02:24:45,948 - apps.reports.views - INFO - Report downloaded: Customer Statement - 2025-08-18 02:24 by admin (admin)
2025-08-18 02:24:55,494 - apps.reports.signals - INFO - New report generated: Profit & Loss Report
2025-08-18 02:24:55,507 - apps.reports.signals - INFO - Generated report updated: Profit & Loss Report
2025-08-18 02:24:55,508 - apps.reports.models - INFO - Report generation completed: Profit & Loss Report - 2025-08-18 02:24
2025-08-18 02:24:55,509 - apps.reports.views - INFO - Report generation completed: Profit & Loss Report - 2025-08-18 02:24 - 2025-08-18 09:24 by admin (admin)
2025-08-18 02:25:00,213 - apps.reports.signals - INFO - Generated report updated: Profit & Loss Report
2025-08-18 02:25:00,214 - apps.reports.models - INFO - Report downloaded: Profit & Loss Report - 2025-08-18 02:24 (download #1)
2025-08-18 02:25:00,219 - apps.reports.views - INFO - Report downloaded: Profit & Loss Report - 2025-08-18 02:24 by admin (admin)
2025-08-18 02:31:28,406 - apps.reports.signals - INFO - New report generated: Customer Statement
2025-08-18 02:31:28,415 - apps.reports.views - ERROR - Error generating customer statement: time data '' does not match format '%Y-%m-%d'
2025-08-18 02:31:28,623 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-18 02:31:28,625 - apps.reports.models - INFO - Report generation completed: Customer Statement - 2025-08-18 02:31
2025-08-18 02:31:28,625 - apps.reports.views - INFO - Report generation completed: Customer Statement - 2025-08-18 02:31 - 2025-08-18 09:31 by admin (admin)
2025-08-18 02:31:33,925 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-18 02:31:33,927 - apps.reports.models - INFO - Report downloaded: Customer Statement - 2025-08-18 02:31 (download #1)
2025-08-18 02:31:33,931 - apps.reports.views - INFO - Report downloaded: Customer Statement - 2025-08-18 02:31 by admin (admin)
2025-08-18 02:46:24,437 - apps.reports.signals - INFO - New report generated: Customer Statement
2025-08-18 02:46:24,682 - apps.reports.views - INFO - Date range: 2025-07-19 to 2025-08-18
2025-08-18 02:46:24,684 - apps.reports.views - INFO - Found 4 transactions
2025-08-18 02:46:24,686 - apps.reports.views - INFO - Starting PDF generation for customer statement
2025-08-18 02:46:24,732 - apps.reports.views - INFO - Transaction count: 4
2025-08-18 02:46:24,732 - apps.reports.views - INFO - Date range: 2025-07-19 to 2025-08-18
2025-08-18 02:46:24,903 - apps.reports.views - INFO - PDF generated at: C:\Users\<USER>\AppData\Local\Temp\2\tmpn46fg2t9.pdf
2025-08-18 02:46:24,903 - apps.reports.views - INFO - PDF file size: 4830 bytes
2025-08-18 02:46:24,908 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-18 02:46:24,908 - apps.reports.models - INFO - Report generation completed: Customer Statement - 2025-08-18 02:46
2025-08-18 02:46:24,909 - apps.reports.views - INFO - Report generation completed: Customer Statement - 2025-08-18 02:46 - 2025-08-18 09:46 by admin (admin)
2025-08-18 02:46:27,758 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-18 02:46:27,760 - apps.reports.models - INFO - Report downloaded: Customer Statement - 2025-08-18 02:46 (download #1)
2025-08-18 02:46:27,765 - apps.reports.views - INFO - Report downloaded: Customer Statement - 2025-08-18 02:46 by admin (admin)
2025-08-18 02:46:48,196 - apps.reports.signals - INFO - New report generated: Profit & Loss Report
2025-08-18 02:46:48,218 - apps.reports.signals - INFO - Generated report updated: Profit & Loss Report
2025-08-18 02:46:48,220 - apps.reports.models - INFO - Report generation completed: Profit & Loss Report - 2025-08-18 02:46
2025-08-18 02:46:48,220 - apps.reports.views - INFO - Report generation completed: Profit & Loss Report - 2025-08-18 02:46 - 2025-08-18 09:46 by admin (admin)
2025-08-18 02:46:51,284 - apps.reports.signals - INFO - Generated report updated: Profit & Loss Report
2025-08-18 02:46:51,285 - apps.reports.models - INFO - Report downloaded: Profit & Loss Report - 2025-08-18 02:46 (download #1)
2025-08-18 02:46:51,289 - apps.reports.views - INFO - Report downloaded: Profit & Loss Report - 2025-08-18 02:46 by admin (admin)
2025-08-18 02:49:51,144 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-18 02:49:51,145 - apps.reports.models - INFO - Report downloaded: Customer Statement - 2025-08-18 02:46 (download #2)
2025-08-18 02:49:51,152 - apps.reports.views - INFO - Report downloaded: Customer Statement - 2025-08-18 02:46 by admin (admin)
2025-08-18 03:01:32,864 - apps.reports.signals - INFO - New report generated: Transaction Summary Report
2025-08-18 03:01:32,872 - apps.reports.views - INFO - Report generation - Type: transaction_summary, Date range: 2025-07-19 to 2025-08-18
2025-08-18 03:01:32,873 - apps.reports.views - INFO - Generating transaction report with 9 transactions
2025-08-18 03:01:32,876 - apps.reports.views - INFO - Generating transaction report PDF with 9 transactions
2025-08-18 03:01:33,030 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-18 03:01:33,032 - apps.reports.models - INFO - Report generation completed: Transaction Summary Report - 2025-08-18 03:01
2025-08-18 03:01:33,032 - apps.reports.views - INFO - Report generation completed: Transaction Summary Report - 2025-08-18 03:01 - 2025-08-18 10:01 by admin (admin)
2025-08-18 03:01:40,749 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-18 03:01:40,751 - apps.reports.models - INFO - Report downloaded: Transaction Summary Report - 2025-08-18 03:01 (download #1)
2025-08-18 03:01:40,756 - apps.reports.views - INFO - Report downloaded: Transaction Summary Report - 2025-08-18 03:01 by admin (admin)
2025-08-18 03:01:51,731 - apps.reports.signals - INFO - New report generated: Balance Report Report
2025-08-18 03:01:51,738 - apps.reports.views - INFO - Report generation - Type: balance_report, Date range: 2025-07-19 to 2025-08-18
2025-08-18 03:01:51,739 - apps.reports.views - INFO - Generating balance report for date range 2025-07-19 to 2025-08-18
2025-08-18 03:01:51,757 - apps.reports.views - INFO - Generating balance report PDF with 12 balance entries
2025-08-18 03:01:51,768 - apps.reports.signals - INFO - Generated report updated: Balance Report Report
2025-08-18 03:01:51,770 - apps.reports.models - INFO - Report generation completed: Balance Report Report - 2025-08-18 03:01
2025-08-18 03:01:51,770 - apps.reports.views - INFO - Report generation completed: Balance Report Report - 2025-08-18 03:01 - 2025-08-18 10:01 by admin (admin)
2025-08-18 03:01:58,826 - apps.reports.signals - INFO - Generated report updated: Balance Report Report
2025-08-18 03:01:58,827 - apps.reports.models - INFO - Report downloaded: Balance Report Report - 2025-08-18 03:01 (download #1)
2025-08-18 03:01:58,831 - apps.reports.views - INFO - Report downloaded: Balance Report Report - 2025-08-18 03:01 by admin (admin)
2025-08-18 03:02:17,960 - apps.reports.signals - INFO - New report generated: Customer Report Report
2025-08-18 03:02:17,965 - apps.reports.views - INFO - Report generation - Type: customer_report, Date range: 2025-07-19 to 2025-08-18
2025-08-18 03:02:17,965 - apps.reports.views - INFO - Generating customer activity report
2025-08-18 03:02:17,987 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-18 03:02:17,989 - apps.reports.models - INFO - Report generation completed: Customer Report Report - 2025-08-18 03:02
2025-08-18 03:02:17,989 - apps.reports.views - INFO - Report generation completed: Customer Report Report - 2025-08-18 03:02 - 2025-08-18 10:02 by admin (admin)
2025-08-18 03:02:23,265 - apps.reports.signals - INFO - Generated report updated: Customer Report Report
2025-08-18 03:02:23,267 - apps.reports.models - INFO - Report downloaded: Customer Report Report - 2025-08-18 03:02 (download #1)
2025-08-18 03:02:23,271 - apps.reports.views - INFO - Report downloaded: Customer Report Report - 2025-08-18 03:02 by admin (admin)
2025-08-18 03:02:44,274 - apps.reports.signals - INFO - New report generated: Profit & Loss Report
2025-08-18 03:02:44,280 - apps.reports.views - INFO - Report generation - Type: profit_loss, Date range: 2025-07-19 to 2025-08-18
2025-08-18 03:02:44,280 - apps.reports.views - INFO - Generating profit & loss report
2025-08-18 03:02:44,280 - apps.reports.views - INFO - Generating profit & loss report for 2025-07-19 to 2025-08-18
2025-08-18 03:02:44,282 - apps.reports.views - INFO - Found 9 transactions for profit & loss analysis
2025-08-18 03:02:44,297 - apps.reports.signals - INFO - Generated report updated: Profit & Loss Report
2025-08-18 03:02:44,299 - apps.reports.models - INFO - Report generation completed: Profit & Loss Report - 2025-08-18 03:02
2025-08-18 03:02:44,300 - apps.reports.views - INFO - Report generation completed: Profit & Loss Report - 2025-08-18 03:02 - 2025-08-18 10:02 by admin (admin)
2025-08-18 03:02:48,046 - apps.reports.signals - INFO - Generated report updated: Profit & Loss Report
2025-08-18 03:02:48,048 - apps.reports.models - INFO - Report downloaded: Profit & Loss Report - 2025-08-18 03:02 (download #1)
2025-08-18 03:02:48,052 - apps.reports.views - INFO - Report downloaded: Profit & Loss Report - 2025-08-18 03:02 by admin (admin)
2025-08-18 05:35:26,527 - apps.reports.signals - INFO - New report generated: Transaction Summary Report
2025-08-18 05:35:26,539 - apps.reports.views - INFO - Report generation - Type: transaction_summary, Date range: 2025-07-19 to 2025-08-18
2025-08-18 05:35:26,542 - apps.reports.views - INFO - Generating transaction report with 9 transactions
2025-08-18 05:35:26,547 - apps.reports.views - INFO - Generating transaction report PDF with 9 transactions
2025-08-18 05:35:26,587 - apps.reports.signals - INFO - Generated report updated: Transaction Summary Report
2025-08-18 05:35:26,588 - apps.reports.models - INFO - Report generation completed: Transaction Summary Report - 2025-08-18 05:35
2025-08-18 05:35:26,589 - apps.reports.views - INFO - Report generation completed: Transaction Summary Report - 2025-08-18 05:35 - 2025-08-18 12:35 by admin (admin)
2025-08-18 05:35:44,978 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-19 22:12:37,350 - apps.reports.signals - INFO - New report generated: Customer Statement
2025-08-19 22:12:37,360 - apps.reports.views - INFO - Report generation - Type: customer_statement, Date range: 2025-07-21 to 2025-08-20
2025-08-19 22:12:37,360 - apps.reports.views - INFO - Generating customer statement
2025-08-19 22:12:37,520 - apps.reports.views - INFO - Date range: 2025-07-21 to 2025-08-20
2025-08-19 22:12:37,522 - apps.reports.views - INFO - Found 4 transactions
2025-08-19 22:12:37,531 - apps.reports.views - INFO - Starting PDF generation for customer statement
2025-08-19 22:12:37,581 - apps.reports.views - INFO - Transaction count: 4
2025-08-19 22:12:37,581 - apps.reports.views - INFO - Date range: 2025-07-21 to 2025-08-20
2025-08-19 22:12:38,034 - apps.reports.views - INFO - PDF generated at: C:\Users\<USER>\AppData\Local\Temp\2\tmpa5rqk127.pdf
2025-08-19 22:12:38,035 - apps.reports.views - INFO - PDF file size: 4828 bytes
2025-08-19 22:12:38,039 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-19 22:12:38,041 - apps.reports.models - INFO - Report generation completed: Customer Statement - 2025-08-19 22:12
2025-08-19 22:12:38,041 - apps.reports.views - INFO - Report generation completed: Customer Statement - 2025-08-19 22:12 - 2025-08-20 05:12 by admin (admin)
2025-08-19 22:12:48,063 - apps.reports.signals - INFO - Generated report updated: Customer Statement
2025-08-19 22:12:48,064 - apps.reports.models - INFO - Report downloaded: Customer Statement - 2025-08-19 22:12 (download #1)
2025-08-19 22:12:48,068 - apps.reports.views - INFO - Report downloaded: Customer Statement - 2025-08-19 22:12 by admin (admin)
