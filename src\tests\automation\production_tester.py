"""
Arena Doviz Production Environment Testing System
"""
import asyncio
import json
import logging
import requests
import subprocess
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

from browser_automation import BrowserAutomation, TransactionFormAutomation
from system_monitor import SystemMonitor
from test_scenarios import TestScenarioGenerator, TestScenarioExecutor
from financial_verification import FinancialVerificationSystem
from test_data_manager import TestDataManager
from issue_detector import IssueDetector
from config import TestConfig, LOGS_DIR, REPORTS_DIR

logger = logging.getLogger("arena_production_tester")

@dataclass
class ProductionTestResult:
    """Production test execution result"""
    test_name: str
    status: str  # 'PASS', 'FAIL', 'ERROR'
    start_time: datetime
    end_time: datetime
    duration_ms: int
    environment_info: Dict[str, Any]
    service_health: Dict[str, Any]
    scenario_results: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]
    issues_detected: List[Dict[str, Any]]
    financial_verification: Dict[str, Any]

class ProductionEnvironmentTester:
    """Comprehensive production environment testing system"""
    
    def __init__(self):
        self.production_url = TestConfig.PRODUCTION_URL
        self.test_results: List[ProductionTestResult] = []
        self.browser_automation: Optional[BrowserAutomation] = None
        self.system_monitor: Optional[SystemMonitor] = None
        self.scenario_executor: Optional[TestScenarioExecutor] = None
        self.financial_verifier: Optional[FinancialVerificationSystem] = None
        self.test_data_manager: Optional[TestDataManager] = None
        self.issue_detector: Optional[IssueDetector] = None
    
    async def run_production_tests(self) -> ProductionTestResult:
        """Run comprehensive production environment tests"""
        logger.info("🚀 Starting production environment testing...")
        
        start_time = datetime.now()
        test_result = ProductionTestResult(
            test_name="Arena Doviz Production Test Suite",
            status="RUNNING",
            start_time=start_time,
            end_time=start_time,
            duration_ms=0,
            environment_info={},
            service_health={},
            scenario_results=[],
            performance_metrics={},
            issues_detected=[],
            financial_verification={}
        )
        
        try:
            # Initialize all components
            await self._initialize_components()
            
            # Step 1: Environment verification
            logger.info("🔍 Step 1: Verifying production environment...")
            test_result.environment_info = await self._verify_environment()
            
            # Step 2: Service health checks
            logger.info("🏥 Step 2: Checking service health...")
            test_result.service_health = await self._check_service_health()
            
            # Step 3: Deployment verification
            logger.info("📦 Step 3: Verifying deployment...")
            deployment_status = await self._verify_deployment()
            
            # Step 4: Database connectivity and data integrity
            logger.info("🗄️ Step 4: Checking database connectivity...")
            database_status = await self._check_database_status()
            
            # Step 5: Seed test data
            logger.info("🌱 Step 5: Seeding test data...")
            data_seeding_status = await self._seed_test_data()
            
            # Step 6: Execute test scenarios
            logger.info("🧪 Step 6: Executing test scenarios...")
            test_result.scenario_results = await self._execute_test_scenarios()
            
            # Step 7: Financial verification
            logger.info("💰 Step 7: Verifying financial accuracy...")
            test_result.financial_verification = await self._verify_financial_accuracy()
            
            # Step 8: Performance analysis
            logger.info("📊 Step 8: Analyzing performance...")
            test_result.performance_metrics = await self._analyze_performance()
            
            # Step 9: Issue detection
            logger.info("🔍 Step 9: Detecting issues...")
            test_result.issues_detected = await self._detect_issues()
            
            # Step 10: Cleanup
            logger.info("🧹 Step 10: Cleaning up test data...")
            await self._cleanup_test_data()
            
            # Determine overall status
            test_result.status = self._determine_overall_status(test_result)
            
        except Exception as e:
            test_result.status = "ERROR"
            logger.error(f"💥 Production testing failed: {str(e)}")
        
        finally:
            # Cleanup components
            await self._cleanup_components()
            
            # Calculate duration
            end_time = datetime.now()
            test_result.end_time = end_time
            test_result.duration_ms = int((end_time - start_time).total_seconds() * 1000)
            
            self.test_results.append(test_result)
        
        logger.info(f"✅ Production testing completed with status: {test_result.status}")
        return test_result
    
    async def _initialize_components(self) -> None:
        """Initialize all testing components"""
        # Set environment to production
        TestConfig.ENVIRONMENT = 'production'
        TestConfig.BASE_URL = self.production_url
        
        # Initialize browser automation
        self.browser_automation = BrowserAutomation()
        await self.browser_automation.setup()
        
        # Initialize system monitor
        self.system_monitor = SystemMonitor()
        self.system_monitor.start_monitoring()
        
        # Initialize other components
        self.scenario_executor = TestScenarioExecutor(self.browser_automation, self.system_monitor)
        self.financial_verifier = FinancialVerificationSystem()
        self.test_data_manager = TestDataManager()
        self.issue_detector = IssueDetector()
        
        logger.info("✅ All testing components initialized")
    
    async def _verify_environment(self) -> Dict[str, Any]:
        """Verify production environment configuration"""
        environment_info = {
            'url': self.production_url,
            'timestamp': datetime.now().isoformat(),
            'accessibility': False,
            'ssl_certificate': False,
            'response_time_ms': 0,
            'server_info': {},
            'static_files': False
        }
        
        try:
            # Check basic accessibility
            start_time = time.time()
            response = requests.get(self.production_url, timeout=30)
            response_time = (time.time() - start_time) * 1000
            
            environment_info['accessibility'] = response.status_code == 200
            environment_info['response_time_ms'] = response_time
            environment_info['server_info'] = dict(response.headers)
            
            # Check SSL certificate
            environment_info['ssl_certificate'] = self.production_url.startswith('https')
            
            # Check static files
            static_file_url = f"{self.production_url}/static/css/style.css"
            static_response = requests.get(static_file_url, timeout=10)
            environment_info['static_files'] = static_response.status_code == 200
            
            logger.info(f"✅ Environment verification completed - Response time: {response_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"❌ Environment verification failed: {str(e)}")
            environment_info['error'] = str(e)
        
        return environment_info
    
    async def _check_service_health(self) -> Dict[str, Any]:
        """Check health of all services"""
        health_status = {
            'web_server': False,
            'database': False,
            'api_endpoints': {},
            'authentication': False,
            'static_files': False
        }
        
        try:
            # Check web server
            response = requests.get(f"{self.production_url}/health/", timeout=10)
            health_status['web_server'] = response.status_code == 200
            
            # Check key API endpoints
            api_endpoints = [
                '/api/v1/customers/customers/',
                '/api/v1/transactions/transactions/',
                '/api/v1/currencies/currencies/',
                '/api/v1/locations/locations/',
                '/api/v1/currencies/rates/current/'
            ]
            
            for endpoint in api_endpoints:
                try:
                    api_response = requests.get(f"{self.production_url}{endpoint}", timeout=10)
                    health_status['api_endpoints'][endpoint] = {
                        'status': api_response.status_code,
                        'accessible': api_response.status_code in [200, 401, 403]  # 401/403 means endpoint exists
                    }
                except Exception as e:
                    health_status['api_endpoints'][endpoint] = {
                        'status': 0,
                        'accessible': False,
                        'error': str(e)
                    }
            
            # Check authentication
            try:
                auth_response = requests.post(f"{self.production_url}/api/v1/auth/token/", {
                    'username': TestConfig.TEST_USERNAME,
                    'password': TestConfig.TEST_PASSWORD
                }, timeout=10)
                health_status['authentication'] = auth_response.status_code == 200
            except Exception as e:
                health_status['authentication'] = False
            
            logger.info("✅ Service health check completed")
            
        except Exception as e:
            logger.error(f"❌ Service health check failed: {str(e)}")
            health_status['error'] = str(e)
        
        return health_status
    
    async def _verify_deployment(self) -> Dict[str, Any]:
        """Verify deployment status and version"""
        deployment_info = {
            'version': 'unknown',
            'deployment_time': 'unknown',
            'git_commit': 'unknown',
            'static_files_updated': False,
            'database_migrations': False
        }
        
        try:
            # Check version endpoint if available
            try:
                version_response = requests.get(f"{self.production_url}/api/v1/version/", timeout=10)
                if version_response.status_code == 200:
                    version_data = version_response.json()
                    deployment_info.update(version_data)
            except:
                pass
            
            # Check if static files are up to date
            # This could check file timestamps or version hashes
            deployment_info['static_files_updated'] = True  # Placeholder
            
            # Check database migrations
            # This would typically check a migrations table
            deployment_info['database_migrations'] = True  # Placeholder
            
            logger.info("✅ Deployment verification completed")
            
        except Exception as e:
            logger.error(f"❌ Deployment verification failed: {str(e)}")
            deployment_info['error'] = str(e)
        
        return deployment_info
    
    async def _check_database_status(self) -> Dict[str, Any]:
        """Check database connectivity and basic data integrity"""
        database_status = {
            'connectivity': False,
            'tables_exist': False,
            'data_integrity': False,
            'performance': {}
        }
        
        try:
            # Check basic connectivity through API
            response = requests.get(f"{self.production_url}/api/v1/currencies/currencies/", timeout=10)
            database_status['connectivity'] = response.status_code in [200, 401, 403]
            
            if database_status['connectivity']:
                # Check if basic data exists
                auth_headers = self._get_auth_headers()
                if auth_headers:
                    # Check currencies
                    currencies_response = requests.get(
                        f"{self.production_url}/api/v1/currencies/currencies/",
                        headers=auth_headers,
                        timeout=10
                    )
                    
                    # Check locations
                    locations_response = requests.get(
                        f"{self.production_url}/api/v1/locations/locations/",
                        headers=auth_headers,
                        timeout=10
                    )
                    
                    database_status['tables_exist'] = (
                        currencies_response.status_code == 200 and
                        locations_response.status_code == 200
                    )
                    
                    if database_status['tables_exist']:
                        currencies_data = currencies_response.json()
                        locations_data = locations_response.json()
                        
                        database_status['data_integrity'] = (
                            len(currencies_data.get('results', [])) >= 3 and  # USD, AED, IRR
                            len(locations_data.get('results', [])) >= 5       # 5 locations
                        )
            
            logger.info("✅ Database status check completed")
            
        except Exception as e:
            logger.error(f"❌ Database status check failed: {str(e)}")
            database_status['error'] = str(e)
        
        return database_status
    
    async def _seed_test_data(self) -> bool:
        """Seed test data for production testing"""
        try:
            if self.test_data_manager:
                return await self.test_data_manager.seed_test_data()
            return False
        except Exception as e:
            logger.error(f"❌ Test data seeding failed: {str(e)}")
            return False
    
    async def _execute_test_scenarios(self) -> List[Dict[str, Any]]:
        """Execute critical test scenarios"""
        scenario_results = []
        
        try:
            if not self.scenario_executor:
                return scenario_results
            
            # Login to the system
            login_success = await self.browser_automation.login()
            if not login_success:
                logger.error("❌ Failed to login to production system")
                return scenario_results
            
            # Generate test scenarios
            scenario_generator = TestScenarioGenerator()
            
            # Execute high-priority scenarios only in production
            high_priority_scenarios = scenario_generator.get_scenarios_by_priority(1)
            
            # Limit to critical scenarios for production
            critical_scenarios = high_priority_scenarios[:10]  # Limit to 10 scenarios
            
            for scenario in critical_scenarios:
                try:
                    result = await self.scenario_executor.execute_scenario(scenario)
                    scenario_results.append(asdict(result))
                    
                    # Small delay between scenarios
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.error(f"❌ Scenario execution failed: {scenario.name} - {str(e)}")
                    scenario_results.append({
                        'scenario_name': scenario.name,
                        'status': 'ERROR',
                        'error': str(e)
                    })
            
            logger.info(f"✅ Executed {len(scenario_results)} test scenarios")
            
        except Exception as e:
            logger.error(f"❌ Test scenario execution failed: {str(e)}")
        
        return scenario_results
    
    async def _verify_financial_accuracy(self) -> Dict[str, Any]:
        """Verify financial accuracy of transactions"""
        financial_results = {
            'verifications_performed': 0,
            'accuracy_rate': 0,
            'issues_found': []
        }
        
        try:
            if not self.financial_verifier:
                return financial_results
            
            # Get recent transactions for verification
            auth_headers = self._get_auth_headers()
            if auth_headers:
                response = requests.get(
                    f"{self.production_url}/api/v1/transactions/transactions/?limit=5",
                    headers=auth_headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    transactions = response.json().get('results', [])
                    
                    for transaction in transactions:
                        transaction_id = transaction.get('id')
                        if transaction_id:
                            verification = await self.financial_verifier.verify_transaction(transaction_id)
                            financial_results['verifications_performed'] += 1
                            
                            if verification.status != 'VERIFIED':
                                financial_results['issues_found'].append(asdict(verification))
                    
                    # Calculate accuracy rate
                    if financial_results['verifications_performed'] > 0:
                        verified_count = financial_results['verifications_performed'] - len(financial_results['issues_found'])
                        financial_results['accuracy_rate'] = (verified_count / financial_results['verifications_performed']) * 100
            
            logger.info(f"✅ Financial verification completed - {financial_results['verifications_performed']} transactions verified")
            
        except Exception as e:
            logger.error(f"❌ Financial verification failed: {str(e)}")
            financial_results['error'] = str(e)
        
        return financial_results
    
    async def _analyze_performance(self) -> Dict[str, Any]:
        """Analyze system performance during testing"""
        performance_metrics = {}
        
        try:
            if self.system_monitor:
                performance_metrics = self.system_monitor.get_performance_summary(duration_minutes=30)
            
            # Add browser performance metrics
            if self.browser_automation:
                browser_summary = self.browser_automation.get_test_summary()
                performance_metrics['browser_tests'] = browser_summary
            
            logger.info("✅ Performance analysis completed")
            
        except Exception as e:
            logger.error(f"❌ Performance analysis failed: {str(e)}")
            performance_metrics['error'] = str(e)
        
        return performance_metrics
    
    async def _detect_issues(self) -> List[Dict[str, Any]]:
        """Detect issues from logs and test results"""
        detected_issues = []
        
        try:
            if self.issue_detector and self.system_monitor:
                # Analyze system logs
                log_entries = self.system_monitor.log_entries
                issues = await self.issue_detector.analyze_logs([asdict(log) for log in log_entries])
                
                detected_issues = [asdict(issue) for issue in issues]
            
            logger.info(f"✅ Issue detection completed - {len(detected_issues)} issues found")
            
        except Exception as e:
            logger.error(f"❌ Issue detection failed: {str(e)}")
        
        return detected_issues
    
    async def _cleanup_test_data(self) -> bool:
        """Clean up test data"""
        try:
            if self.test_data_manager:
                return await self.test_data_manager.cleanup_test_data()
            return True
        except Exception as e:
            logger.error(f"❌ Test data cleanup failed: {str(e)}")
            return False
    
    async def _cleanup_components(self) -> None:
        """Cleanup all testing components"""
        try:
            if self.browser_automation:
                await self.browser_automation.cleanup()
            
            if self.system_monitor:
                self.system_monitor.stop_monitoring()
            
            logger.info("✅ Component cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Component cleanup failed: {str(e)}")
    
    def _get_auth_headers(self) -> Optional[Dict[str, str]]:
        """Get authentication headers"""
        try:
            response = requests.post(f"{self.production_url}/api/v1/auth/token/", {
                'username': TestConfig.TEST_USERNAME,
                'password': TestConfig.TEST_PASSWORD
            }, timeout=10)
            
            if response.status_code == 200:
                token = response.json().get('access', '')
                return {
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json'
                }
        except Exception as e:
            logger.error(f"Failed to get auth headers: {str(e)}")
        
        return None
    
    def _determine_overall_status(self, test_result: ProductionTestResult) -> str:
        """Determine overall test status"""
        # Check critical failures
        if not test_result.environment_info.get('accessibility', False):
            return 'FAIL'
        
        if not test_result.service_health.get('web_server', False):
            return 'FAIL'
        
        # Check scenario results
        scenario_failures = [r for r in test_result.scenario_results if r.get('status') == 'FAIL']
        if len(scenario_failures) > len(test_result.scenario_results) * 0.5:  # More than 50% failed
            return 'FAIL'
        
        # Check for critical issues
        critical_issues = [i for i in test_result.issues_detected if i.get('severity') == 'CRITICAL']
        if len(critical_issues) > 0:
            return 'FAIL'
        
        return 'PASS'
    
    def export_production_report(self, filepath: Path) -> None:
        """Export comprehensive production test report"""
        if not self.test_results:
            logger.warning("No test results to export")
            return
        
        latest_result = self.test_results[-1]
        
        report = {
            'report_timestamp': datetime.now().isoformat(),
            'production_url': self.production_url,
            'test_summary': {
                'status': latest_result.status,
                'duration_ms': latest_result.duration_ms,
                'start_time': latest_result.start_time.isoformat(),
                'end_time': latest_result.end_time.isoformat()
            },
            'environment_verification': latest_result.environment_info,
            'service_health': latest_result.service_health,
            'scenario_results': latest_result.scenario_results,
            'performance_metrics': latest_result.performance_metrics,
            'financial_verification': latest_result.financial_verification,
            'issues_detected': latest_result.issues_detected,
            'recommendations': self._generate_recommendations(latest_result)
        }
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📊 Production test report exported to {filepath}")
    
    def _generate_recommendations(self, test_result: ProductionTestResult) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Environment recommendations
        if test_result.environment_info.get('response_time_ms', 0) > 5000:
            recommendations.append("Consider optimizing server response time (currently > 5 seconds)")
        
        # Service health recommendations
        failed_endpoints = [
            endpoint for endpoint, status in test_result.service_health.get('api_endpoints', {}).items()
            if not status.get('accessible', False)
        ]
        if failed_endpoints:
            recommendations.append(f"Fix inaccessible API endpoints: {', '.join(failed_endpoints)}")
        
        # Performance recommendations
        performance = test_result.performance_metrics
        if performance.get('cpu_usage', {}).get('average', 0) > 80:
            recommendations.append("High CPU usage detected - consider scaling or optimization")
        
        if performance.get('memory_usage', {}).get('average', 0) > 80:
            recommendations.append("High memory usage detected - check for memory leaks")
        
        # Financial accuracy recommendations
        financial = test_result.financial_verification
        if financial.get('accuracy_rate', 100) < 95:
            recommendations.append("Financial accuracy below 95% - review transaction processing logic")
        
        # Issue-based recommendations
        critical_issues = [i for i in test_result.issues_detected if i.get('severity') == 'CRITICAL']
        if critical_issues:
            recommendations.append(f"Address {len(critical_issues)} critical issues immediately")
        
        return recommendations
