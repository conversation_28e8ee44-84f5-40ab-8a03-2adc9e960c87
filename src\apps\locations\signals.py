"""
Signal handlers for Arena Doviz Locations app.
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Location
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Location)
def location_post_save(sender, instance, created, **kwargs):
    """Handle location post-save signal."""
    if created:
        logger.info(f"New location created: {instance.code}")
    else:
        logger.info(f"Location updated: {instance.code}")


@receiver(post_delete, sender=Location)
def location_post_delete(sender, instance, **kwargs):
    """Handle location post-delete signal."""
    logger.info(f"Location deleted: {instance.code}")
