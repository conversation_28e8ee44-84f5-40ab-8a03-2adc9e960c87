# Generated by Django 4.2.23 on 2025-08-13 08:44

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Location',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('name', models.CharField(help_text='Name of the location (e.g., Istanbul, Tabriz)', max_length=100, verbose_name='Location name')),
                ('code', models.CharField(help_text='Unique code for the location (e.g., IST, TBZ)', max_length=10, unique=True, validators=[django.core.validators.RegexValidator(message='Location code must be 2-10 uppercase letters', regex='^[A-Z]{2,10}$')], verbose_name='Location code')),
                ('country', models.CharField(help_text='Country where the location is situated', max_length=100, verbose_name='Country')),
                ('city', models.CharField(help_text='City where the location is situated', max_length=100, verbose_name='City')),
                ('address', models.TextField(blank=True, help_text='Full address of the location', verbose_name='Address')),
                ('phone_number', models.CharField(blank=True, help_text='Contact phone number for the location', max_length=20, validators=[django.core.validators.RegexValidator(message='Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.', regex='^\\+?1?\\d{9,15}$')], verbose_name='Phone number')),
                ('email', models.EmailField(blank=True, help_text='Contact email for the location', max_length=254, verbose_name='Email')),
                ('timezone', models.CharField(default='Asia/Tehran', help_text='Timezone for the location (e.g., Asia/Tehran, Europe/Istanbul)', max_length=50, verbose_name='Timezone')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this location is currently active', verbose_name='Is active')),
                ('is_main_office', models.BooleanField(default=False, help_text='Whether this is the main office location', verbose_name='Is main office')),
                ('commission_rate', models.DecimalField(decimal_places=4, default=0.01, help_text='Default commission rate for transactions at this location', max_digits=5, verbose_name='Commission rate')),
                ('min_transaction_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Minimum amount for transactions at this location', max_digits=15, verbose_name='Minimum transaction amount')),
                ('max_transaction_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum amount for transactions at this location', max_digits=15, null=True, verbose_name='Maximum transaction amount')),
                ('opening_time', models.TimeField(blank=True, help_text='Daily opening time', null=True, verbose_name='Opening time')),
                ('closing_time', models.TimeField(blank=True, help_text='Daily closing time', null=True, verbose_name='Closing time')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this location', verbose_name='Notes')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Order in which locations should be displayed', verbose_name='Sort order')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('manager', models.ForeignKey(blank=True, help_text='User responsible for managing this location', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_locations', to=settings.AUTH_USER_MODEL, verbose_name='Location manager')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Location',
                'verbose_name_plural': 'Locations',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='LocationSettings',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('whatsapp_enabled', models.BooleanField(default=True, help_text='Whether WhatsApp integration is enabled for this location', verbose_name='WhatsApp enabled')),
                ('whatsapp_group_id', models.CharField(blank=True, help_text='WhatsApp group ID for notifications', max_length=100, verbose_name='WhatsApp group ID')),
                ('email_notifications', models.BooleanField(default=True, help_text='Whether to send email notifications', verbose_name='Email notifications')),
                ('sms_notifications', models.BooleanField(default=False, help_text='Whether to send SMS notifications', verbose_name='SMS notifications')),
                ('require_approval_above', models.DecimalField(blank=True, decimal_places=2, help_text='Transaction amount above which approval is required', max_digits=15, null=True, verbose_name='Require approval above')),
                ('auto_approve_below', models.DecimalField(blank=True, decimal_places=2, help_text='Transaction amount below which auto-approval is enabled', max_digits=15, null=True, verbose_name='Auto approve below')),
                ('ip_whitelist', models.TextField(blank=True, help_text='Comma-separated list of allowed IP addresses', verbose_name='IP whitelist')),
                ('session_timeout', models.PositiveIntegerField(default=3600, help_text='Session timeout in seconds', verbose_name='Session timeout')),
                ('backup_enabled', models.BooleanField(default=True, help_text='Whether automatic backups are enabled', verbose_name='Backup enabled')),
                ('backup_frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], default='daily', help_text='How often to perform backups', max_length=20, verbose_name='Backup frequency')),
                ('additional_settings', models.JSONField(blank=True, default=dict, help_text='Additional location-specific settings', verbose_name='Additional settings')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('location', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='settings', to='locations.location', verbose_name='Location')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Location Settings',
                'verbose_name_plural': 'Location Settings',
            },
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['code'], name='locations_l_code_b786f2_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['is_active'], name='locations_l_is_acti_9f2958_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['is_main_office'], name='locations_l_is_main_70b038_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['country', 'city'], name='locations_l_country_857b42_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['sort_order'], name='locations_l_sort_or_56adc2_idx'),
        ),
        migrations.AddConstraint(
            model_name='location',
            constraint=models.UniqueConstraint(condition=models.Q(('is_main_office', True)), fields=('is_main_office',), name='unique_main_office'),
        ),
    ]
