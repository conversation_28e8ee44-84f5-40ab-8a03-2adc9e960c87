{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transaction Forms Test" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-check-circle"></i>
                {% trans "Transaction Forms Test" %}
            </h1>
            <a href="{% url 'transactions_web:navigation' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {% trans "Back to Transactions" %}
            </a>
        </div>
    </div>
</div>

<!-- Test Results -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clipboard-check"></i>
                    {% trans "Test Results" %}
                </h5>
            </div>
            <div class="card-body">
                <div id="test-results">
                    <div class="text-muted text-center py-4">
                        <i class="bi bi-hourglass-split"></i>
                        {% trans "Click 'Run Tests' to start testing transaction forms" %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Controls -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-play-circle"></i>
                    {% trans "Test Controls" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                    <button type="button" class="btn btn-primary" id="run-all-tests">
                        <i class="bi bi-play-fill"></i>
                        {% trans "Run All Tests" %}
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="test-customer-loading">
                        <i class="bi bi-people"></i>
                        {% trans "Test Customer Loading" %}
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="test-currency-loading">
                        <i class="bi bi-currency-exchange"></i>
                        {% trans "Test Currency Loading" %}
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="test-document-upload">
                        <i class="bi bi-file-earmark-arrow-up"></i>
                        {% trans "Test Document Upload" %}
                    </button>
                    <button type="button" class="btn btn-outline-danger" id="clear-results">
                        <i class="bi bi-trash"></i>
                        {% trans "Clear Results" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Type Links -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-link-45deg"></i>
                    {% trans "Quick Access to Transaction Forms" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{% url 'transactions_web:type_add' 'EXCHANGE' %}" class="btn btn-outline-primary w-100" target="_blank">
                            <i class="bi bi-arrow-left-right"></i>
                            {% trans "Currency Exchange" %}
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{% url 'transactions_web:type_add' 'DEPOSIT' %}" class="btn btn-outline-primary w-100" target="_blank">
                            <i class="bi bi-cash-coin"></i>
                            {% trans "Cash Deposit" %}
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{% url 'transactions_web:type_add' 'WITHDRAWAL' %}" class="btn btn-outline-primary w-100" target="_blank">
                            <i class="bi bi-cash-stack"></i>
                            {% trans "Cash Withdrawal" %}
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{% url 'transactions_web:type_add' 'REMITTANCE' %}" class="btn btn-outline-primary w-100" target="_blank">
                            <i class="bi bi-globe"></i>
                            {% trans "Remittance" %}
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{% url 'transactions_web:type_add' 'ADJUSTMENT' %}" class="btn btn-outline-primary w-100" target="_blank">
                            <i class="bi bi-calculator"></i>
                            {% trans "Balance Adjustment" %}
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{% url 'transactions_web:internal_transfer_add' %}" class="btn btn-outline-primary w-100" target="_blank">
                            <i class="bi bi-arrow-left-right"></i>
                            {% trans "Internal Transfer" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% url 'javascript-catalog' %}"></script>
<script src="/static/js/transactions/common.js"></script>
<script>
class TransactionFormTester {
    constructor() {
        this.results = [];
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        $('#run-all-tests').on('click', () => {
            this.runAllTests();
        });

        $('#test-customer-loading').on('click', () => {
            this.testCustomerLoading();
        });

        $('#test-currency-loading').on('click', () => {
            this.testCurrencyLoading();
        });

        $('#test-document-upload').on('click', () => {
            this.testDocumentUpload();
        });

        $('#clear-results').on('click', () => {
            this.clearResults();
        });
    }

    async runAllTests() {
        this.clearResults();
        this.addResult('INFO', 'Starting comprehensive transaction form tests...');

        await this.testCustomerLoading();
        await this.testCurrencyLoading();
        await this.testLocationLoading();
        await this.testCourierLoading();
        await this.testExchangeRates();
        await this.testDocumentUpload();

        this.addResult('SUCCESS', 'All tests completed!');
    }

    async testCustomerLoading() {
        this.addResult('INFO', 'Testing customer loading...');
        
        try {
            const data = await TransactionUtils.loadCustomers();
            if (data.results && data.results.length > 0) {
                this.addResult('SUCCESS', `Customer loading: Found ${data.results.length} customers`);
                
                // Test display names
                const customer = data.results[0];
                const displayName = customer.display_name || `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || customer.customer_code;
                this.addResult('SUCCESS', `Customer display name: "${displayName}"`);
            } else {
                this.addResult('WARNING', 'Customer loading: No customers found');
            }
        } catch (error) {
            this.addResult('ERROR', `Customer loading failed: ${error.message}`);
        }
    }

    async testCurrencyLoading() {
        this.addResult('INFO', 'Testing currency loading...');
        
        try {
            const data = await TransactionUtils.loadCurrencies();
            if (data.results && data.results.length > 0) {
                this.addResult('SUCCESS', `Currency loading: Found ${data.results.length} currencies`);
                
                // Test currency format
                const currency = data.results[0];
                this.addResult('SUCCESS', `Currency format: "${currency.code} - ${currency.name}"`);
            } else {
                this.addResult('WARNING', 'Currency loading: No currencies found');
            }
        } catch (error) {
            this.addResult('ERROR', `Currency loading failed: ${error.message}`);
        }
    }

    async testLocationLoading() {
        this.addResult('INFO', 'Testing location loading...');
        
        try {
            const data = await TransactionUtils.loadLocations();
            if (data.results && data.results.length > 0) {
                this.addResult('SUCCESS', `Location loading: Found ${data.results.length} locations`);
            } else {
                this.addResult('WARNING', 'Location loading: No locations found');
            }
        } catch (error) {
            this.addResult('ERROR', `Location loading failed: ${error.message}`);
        }
    }

    async testCourierLoading() {
        this.addResult('INFO', 'Testing courier loading...');
        
        try {
            const data = await TransactionUtils.loadCouriers();
            if (data.results && data.results.length > 0) {
                this.addResult('SUCCESS', `Courier loading: Found ${data.results.length} couriers`);
            } else {
                this.addResult('WARNING', 'Courier loading: No couriers found (this is normal if no courier users exist)');
            }
        } catch (error) {
            this.addResult('ERROR', `Courier loading failed: ${error.message}`);
        }
    }

    async testExchangeRates() {
        this.addResult('INFO', 'Testing exchange rates loading...');
        
        try {
            const data = await TransactionUtils.loadExchangeRates('IST'); // Test with Istanbul code
            if (data && data.length > 0) {
                this.addResult('SUCCESS', `Exchange rates: Found ${data.length} rates`);
            } else {
                this.addResult('WARNING', 'Exchange rates: No rates found for test location');
            }
        } catch (error) {
            this.addResult('ERROR', `Exchange rates loading failed: ${error.message}`);
        }
    }

    testDocumentUpload() {
        this.addResult('INFO', 'Testing document upload functionality...');
        
        // Create a test file
        const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
        
        // Test file selection handling
        try {
            // Simulate file selection
            const fileInput = $('<input type="file" id="document_files" multiple>');
            $('body').append(fileInput);
            
            // Test file handling
            TransactionUtils.handleFileSelection();
            
            fileInput.remove();
            this.addResult('SUCCESS', 'Document upload: File selection handling works');
        } catch (error) {
            this.addResult('ERROR', `Document upload test failed: ${error.message}`);
        }
    }

    addResult(type, message) {
        const timestamp = new Date().toLocaleTimeString();
        const result = {
            type: type,
            message: message,
            timestamp: timestamp
        };
        
        this.results.push(result);
        this.updateResultsDisplay();
    }

    updateResultsDisplay() {
        const container = $('#test-results');
        let html = '';
        
        this.results.forEach(result => {
            const alertClass = {
                'SUCCESS': 'alert-success',
                'ERROR': 'alert-danger',
                'WARNING': 'alert-warning',
                'INFO': 'alert-info'
            }[result.type] || 'alert-secondary';
            
            const icon = {
                'SUCCESS': 'bi-check-circle',
                'ERROR': 'bi-x-circle',
                'WARNING': 'bi-exclamation-triangle',
                'INFO': 'bi-info-circle'
            }[result.type] || 'bi-circle';
            
            html += `
                <div class="alert ${alertClass} alert-sm">
                    <i class="bi ${icon}"></i>
                    <strong>[${result.timestamp}]</strong> ${result.message}
                </div>
            `;
        });
        
        container.html(html);
        
        // Scroll to bottom
        container.scrollTop(container[0].scrollHeight);
    }

    clearResults() {
        this.results = [];
        $('#test-results').html(`
            <div class="text-muted text-center py-4">
                <i class="bi bi-clipboard"></i>
                Test results cleared
            </div>
        `);
    }
}

// Initialize when DOM is ready
$(document).ready(() => {
    new TransactionFormTester();
});
</script>

<style>
#test-results {
    max-height: 400px;
    overflow-y: auto;
}

.alert-sm {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}
</style>
{% endblock %}
