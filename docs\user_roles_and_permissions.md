# Arena Doviz User Roles and Permissions

## Overview

Arena Doviz implements a comprehensive role-based access control (RBAC) system with five distinct user roles, each with specific permissions and access levels tailored for exchange office operations.

## User Roles

### 1. Admin
**Role Code:** `admin`
**Description:** Full system administrator with complete access to all features and settings.

#### Permissions:
- ✅ **User Management:** Create, edit, delete users and assign roles
- ✅ **System Settings:** Configure system-wide settings and parameters
- ✅ **Transaction Management:** Create, approve, complete, and cancel transactions
- ✅ **Financial Operations:** All monetary operations without limits
- ✅ **Reports Access:** Generate and view all types of reports
- ✅ **Location Access:** View and manage all locations
- ✅ **Exchange Rate Management:** Set and approve exchange rates
- ✅ **Audit Logs:** View all system audit logs and user activities
- ✅ **Customer Management:** Full CRUD operations on customer data
- ✅ **Document Management:** Upload, view, and manage all documents

#### Transaction Limits:
- **No monetary limits** - Can process transactions of any amount
- **All currencies** - USD, AED, IRR access
- **All transaction types** - Buy, sell, exchange, transfer, remittance

#### System Access:
- **All modules** - Complete access to every system feature
- **All locations** - Can view and operate across all branches
- **Administrative panels** - Access to Django admin and system configuration

---

### 2. Accountant
**Role Code:** `accountant`
**Description:** Financial specialist responsible for transaction approval, reporting, and accounting operations.

#### Permissions:
- ❌ **User Management:** Cannot create or manage users
- ❌ **System Settings:** Cannot modify system configuration
- ✅ **Transaction Management:** Create, approve, complete transactions
- ✅ **Financial Operations:** High-value transaction processing
- ✅ **Reports Access:** Generate and view financial reports
- ✅ **Location Access:** View all locations, manage assigned location
- ✅ **Exchange Rate Management:** View rates, cannot set new rates
- ✅ **Audit Logs:** View transaction-related audit logs
- ✅ **Customer Management:** Full CRUD operations on customer data
- ✅ **Document Management:** Upload and manage transaction documents

#### Transaction Limits:
- **High monetary limits** - Can process large transactions (configurable)
- **All currencies** - USD, AED, IRR access
- **Most transaction types** - Buy, sell, exchange, transfer (remittance requires approval)

#### System Access:
- **Financial modules** - Transactions, reports, customer management
- **Multi-location view** - Can see data from all branches
- **Approval workflows** - Can approve transactions from branch employees

---

### 3. Branch Employee
**Role Code:** `branch_employee`
**Description:** Front-line staff handling daily customer transactions and operations.

#### Permissions:
- ❌ **User Management:** Cannot manage users
- ❌ **System Settings:** Cannot modify system settings
- ✅ **Transaction Management:** Create and complete transactions (approval required for high amounts)
- ✅ **Financial Operations:** Standard transaction processing
- ✅ **Reports Access:** View basic operational reports
- ✅ **Location Access:** Access only to assigned location
- ❌ **Exchange Rate Management:** View current rates only
- ✅ **Audit Logs:** View own transaction logs
- ✅ **Customer Management:** Create and edit customer information
- ✅ **Document Management:** Upload transaction documents

#### Transaction Limits:
- **Medium monetary limits** - Standard daily transaction limits (configurable)
- **All currencies** - USD, AED, IRR access
- **Standard transaction types** - Buy, sell, exchange (transfers require approval)

#### System Access:
- **Operational modules** - Transactions, customer service
- **Single location** - Access limited to assigned branch
- **Customer interface** - Primary point of contact for customers

---

### 4. Viewer
**Role Code:** `viewer`
**Description:** Read-only access for supervisors, auditors, or management oversight.

#### Permissions:
- ❌ **User Management:** Cannot manage users
- ❌ **System Settings:** Cannot modify settings
- ❌ **Transaction Management:** View-only access to transactions
- ❌ **Financial Operations:** Cannot process transactions
- ✅ **Reports Access:** View and generate reports
- ✅ **Location Access:** View data from all locations
- ❌ **Exchange Rate Management:** View rates only
- ✅ **Audit Logs:** View audit logs for oversight
- ✅ **Customer Management:** View customer information only
- ✅ **Document Management:** View documents only

#### Transaction Limits:
- **No transaction processing** - Read-only access
- **View all currencies** - Can see USD, AED, IRR data
- **No transaction creation** - Cannot initiate any transactions

#### System Access:
- **Reporting modules** - Comprehensive report access
- **All locations** - Can view data across all branches
- **Dashboard access** - Real-time system monitoring

---

### 5. Courier
**Role Code:** `courier`
**Description:** Delivery personnel for document and cash transport (limited system access).

#### Permissions:
- ❌ **User Management:** Cannot manage users
- ❌ **System Settings:** Cannot modify settings
- ✅ **Transaction Management:** Update delivery status only
- ❌ **Financial Operations:** Cannot process financial transactions
- ❌ **Reports Access:** No report access
- ✅ **Location Access:** View assigned delivery locations
- ❌ **Exchange Rate Management:** No access
- ✅ **Audit Logs:** View own delivery logs
- ❌ **Customer Management:** No customer data access
- ✅ **Document Management:** Confirm document delivery

#### Transaction Limits:
- **No financial operations** - Cannot handle money
- **No currency access** - Cannot view financial data
- **Delivery tracking only** - Update delivery status

#### System Access:
- **Delivery module** - Track and update deliveries
- **Mobile interface** - Optimized for mobile devices
- **Limited dashboard** - Delivery-focused view

## Permission Matrix

| Feature | Admin | Accountant | Branch Employee | Viewer | Courier |
|---------|-------|------------|-----------------|--------|---------|
| User Management | ✅ | ❌ | ❌ | ❌ | ❌ |
| System Settings | ✅ | ❌ | ❌ | ❌ | ❌ |
| Create Transactions | ✅ | ✅ | ✅ | ❌ | ❌ |
| Approve Transactions | ✅ | ✅ | ❌ | ❌ | ❌ |
| Complete Transactions | ✅ | ✅ | ✅ | ❌ | ❌ |
| View All Locations | ✅ | ✅ | ❌ | ✅ | ❌ |
| Generate Reports | ✅ | ✅ | ✅ | ✅ | ❌ |
| Manage Exchange Rates | ✅ | ❌ | ❌ | ❌ | ❌ |
| Customer CRUD | ✅ | ✅ | ✅ | View Only | ❌ |
| Document Management | ✅ | ✅ | ✅ | View Only | Delivery Only |

## Implementation Notes

### Authentication
- All roles require authentication (no anonymous access)
- JWT tokens used for API authentication
- Session-based authentication for web interface
- Failed login attempt tracking and account lockout

### Location-Based Access
- **Admin:** Can access all locations (system management)
- **Accountant:** Limited to assigned location (can approve transactions)
- **Branch Employee:** Limited to assigned location
- **Viewer:** Limited to assigned location (read-only)
- **Courier:** Access to delivery routes only

### Transaction Approval Workflow
1. **Branch Employee** creates transaction
2. **Accountant** or **Admin** approves (if required)
3. **Branch Employee**, **Accountant**, or **Admin** completes transaction

### Security Features
- Role-based middleware for API endpoints
- Template-level permission checks
- Audit logging for all user actions
- Encrypted sensitive data storage
- Regular permission validation

## Default Users Setup

Use the management command to create default users for testing:

```bash
python manage.py create_default_users
```

This creates:
- `admin` (Admin role)
- `accountant` (Accountant role) 
- `employee` (Branch Employee role)
- `viewer` (Viewer role)
- `courier` (Courier role)

**Note:** Change default passwords immediately in production environments.
