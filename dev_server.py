#!/usr/bin/env python
"""
Simple development server for Arena Doviz
"""
import os
import sys
import django
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
os.environ.setdefault('DEBUG', 'True')

# Setup Django
django.setup()

# Start development server
if __name__ == '__main__':
    from django.core.management import execute_from_command_line
    
    # Change to src directory
    os.chdir(src_path)
    
    print("Starting Arena Doviz development server...")
    print("Server will be accessible at: http://127.0.0.1:8000")
    
    # Run the development server
    execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8000'])
