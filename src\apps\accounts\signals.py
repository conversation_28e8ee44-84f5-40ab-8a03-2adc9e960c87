"""
Signal handlers for Arena Doviz Accounts app.
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from .models import User, UserSession, AuditLog
import logging

logger = logging.getLogger(__name__)


@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user login."""
    ip_address = get_client_ip(request)
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    # Create user session record
    session_key = request.session.session_key
    if session_key:
        UserSession.objects.create(
            user=user,
            session_key=session_key,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    # Update user's last login IP
    user.last_login_ip = ip_address
    user.is_active_session = True
    user.save(update_fields=['last_login_ip', 'is_active_session'])
    
    # Reset failed login attempts
    user.reset_failed_login_attempts()
    
    # Log the event
    AuditLog.log_action(
        user=user,
        action=AuditLog.Action.LOGIN,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    logger.info(f"User logged in: {user.username} from {ip_address}")


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout."""
    if user:
        ip_address = get_client_ip(request)
        
        # End user session
        session_key = request.session.session_key
        if session_key:
            try:
                session = UserSession.objects.get(
                    user=user,
                    session_key=session_key,
                    is_active=True
                )
                session.end_session()
            except UserSession.DoesNotExist:
                pass
        
        # Update user status
        user.is_active_session = False
        user.save(update_fields=['is_active_session'])
        
        # Log the event
        AuditLog.log_action(
            user=user,
            action=AuditLog.Action.LOGOUT,
            ip_address=ip_address
        )
        
        logger.info(f"User logged out: {user.username}")


@receiver(user_login_failed)
def log_failed_login(sender, credentials, request, **kwargs):
    """Log failed login attempts."""
    username = credentials.get('username', '')
    ip_address = get_client_ip(request)
    
    # Try to find the user and increment failed attempts
    try:
        user = User.objects.get(username=username)
        user.increment_failed_login_attempts()
        
        # Log the event
        AuditLog.log_action(
            user=user,
            action=AuditLog.Action.LOGIN,
            ip_address=ip_address,
            additional_data={'status': 'failed', 'reason': 'invalid_credentials'}
        )
        
        logger.warning(f"Failed login attempt for user: {username} from {ip_address}")
        
    except User.DoesNotExist:
        # Log attempt for non-existent user
        logger.warning(f"Failed login attempt for non-existent user: {username} from {ip_address}")


@receiver(pre_save, sender=User)
def log_user_changes(sender, instance, **kwargs):
    """Log user profile changes."""
    if instance.pk:  # Only for existing users
        try:
            old_instance = User.objects.get(pk=instance.pk)
            changes = {}
            
            # Track important field changes
            important_fields = ['role', 'is_active', 'location', 'email']
            for field in important_fields:
                old_value = getattr(old_instance, field)
                new_value = getattr(instance, field)
                if old_value != new_value:
                    changes[field] = {
                        'old': str(old_value),
                        'new': str(new_value)
                    }
            
            if changes:
                # This will be logged after save in post_save signal
                instance._pending_changes = changes
                
        except User.DoesNotExist:
            pass


@receiver(post_save, sender=User)
def log_user_profile_update(sender, instance, created, **kwargs):
    """Log user profile updates."""
    if not created and hasattr(instance, '_pending_changes'):
        AuditLog.log_action(
            user=instance,  # The user being modified
            action=AuditLog.Action.UPDATE,
            model_name='User',
            object_id=str(instance.pk),
            object_repr=str(instance),
            changes=instance._pending_changes
        )
        
        logger.info(f"User profile updated: {instance.username}")
        delattr(instance, '_pending_changes')


def get_client_ip(request):
    """Get client IP address from request."""
    if not request:
        return '127.0.0.1'  # Default for test/shell environments

    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR')

    return ip or '127.0.0.1'  # Fallback to localhost
