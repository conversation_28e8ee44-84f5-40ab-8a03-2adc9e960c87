{% extends 'transactions/base_form.html' %}
{% load i18n static %}

{% block additional_basic_fields %}
<div class="col-md-6">
    <div class="mb-3">
        <label for="recipient_customer" class="form-label">{% trans "Recipient Customer" %} <span class="text-danger">*</span></label>
        <select class="form-select" id="recipient_customer" name="recipient_customer" required>
            <option value="">{% trans "Select recipient..." %}</option>
        </select>
        <div class="form-text">{% trans "Customer who will receive the transfer" %}</div>
    </div>
</div>
{% endblock %}

{% block transaction_specific_fields %}
<!-- Internal Transfer Details -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-arrow-left-right"></i>
            {% trans "Internal Transfer Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Transfer Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.01" required placeholder="0.00">
                    <div class="form-text">{% trans "Amount to transfer from sender" %}</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="commission_amount" class="form-label">{% trans "Transfer Fee" %}</label>
                    <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.01" placeholder="0.00">
                    <div class="form-text">{% trans "Optional transfer fee" %}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="internal_reference" class="form-label">{% trans "Internal Reference" %}</label>
                    <input type="text" class="form-control" id="internal_reference" name="internal_reference" placeholder="{% trans 'Internal tracking reference...' %}">
                    <div class="form-text">{% trans "Internal tracking number for this transfer" %}</div>
                </div>
            </div>
        </div>
        
        <!-- Balance Warning -->
        <div id="balance-warning"></div>
        
        <!-- Transfer Summary -->
        <div class="alert alert-info" id="transfer-summary" style="display: none;">
            <h6 class="alert-heading">
                <i class="bi bi-info-circle"></i>
                {% trans "Transfer Summary" %}
            </h6>
            <div id="transfer-summary-content"></div>
        </div>
    </div>
</div>

<!-- Recipient Balance Information -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-person-check"></i>
            {% trans "Recipient Information" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>{% trans "Recipient Details" %}</h6>
                <div id="recipient-details">
                    <div class="text-muted text-center py-3">
                        <i class="bi bi-person"></i>
                        {% trans "Select recipient to view details" %}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h6>{% trans "Recipient Balance" %}</h6>
                <div id="recipient-balance">
                    <div class="text-muted text-center py-3">
                        <i class="bi bi-wallet"></i>
                        {% trans "Select recipient to view balance" %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="{% static 'js/transactions/internal_transfer.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = new InternalTransferForm();
    form.init();
});
</script>
{% endblock %}
