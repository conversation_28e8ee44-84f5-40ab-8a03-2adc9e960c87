"""
Accounts app configuration for Arena Doviz Exchange Accounting System.
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class AccountsConfig(AppConfig):
    """Configuration for the accounts app."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.accounts'
    verbose_name = 'Arena Doviz Accounts'
    
    def ready(self):
        """
        Initialize the accounts app when Django starts.
        """
        logger.info("Arena Doviz Accounts app is ready")
        
        # Import signal handlers
        try:
            from . import signals
            logger.debug("Accounts app signals imported successfully")
        except ImportError as e:
            logger.warning(f"Could not import accounts app signals: {e}")
        
        # Initialize authentication services
        self._initialize_auth_services()
    
    def _initialize_auth_services(self):
        """Initialize authentication and authorization services."""
        logger.debug("Initializing authentication services...")

        try:
            # Skip database operations during migrations
            from django.db import connection
            if connection.introspection.table_names():
                # Setup default groups and permissions only if tables exist
                self._setup_default_groups()
                logger.info("Authentication services initialized successfully")
            else:
                logger.debug("Skipping auth services initialization - database not ready")
        except Exception as e:
            logger.error(f"Failed to initialize authentication services: {e}")
    
    def _setup_default_groups(self):
        """Setup default user groups and permissions."""
        from django.contrib.auth.models import Group, Permission
        from django.contrib.contenttypes.models import ContentType
        
        # Define default groups based on business requirements
        default_groups = [
            {
                'name': 'Admin',
                'description': 'Full system access and management capabilities'
            },
            {
                'name': 'Accountant',
                'description': 'Transaction management and financial reporting'
            },
            {
                'name': 'Branch Employee',
                'description': 'Daily operations and customer service'
            },
            {
                'name': 'Viewer',
                'description': 'Read-only access to reports and data'
            },
            {
                'name': 'Courier',
                'description': 'Delivery tracking and receipt management'
            }
        ]
        
        for group_data in default_groups:
            group, created = Group.objects.get_or_create(
                name=group_data['name']
            )
            if created:
                logger.debug(f"Created default group: {group.name}")
            else:
                logger.debug(f"Default group already exists: {group.name}")
