"""
Serializers for Arena Doviz Accounts app.
"""

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import User, UserSession, AuditLog
from apps.core.models import EncryptedAuditLog
import logging

logger = logging.getLogger(__name__)


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model."""
    
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone_number', 'role', 'location', 'employee_id', 'department',
            'is_active', 'date_joined', 'last_login', 'password', 'password_confirm'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']
        extra_kwargs = {
            'password': {'write_only': True},
            'password_confirm': {'write_only': True},
        }
    
    def validate(self, attrs):
        """Validate password confirmation."""
        if 'password' in attrs and 'password_confirm' in attrs:
            if attrs['password'] != attrs['password_confirm']:
                raise serializers.ValidationError("Passwords do not match")
        return attrs
    
    def create(self, validated_data):
        """Create new user."""
        validated_data.pop('password_confirm', None)
        password = validated_data.pop('password')
        
        user = User.objects.create_user(
            password=password,
            **validated_data
        )
        
        logger.info(f"User created via API: {user.username}")
        return user
    
    def update(self, instance, validated_data):
        """Update user."""
        validated_data.pop('password_confirm', None)
        password = validated_data.pop('password', None)
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        if password:
            instance.set_password(password)
        
        instance.save()
        
        logger.info(f"User updated via API: {instance.username}")
        return instance


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile (limited fields)."""
    
    location_name = serializers.CharField(source='location.name', read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone_number', 'role', 'role_display', 'location', 'location_name',
            'employee_id', 'department', 'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'username', 'role', 'date_joined', 'last_login']


class LoginSerializer(serializers.Serializer):
    """Serializer for user login."""
    
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validate login credentials."""
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(
                request=self.context.get('request'),
                username=username,
                password=password
            )
            
            if not user:
                raise serializers.ValidationError("Invalid credentials")
            
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled")
            
            if user.is_account_locked():
                raise serializers.ValidationError("Account is temporarily locked due to failed login attempts")
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError("Must include username and password")


class ChangePasswordSerializer(serializers.Serializer):
    """Serializer for changing password."""
    
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate_old_password(self, value):
        """Validate old password."""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value
    
    def validate(self, attrs):
        """Validate new password confirmation."""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords do not match")
        return attrs
    
    def save(self):
        """Change user password."""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        
        logger.info(f"Password changed for user: {user.username}")
        return user


class UserSessionSerializer(serializers.ModelSerializer):
    """Serializer for UserSession model."""
    
    user_username = serializers.CharField(source='user.username', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = UserSession
        fields = [
            'id', 'user', 'user_username', 'session_key', 'ip_address',
            'user_agent', 'is_active', 'created_at', 'ended_at', 'duration'
        ]
        read_only_fields = ['id', 'created_at', 'ended_at']
    
    def get_duration(self, obj):
        """Get session duration."""
        if obj.ended_at and obj.created_at:
            duration = obj.ended_at - obj.created_at
            return str(duration)
        return None


class AuditLogSerializer(serializers.ModelSerializer):
    """Serializer for EncryptedAuditLog model."""

    action_display = serializers.CharField(source='get_action_type_display', read_only=True)
    user_username = serializers.SerializerMethodField()

    class Meta:
        model = EncryptedAuditLog
        fields = [
            'id', 'action_type', 'action_display', 'table_name', 'record_id',
            'user_id', 'user_username', 'success', 'error_message', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

    def get_user_username(self, obj):
        """Get username for the user who performed the action."""
        if obj.user_id:
            try:
                from .models import User
                user = User.objects.get(id=obj.user_id)
                return user.username
            except User.DoesNotExist:
                return f"User {obj.user_id} (deleted)"
        return "System"


class UserListSerializer(serializers.ModelSerializer):
    """Simplified serializer for user list views."""

    display_name = serializers.CharField(source='get_display_name', read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'display_name', 'role', 'role_display', 'location_name',
            'is_active', 'date_joined', 'last_login'
        ]


class UserStatsSerializer(serializers.Serializer):
    """Serializer for user statistics."""

    total_users = serializers.IntegerField()
    active_users = serializers.IntegerField()
    users_by_role = serializers.DictField()
    users_by_location = serializers.DictField()
    recent_logins = serializers.IntegerField()
    failed_login_attempts = serializers.IntegerField()


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom JWT token serializer that includes user information in the response.
    """

    def validate(self, attrs):
        """Validate credentials and add user information to token."""
        # Get the token data from parent
        data = super().validate(attrs)

        # Check if user account is locked
        if self.user.is_account_locked():
            raise serializers.ValidationError("Account is temporarily locked due to failed login attempts")

        # Reset failed login attempts on successful login
        if self.user.failed_login_attempts > 0:
            self.user.failed_login_attempts = 0
            self.user.account_locked_until = None
            self.user.save(update_fields=['failed_login_attempts', 'account_locked_until'])

        # Add user information to response
        data['user'] = {
            'id': str(self.user.id),
            'username': self.user.username,
            'email': self.user.email,
            'first_name': self.user.first_name,
            'last_name': self.user.last_name,
            'role': self.user.role,
            'location': {
                'id': str(self.user.location.id),
                'name': self.user.location.name,
                'code': self.user.location.code
            } if self.user.location else None,
            'is_active': self.user.is_active,
            'permissions': {
                'can_approve_transactions': self.user.can_approve_transactions(),
                'can_complete_transactions': self.user.can_complete_transactions(),
                'can_manage_users': self.user.can_manage_users(),
                'can_view_all_locations': self.user.can_view_all_locations(),
            }
        }

        # Create Django session for web interface compatibility
        request = self.context.get('request')
        if request:
            from django.contrib.auth import login
            login(request, self.user)
            logger.info(f"Django session created for user: {self.user.username}")

        # Log successful login
        from .utils import log_user_action, get_client_ip
        log_user_action(
            user=self.user,
            action='jwt_login',
            ip_address=get_client_ip(self.context.get('request')),
            additional_data={'login_method': 'jwt_with_session'}
        )

        logger.info(f"JWT token issued for user: {self.user.username}")

        return data

    @classmethod
    def get_token(cls, user):
        """Add custom claims to the token."""
        token = super().get_token(user)

        # Add custom claims
        token['username'] = user.username
        token['role'] = user.role
        token['location_id'] = str(user.location.id) if user.location else None
        token['is_active'] = user.is_active

        return token
