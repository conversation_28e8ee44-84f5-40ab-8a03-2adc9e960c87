"""
Web URL configuration for Arena Doviz Transactions app.
Handles HTML template routes for transaction management interface.
"""

from django.urls import path
from . import web_views

app_name = 'transactions_web'

urlpatterns = [
    # Transaction management pages
    path('', web_views.transaction_navigation, name='navigation'),
    path('list/', web_views.transaction_list, name='list'),
    path('add/', web_views.transaction_add, name='add'),
    path('<uuid:transaction_id>/', web_views.transaction_detail, name='detail'),
    path('<uuid:transaction_id>/edit/', web_views.transaction_edit, name='edit'),
    path('<uuid:transaction_id>/approve/', web_views.transaction_approve_page, name='approve'),

    # Test page
    path('test/', web_views.transaction_test_forms, name='test_forms'),

    # Transaction type specific pages
    path('type/<str:transaction_type_code>/', web_views.transaction_type_list, name='type_list'),
    path('type/<str:transaction_type_code>/add/', web_views.transaction_type_add, name='type_add'),

    # Transfer sub-type specific pages
    path('transfer/', web_views.transfer_navigation, name='transfer_navigation'),

    # Cash transaction specific pages
    path('cash/', web_views.cash_navigation, name='cash_navigation'),
    path('transfer/internal/', web_views.internal_transfer_list, name='internal_transfer_list'),
    path('transfer/internal/add/', web_views.internal_transfer_add, name='internal_transfer_add'),
    path('transfer/external/', web_views.external_transfer_list, name='external_transfer_list'),
    path('transfer/external/add/', web_views.external_transfer_add, name='external_transfer_add'),
    path('transfer/international/', web_views.international_transfer_list, name='international_transfer_list'),
    path('transfer/international/add/', web_views.international_transfer_add, name='international_transfer_add'),

    # Special transaction pages
    path('pending-approvals/', web_views.pending_approvals, name='pending_approvals'),
    path('reports/', web_views.transaction_reports, name='reports'),
    path('dashboard/', web_views.TransactionDashboardView.as_view(), name='dashboard'),

    # AJAX endpoints for web interface
    path('api/stats/', web_views.get_transaction_stats, name='api_stats'),
    path('api/customer/<uuid:customer_id>/balance/', web_views.get_customer_balance, name='api_customer_balance'),
    path('api/exchange-rates/', web_views.get_exchange_rates, name='api_exchange_rates'),
    path('api/quick-transaction/', web_views.quick_transaction, name='api_quick_transaction'),
]
