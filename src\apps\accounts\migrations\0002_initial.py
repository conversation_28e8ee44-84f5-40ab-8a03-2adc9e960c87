# Generated by Django 4.2.23 on 2025-08-13 08:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('locations', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='location',
            field=models.ForeignKey(blank=True, help_text='Primary location where this user works', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='locations.location', verbose_name='Location'),
        ),
        migrations.AddField(
            model_name='user',
            name='updated_by',
            field=models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by'),
        ),
        migrations.AddField(
            model_name='user',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['user', 'is_active'], name='accounts_us_user_id_91ed82_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['session_key'], name='accounts_us_session_511f42_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['created_at'], name='accounts_us_created_e46af9_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['user', 'action'], name='accounts_au_user_id_96c97d_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['model_name', 'object_id'], name='accounts_au_model_n_76c60d_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['created_at'], name='accounts_au_created_606b86_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['action'], name='accounts_au_action_5ca9a9_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['role'], name='accounts_us_role_1fa9a5_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['location'], name='accounts_us_locatio_5a932c_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['is_active'], name='accounts_us_is_acti_a5841d_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['employee_id'], name='accounts_us_employe_0cbd94_idx'),
        ),
    ]
