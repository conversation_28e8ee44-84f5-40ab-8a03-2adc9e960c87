"""
Comprehensive Arena Doviz System Analysis
"""
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
import django
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import AccessToken
from apps.customers.models import Customer
from apps.transactions.models import Transaction, TransactionType
from apps.currencies.models import Currency, ExchangeRate
from apps.locations.models import Location

User = get_user_model()

class ArenaSystemAnalysis:
    """Comprehensive system analysis"""
    
    def __init__(self):
        self.client = Client()
        self.analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'system_status': 'UNKNOWN',
            'critical_issues': [],
            'warnings': [],
            'recommendations': [],
            'test_results': {}
        }
        
    def run_comprehensive_analysis(self):
        """Run comprehensive system analysis"""
        print("🔍 Starting Comprehensive Arena Doviz System Analysis...")
        print("=" * 60)
        
        try:
            # 1. Database connectivity and models
            self.test_database_connectivity()
            
            # 2. API endpoints
            self.test_api_endpoints()
            
            # 3. Transaction processing
            self.test_transaction_processing()
            
            # 4. Financial calculations
            self.test_financial_calculations()
            
            # 5. Frontend JavaScript validation
            self.analyze_frontend_code()
            
            # 6. Configuration analysis
            self.analyze_configuration()
            
            # Determine overall status
            self.determine_system_status()
            
            # Generate report
            self.generate_analysis_report()
            
        except Exception as e:
            self.analysis_results['critical_issues'].append(f"Analysis failed: {str(e)}")
            self.analysis_results['system_status'] = 'ERROR'
        
        return self.analysis_results
    
    def test_database_connectivity(self):
        """Test database connectivity and model integrity"""
        print("📊 Testing Database Connectivity...")
        
        try:
            # Test basic model queries
            user_count = User.objects.count()
            customer_count = Customer.objects.count()
            currency_count = Currency.objects.count()
            location_count = Location.objects.count()
            transaction_count = Transaction.objects.count()
            
            self.analysis_results['test_results']['database'] = {
                'status': 'PASS',
                'users': user_count,
                'customers': customer_count,
                'currencies': currency_count,
                'locations': location_count,
                'transactions': transaction_count
            }
            
            print(f"✅ Database connectivity: PASS")
            print(f"   Users: {user_count}, Customers: {customer_count}")
            print(f"   Currencies: {currency_count}, Locations: {location_count}")
            print(f"   Transactions: {transaction_count}")
            
            # Check for required data
            if currency_count < 3:
                self.analysis_results['warnings'].append("Less than 3 currencies configured")
            if location_count < 1:
                self.analysis_results['critical_issues'].append("No locations configured")
                
        except Exception as e:
            self.analysis_results['critical_issues'].append(f"Database connectivity failed: {str(e)}")
            self.analysis_results['test_results']['database'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            print(f"❌ Database connectivity: FAIL - {str(e)}")
    
    def test_api_endpoints(self):
        """Test critical API endpoints"""
        print("\n🌐 Testing API Endpoints...")
        
        try:
            # Create test user and get token
            user, created = User.objects.get_or_create(
                username='test_api_user',
                defaults={'email': '<EMAIL>', 'is_staff': True}
            )
            token = AccessToken.for_user(user)
            headers = {'HTTP_AUTHORIZATION': f'Bearer {str(token)}'}
            
            # Test endpoints
            endpoints = [
                '/api/v1/customers/customers/',
                '/api/v1/transactions/transactions/',
                '/api/v1/currencies/currencies/',
                '/api/v1/locations/locations/',
                '/api/v1/currencies/rates/current/?location=DXB'
            ]
            
            endpoint_results = {}
            
            for endpoint in endpoints:
                try:
                    response = self.client.get(endpoint, **headers)
                    endpoint_results[endpoint] = {
                        'status_code': response.status_code,
                        'accessible': response.status_code in [200, 404],  # 404 is OK if no data
                        'error': None
                    }
                    
                    if response.status_code == 200:
                        print(f"✅ {endpoint}: PASS ({response.status_code})")
                    else:
                        print(f"⚠️ {endpoint}: WARNING ({response.status_code})")
                        
                except Exception as e:
                    endpoint_results[endpoint] = {
                        'status_code': 0,
                        'accessible': False,
                        'error': str(e)
                    }
                    print(f"❌ {endpoint}: FAIL - {str(e)}")
            
            self.analysis_results['test_results']['api_endpoints'] = endpoint_results
            
        except Exception as e:
            self.analysis_results['critical_issues'].append(f"API endpoint testing failed: {str(e)}")
            print(f"❌ API endpoint testing: FAIL - {str(e)}")
    
    def test_transaction_processing(self):
        """Test transaction processing logic"""
        print("\n💰 Testing Transaction Processing...")
        
        try:
            # Check transaction types
            transaction_types = TransactionType.objects.filter(is_active=True)
            
            required_types = ['EXCHANGE', 'DEPOSIT', 'WITHDRAWAL', 'INTERNAL_TRANSFER']
            existing_types = [tt.code for tt in transaction_types]
            
            missing_types = [t for t in required_types if t not in existing_types]
            
            self.analysis_results['test_results']['transaction_processing'] = {
                'status': 'PASS' if not missing_types else 'WARNING',
                'transaction_types_count': len(transaction_types),
                'missing_types': missing_types
            }
            
            if missing_types:
                self.analysis_results['warnings'].append(f"Missing transaction types: {missing_types}")
                print(f"⚠️ Missing transaction types: {missing_types}")
            else:
                print(f"✅ Transaction types: PASS ({len(transaction_types)} types)")
                
        except Exception as e:
            self.analysis_results['critical_issues'].append(f"Transaction processing test failed: {str(e)}")
            print(f"❌ Transaction processing: FAIL - {str(e)}")
    
    def test_financial_calculations(self):
        """Test financial calculation accuracy"""
        print("\n🧮 Testing Financial Calculations...")
        
        try:
            # Test commission calculation
            from apps.transactions.commission_utils import commission_calculator
            
            # Create test data if needed
            location = Location.objects.first()
            currency = Currency.objects.first()
            
            if location and currency:
                # Test commission calculation
                test_transaction_data = {
                    'location': location,
                    'from_currency': currency,
                    'to_currency': currency,
                    'from_amount': 1000,
                    'transaction_type': None
                }
                
                commission_result = commission_calculator.get_commission_preview(test_transaction_data)
                
                self.analysis_results['test_results']['financial_calculations'] = {
                    'status': 'PASS',
                    'commission_calculation': 'working',
                    'test_amount': 1000,
                    'calculated_commission': str(commission_result.get('amount', 0))
                }
                
                print(f"✅ Financial calculations: PASS")
                print(f"   Commission calculation working")
            else:
                self.analysis_results['warnings'].append("No location or currency data for financial testing")
                print(f"⚠️ Financial calculations: WARNING - No test data")
                
        except Exception as e:
            self.analysis_results['warnings'].append(f"Financial calculation test failed: {str(e)}")
            print(f"⚠️ Financial calculations: WARNING - {str(e)}")
    
    def analyze_frontend_code(self):
        """Analyze frontend JavaScript code"""
        print("\n🖥️ Analyzing Frontend Code...")
        
        try:
            # Check for key JavaScript files
            js_files = [
                'src/static/js/transactions/common.js',
                'src/static/js/transactions/deposit.js',
                'src/static/js/transactions/exchange.js'
            ]
            
            js_analysis = {}
            
            for js_file in js_files:
                if os.path.exists(js_file):
                    with open(js_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # Check for common issues
                    issues = []
                    if 'commission_amount' in content and 'parseFloat' in content:
                        # Commission validation is implemented
                        pass
                    elif 'commission_amount' in content:
                        issues.append("Commission amount validation may need improvement")
                    
                    js_analysis[js_file] = {
                        'exists': True,
                        'size_kb': len(content) / 1024,
                        'issues': issues
                    }
                    
                    if issues:
                        print(f"⚠️ {js_file}: {len(issues)} issues found")
                    else:
                        print(f"✅ {js_file}: No issues found")
                else:
                    js_analysis[js_file] = {
                        'exists': False,
                        'issues': ['File not found']
                    }
                    print(f"❌ {js_file}: File not found")
            
            self.analysis_results['test_results']['frontend_code'] = js_analysis
            
        except Exception as e:
            self.analysis_results['warnings'].append(f"Frontend code analysis failed: {str(e)}")
            print(f"⚠️ Frontend code analysis: WARNING - {str(e)}")
    
    def analyze_configuration(self):
        """Analyze system configuration"""
        print("\n⚙️ Analyzing Configuration...")
        
        try:
            from django.conf import settings
            
            config_analysis = {
                'debug_mode': settings.DEBUG,
                'database_engine': settings.DATABASES['default']['ENGINE'],
                'static_files_configured': bool(settings.STATIC_URL),
                'media_files_configured': bool(settings.MEDIA_URL),
                'timezone': str(settings.TIME_ZONE),
                'language': settings.LANGUAGE_CODE
            }
            
            # Check for potential issues
            if settings.DEBUG:
                self.analysis_results['warnings'].append("Debug mode is enabled")
            
            if 'sqlite' in settings.DATABASES['default']['ENGINE']:
                self.analysis_results['recommendations'].append("Consider using PostgreSQL for production")
            
            self.analysis_results['test_results']['configuration'] = config_analysis
            
            print(f"✅ Configuration analysis: COMPLETE")
            print(f"   Database: {config_analysis['database_engine']}")
            print(f"   Debug mode: {config_analysis['debug_mode']}")
            
        except Exception as e:
            self.analysis_results['warnings'].append(f"Configuration analysis failed: {str(e)}")
            print(f"⚠️ Configuration analysis: WARNING - {str(e)}")
    
    def determine_system_status(self):
        """Determine overall system status"""
        critical_count = len(self.analysis_results['critical_issues'])
        warning_count = len(self.analysis_results['warnings'])
        
        if critical_count > 0:
            self.analysis_results['system_status'] = 'CRITICAL'
        elif warning_count > 3:
            self.analysis_results['system_status'] = 'WARNING'
        else:
            self.analysis_results['system_status'] = 'HEALTHY'
    
    def generate_analysis_report(self):
        """Generate comprehensive analysis report"""
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE SYSTEM ANALYSIS REPORT")
        print("=" * 60)
        
        status_emoji = {
            'HEALTHY': '✅',
            'WARNING': '⚠️',
            'CRITICAL': '❌',
            'ERROR': '💥'
        }
        
        status = self.analysis_results['system_status']
        print(f"Overall Status: {status_emoji.get(status, '❓')} {status}")
        
        if self.analysis_results['critical_issues']:
            print(f"\n🚨 CRITICAL ISSUES ({len(self.analysis_results['critical_issues'])}):")
            for issue in self.analysis_results['critical_issues']:
                print(f"   - {issue}")
        
        if self.analysis_results['warnings']:
            print(f"\n⚠️ WARNINGS ({len(self.analysis_results['warnings'])}):")
            for warning in self.analysis_results['warnings']:
                print(f"   - {warning}")
        
        if self.analysis_results['recommendations']:
            print(f"\n💡 RECOMMENDATIONS ({len(self.analysis_results['recommendations'])}):")
            for rec in self.analysis_results['recommendations']:
                print(f"   - {rec}")
        
        # Save detailed report
        report_path = Path('reports') / f"system_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(self.analysis_results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: {report_path}")
        print("=" * 60)

def main():
    """Main function"""
    analyzer = ArenaSystemAnalysis()
    results = analyzer.run_comprehensive_analysis()
    return results

if __name__ == "__main__":
    main()
