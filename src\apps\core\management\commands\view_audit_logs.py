"""
Django management command to view and analyze audit logs.
"""

import json
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import models
from apps.core.models import EncryptedAuditLog
from apps.core.audit import get_audit_logger


class Command(BaseCommand):
    """View and analyze audit logs from Arena Doviz system."""
    
    help = 'View and analyze audit logs from Arena Doviz system'
    
    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--action-type',
            type=str,
            help='Filter by action type (create, update, delete, login, logout, etc.)'
        )
        
        parser.add_argument(
            '--table-name',
            type=str,
            help='Filter by table name'
        )
        
        parser.add_argument(
            '--user-id',
            type=int,
            help='Filter by user ID'
        )
        
        parser.add_argument(
            '--record-id',
            type=int,
            help='Filter by record ID'
        )
        
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days to look back (default: 7)'
        )
        
        parser.add_argument(
            '--limit',
            type=int,
            default=50,
            help='Maximum number of records to display (default: 50)'
        )
        
        parser.add_argument(
            '--format',
            choices=['table', 'json', 'csv'],
            default='table',
            help='Output format (default: table)'
        )
        
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show audit log statistics instead of individual records'
        )
        
        parser.add_argument(
            '--failed-only',
            action='store_true',
            help='Show only failed operations'
        )
        
        parser.add_argument(
            '--export',
            type=str,
            help='Export results to file (specify filename)'
        )
    
    def handle(self, *args, **options):
        """Handle the command execution."""
        try:
            if options['stats']:
                self._show_statistics(options)
            else:
                self._show_audit_logs(options)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to view audit logs: {str(e)}')
            )
    
    def _show_audit_logs(self, options):
        """Show individual audit log records."""
        # Build filters
        filters = {}
        
        if options['action_type']:
            filters['action_type'] = options['action_type']
        
        if options['table_name']:
            filters['table_name'] = options['table_name']
        
        if options['user_id']:
            filters['user_id'] = options['user_id']
        
        if options['record_id']:
            filters['record_id'] = options['record_id']
        
        if options['failed_only']:
            filters['success'] = False
        
        # Date filter
        start_date = timezone.now() - timedelta(days=options['days'])
        filters['created_at__gte'] = start_date
        
        # Get audit logs
        audit_logs = EncryptedAuditLog.objects.filter(**filters).order_by('-created_at')[:options['limit']]
        
        if not audit_logs:
            self.stdout.write(self.style.WARNING('No audit logs found matching the criteria.'))
            return
        
        # Display results
        if options['format'] == 'table':
            self._display_table(audit_logs)
        elif options['format'] == 'json':
            self._display_json(audit_logs)
        elif options['format'] == 'csv':
            self._display_csv(audit_logs)
        
        # Export if requested
        if options['export']:
            self._export_logs(audit_logs, options['export'], options['format'])
    
    def _show_statistics(self, options):
        """Show audit log statistics."""
        # Date filter
        start_date = timezone.now() - timedelta(days=options['days'])
        
        # Get base queryset
        queryset = EncryptedAuditLog.objects.filter(created_at__gte=start_date)
        
        self.stdout.write(self.style.SUCCESS(f'Audit Log Statistics (Last {options["days"]} days)'))
        self.stdout.write('=' * 60)
        
        # Total records
        total_records = queryset.count()
        self.stdout.write(f'Total Records: {total_records}')
        
        # Success/Failure breakdown
        successful = queryset.filter(success=True).count()
        failed = queryset.filter(success=False).count()
        self.stdout.write(f'Successful Operations: {successful}')
        self.stdout.write(f'Failed Operations: {failed}')
        
        # Action type breakdown
        self.stdout.write('\nAction Type Breakdown:')
        action_stats = queryset.values('action_type').annotate(
            count=models.Count('id')
        ).order_by('-count')
        
        for stat in action_stats:
            self.stdout.write(f'  {stat["action_type"]}: {stat["count"]}')
        
        # Table breakdown
        self.stdout.write('\nTable Breakdown:')
        table_stats = queryset.values('table_name').annotate(
            count=models.Count('id')
        ).order_by('-count')[:10]  # Top 10 tables
        
        for stat in table_stats:
            self.stdout.write(f'  {stat["table_name"]}: {stat["count"]}')
        
        # User activity
        self.stdout.write('\nTop Users by Activity:')
        user_stats = queryset.filter(user_id__isnull=False).values('user_id').annotate(
            count=models.Count('id')
        ).order_by('-count')[:10]
        
        for stat in user_stats:
            self.stdout.write(f'  User ID {stat["user_id"]}: {stat["count"]} actions')
        
        # Recent failed operations
        recent_failures = queryset.filter(success=False).order_by('-created_at')[:5]
        if recent_failures:
            self.stdout.write('\nRecent Failed Operations:')
            for failure in recent_failures:
                self.stdout.write(
                    f'  {failure.created_at.strftime("%Y-%m-%d %H:%M:%S")} - '
                    f'{failure.action_type} on {failure.table_name}[{failure.record_id}] - '
                    f'{failure.error_message[:50]}...'
                )
    
    def _display_table(self, audit_logs):
        """Display audit logs in table format."""
        self.stdout.write(self.style.SUCCESS('Audit Logs'))
        self.stdout.write('=' * 120)
        
        # Header
        header = f"{'Timestamp':<20} {'Action':<12} {'Table':<15} {'Record ID':<10} {'User ID':<8} {'Success':<7} {'Error':<30}"
        self.stdout.write(header)
        self.stdout.write('-' * 120)
        
        # Rows
        for log in audit_logs:
            timestamp = log.created_at.strftime('%Y-%m-%d %H:%M:%S')
            success = 'Yes' if log.success else 'No'
            error = (log.error_message[:27] + '...') if len(log.error_message) > 30 else log.error_message
            
            row = f"{timestamp:<20} {log.action_type:<12} {log.table_name:<15} {log.record_id:<10} {log.user_id or 'N/A':<8} {success:<7} {error:<30}"
            
            if log.success:
                self.stdout.write(row)
            else:
                self.stdout.write(self.style.ERROR(row))
    
    def _display_json(self, audit_logs):
        """Display audit logs in JSON format."""
        logs_data = []
        
        for log in audit_logs:
            log_data = {
                'id': log.id,
                'timestamp': log.created_at.isoformat(),
                'action_type': log.action_type,
                'table_name': log.table_name,
                'record_id': log.record_id,
                'user_id': log.user_id,
                'success': log.success,
                'error_message': log.error_message,
                'encrypted_ip_address': bool(log.encrypted_ip_address),
                'encrypted_user_agent': bool(log.encrypted_user_agent),
                'has_old_values': bool(log.encrypted_old_values),
                'has_new_values': bool(log.encrypted_new_values)
            }
            logs_data.append(log_data)
        
        self.stdout.write(json.dumps(logs_data, indent=2))
    
    def _display_csv(self, audit_logs):
        """Display audit logs in CSV format."""
        # Header
        self.stdout.write('timestamp,action_type,table_name,record_id,user_id,success,error_message')
        
        # Rows
        for log in audit_logs:
            timestamp = log.created_at.isoformat()
            error_message = log.error_message.replace(',', ';').replace('\n', ' ')
            
            self.stdout.write(
                f'{timestamp},{log.action_type},{log.table_name},{log.record_id},'
                f'{log.user_id or ""},{log.success},{error_message}'
            )
    
    def _export_logs(self, audit_logs, filename, format_type):
        """Export audit logs to file."""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                if format_type == 'json':
                    logs_data = []
                    for log in audit_logs:
                        log_data = {
                            'id': log.id,
                            'timestamp': log.created_at.isoformat(),
                            'action_type': log.action_type,
                            'table_name': log.table_name,
                            'record_id': log.record_id,
                            'user_id': log.user_id,
                            'success': log.success,
                            'error_message': log.error_message
                        }
                        logs_data.append(log_data)
                    
                    json.dump(logs_data, f, indent=2)
                
                elif format_type == 'csv':
                    f.write('timestamp,action_type,table_name,record_id,user_id,success,error_message\n')
                    for log in audit_logs:
                        timestamp = log.created_at.isoformat()
                        error_message = log.error_message.replace(',', ';').replace('\n', ' ')
                        f.write(
                            f'{timestamp},{log.action_type},{log.table_name},{log.record_id},'
                            f'{log.user_id or ""},{log.success},{error_message}\n'
                        )
            
            self.stdout.write(
                self.style.SUCCESS(f'Audit logs exported to {filename}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to export audit logs: {str(e)}')
            )
