"""
Custom Django model fields for encrypted data storage.
Provides transparent encryption/decryption for sensitive fields.
"""

import logging
from typing import Any, Optional
from django.db import models
from django.core.exceptions import ValidationError
from .encryption import encrypt_sensitive_data, decrypt_sensitive_data, mask_sensitive_value

logger = logging.getLogger(__name__)


class EncryptedFieldMixin:
    """Mixin for encrypted field functionality."""
    
    def __init__(self, *args, **kwargs):
        """Initialize encrypted field with additional options."""
        self.mask_in_admin = kwargs.pop('mask_in_admin', True)
        self.mask_char = kwargs.pop('mask_char', '*')
        self.visible_chars = kwargs.pop('visible_chars', 4)
        super().__init__(*args, **kwargs)
    
    def from_db_value(self, value, expression, connection):
        """Decrypt value when loading from database."""
        if value is None:
            return value
        
        try:
            decrypted_value = decrypt_sensitive_data(value)
            return decrypted_value
        except Exception as e:
            logger.error(f"Failed to decrypt field value: {str(e)}")
            # Return the original value if decryption fails (for migration compatibility)
            return value
    
    def to_python(self, value):
        """Convert value to Python representation."""
        if value is None:
            return value

        # If it's a string, check if it looks like encrypted data
        if isinstance(value, str):
            # Check if it looks like base64 encrypted data (our encryption format)
            if len(value) > 50 and value.replace('=', '').replace('+', '').replace('/', '').isalnum():
                try:
                    decrypted_value = decrypt_sensitive_data(value)
                    return decrypted_value if decrypted_value is not None else value
                except Exception:
                    # If decryption fails, assume it's already decrypted
                    return value
            else:
                # Looks like plain text, return as-is
                return value

        # For non-string values, convert to string first
        return str(value)
    
    def get_prep_value(self, value):
        """Encrypt value before saving to database."""
        if value is None or value == '':
            return value
        
        try:
            encrypted_value = encrypt_sensitive_data(str(value))
            return encrypted_value
        except Exception as e:
            logger.error(f"Failed to encrypt field value: {str(e)}")
            # In case of encryption failure, log the error but don't save the raw value
            raise ValidationError(f"Failed to encrypt sensitive data: {str(e)}")
    
    def get_masked_value(self, value):
        """Get masked version of the value for display."""
        if not value:
            return value
        
        return mask_sensitive_value(
            str(value), 
            mask_char=self.mask_char, 
            visible_chars=self.visible_chars
        )


class EncryptedCharField(EncryptedFieldMixin, models.CharField):
    """
    CharField that automatically encrypts/decrypts data.
    Stores encrypted data in the database but provides transparent access.
    """
    
    description = "Encrypted character field"
    
    def __init__(self, *args, **kwargs):
        """Initialize encrypted char field."""
        # Increase max_length to accommodate encrypted data
        if 'max_length' in kwargs:
            # Encrypted data is typically 1.5-2x larger than original
            kwargs['max_length'] = max(kwargs['max_length'] * 2, 500)
        else:
            kwargs['max_length'] = 500
        
        super().__init__(*args, **kwargs)


class EncryptedTextField(EncryptedFieldMixin, models.TextField):
    """
    TextField that automatically encrypts/decrypts data.
    Stores encrypted data in the database but provides transparent access.
    """
    
    description = "Encrypted text field"


class EncryptedEmailField(EncryptedFieldMixin, models.EmailField):
    """
    EmailField that automatically encrypts/decrypts email addresses.
    Validates email format before encryption.
    """
    
    description = "Encrypted email field"
    
    def __init__(self, *args, **kwargs):
        """Initialize encrypted email field."""
        # Increase max_length to accommodate encrypted data
        kwargs['max_length'] = kwargs.get('max_length', 254) * 2
        super().__init__(*args, **kwargs)
    
    def validate(self, value, model_instance):
        """Validate email format before encryption."""
        if value:
            # Create a temporary EmailField for validation
            temp_field = models.EmailField()
            temp_field.validate(value, model_instance)
        super().validate(value, model_instance)


class EncryptedDecimalField(EncryptedFieldMixin, models.DecimalField):
    """
    DecimalField that automatically encrypts/decrypts decimal values.
    Maintains precision while providing encryption.
    """
    
    description = "Encrypted decimal field"
    
    def from_db_value(self, value, expression, connection):
        """Decrypt and convert to Decimal."""
        decrypted_value = super().from_db_value(value, expression, connection)
        if decrypted_value is None:
            return None
        
        try:
            from decimal import Decimal
            return Decimal(str(decrypted_value))
        except Exception as e:
            logger.error(f"Failed to convert decrypted value to Decimal: {str(e)}")
            return None
    
    def to_python(self, value):
        """Convert to Decimal after decryption."""
        if value is None:
            return value
        
        # If it's already a Decimal, return as-is
        from decimal import Decimal
        if isinstance(value, Decimal):
            return value
        
        # Decrypt first, then convert to Decimal
        decrypted_value = super().to_python(value)
        if decrypted_value is None:
            return None
        
        try:
            return Decimal(str(decrypted_value))
        except Exception as e:
            logger.error(f"Failed to convert to Decimal: {str(e)}")
            return None
    
    def get_prep_value(self, value):
        """Convert Decimal to string before encryption."""
        if value is None:
            return value
        
        # Convert Decimal to string before encryption
        string_value = str(value)
        return super().get_prep_value(string_value)


class EncryptedJSONField(EncryptedFieldMixin, models.JSONField):
    """
    JSONField that automatically encrypts/decrypts JSON data.
    Stores encrypted JSON as text in the database.
    """
    
    description = "Encrypted JSON field"
    
    def from_db_value(self, value, expression, connection):
        """Decrypt and parse JSON."""
        decrypted_value = super().from_db_value(value, expression, connection)
        if decrypted_value is None:
            return None
        
        try:
            import json
            return json.loads(decrypted_value)
        except Exception as e:
            logger.error(f"Failed to parse decrypted JSON: {str(e)}")
            return None
    
    def to_python(self, value):
        """Convert to Python object after decryption."""
        if value is None:
            return value
        
        # If it's already a Python object (dict, list), return as-is
        if isinstance(value, (dict, list)):
            return value
        
        # Decrypt first, then parse JSON
        decrypted_value = super().to_python(value)
        if decrypted_value is None:
            return None
        
        try:
            import json
            return json.loads(decrypted_value)
        except Exception as e:
            logger.error(f"Failed to parse JSON: {str(e)}")
            return None
    
    def get_prep_value(self, value):
        """Convert Python object to JSON string before encryption."""
        if value is None:
            return value
        
        try:
            import json
            json_string = json.dumps(value, ensure_ascii=False)
            return super().get_prep_value(json_string)
        except Exception as e:
            logger.error(f"Failed to serialize JSON: {str(e)}")
            raise ValidationError(f"Failed to serialize JSON data: {str(e)}")


# Utility functions for working with encrypted fields

def get_encrypted_field_names(model_class):
    """
    Get list of encrypted field names for a model.
    
    Args:
        model_class: Django model class
        
    Returns:
        list: List of encrypted field names
    """
    encrypted_fields = []
    
    for field in model_class._meta.get_fields():
        if isinstance(field, EncryptedFieldMixin):
            encrypted_fields.append(field.name)
    
    return encrypted_fields


def mask_model_sensitive_data(instance, field_names=None):
    """
    Create a dictionary with masked sensitive data for logging.
    
    Args:
        instance: Model instance
        field_names: List of field names to mask (defaults to all encrypted fields)
        
    Returns:
        dict: Dictionary with masked sensitive data
    """
    if field_names is None:
        field_names = get_encrypted_field_names(instance.__class__)
    
    masked_data = {}
    
    for field_name in field_names:
        if hasattr(instance, field_name):
            field = instance._meta.get_field(field_name)
            value = getattr(instance, field_name)
            
            if isinstance(field, EncryptedFieldMixin) and value:
                masked_data[field_name] = field.get_masked_value(value)
            else:
                masked_data[field_name] = value
    
    return masked_data
