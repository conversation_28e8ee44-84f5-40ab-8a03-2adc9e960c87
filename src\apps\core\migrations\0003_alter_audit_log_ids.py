# Generated by Django 4.2.23 on 2025-08-19 21:34

import apps.core.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_encryptedauditlog_encrypted_ip_address'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='encryptedauditlog',
            name='encrypted_ip_address',
            field=apps.core.fields.EncryptedCharField(blank=True, help_text='Encrypted IP address of the user', max_length=1000, null=True, verbose_name='IP Address'),
        ),
        migrations.AlterField(
            model_name='encryptedauditlog',
            name='record_id',
            field=models.Char<PERSON>ield(help_text='ID of the affected record (supports both integer and UUID)', max_length=100, verbose_name='Record ID'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='encryptedauditlog',
            name='user_id',
            field=models.Char<PERSON>ield(blank=True, help_text='ID of the user who performed the action (supports both integer and UUID)', max_length=100, null=True, verbose_name='User ID'),
        ),
    ]
