{"timestamp": "2025-08-20T02:38:35.220430", "system_status": "HEALTHY", "critical_issues": [], "warnings": ["Debug mode is enabled"], "recommendations": ["Consider using PostgreSQL for production"], "test_results": {"database": {"status": "PASS", "users": 14, "customers": 5, "currencies": 3, "locations": 6, "transactions": 9}, "api_endpoints": {"/api/v1/customers/customers/": {"status_code": 200, "accessible": true, "error": null}, "/api/v1/transactions/transactions/": {"status_code": 200, "accessible": true, "error": null}, "/api/v1/currencies/currencies/": {"status_code": 200, "accessible": true, "error": null}, "/api/v1/locations/locations/": {"status_code": 200, "accessible": true, "error": null}, "/api/v1/currencies/rates/current/?location=DXB": {"status_code": 200, "accessible": true, "error": null}}, "transaction_processing": {"status": "PASS", "transaction_types_count": 11, "missing_types": []}, "financial_calculations": {"status": "PASS", "commission_calculation": "working", "test_amount": 1000, "calculated_commission": "0"}, "frontend_code": {"src/static/js/transactions/common.js": {"exists": false, "issues": ["File not found"]}, "src/static/js/transactions/deposit.js": {"exists": false, "issues": ["File not found"]}, "src/static/js/transactions/exchange.js": {"exists": false, "issues": ["File not found"]}}, "configuration": {"debug_mode": true, "database_engine": "django.db.backends.sqlite3", "static_files_configured": true, "media_files_configured": true, "timezone": "Asia/Tehran", "language": "fa"}}}