"""
Test Transfer Forms Functionality
"""
import os
import sys
import time
import json
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
import django
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import AccessToken
from apps.customers.models import Customer
from apps.transactions.models import TransactionType
from apps.currencies.models import Currency
from apps.locations.models import Location

User = get_user_model()

class TransferFormTester:
    """Test transfer form functionality"""
    
    def __init__(self):
        self.client = Client()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'forms_tested': [],
            'issues_found': [],
            'fixes_applied': [],
            'overall_status': 'UNKNOWN'
        }
        
    def run_transfer_form_tests(self):
        """Run comprehensive transfer form tests"""
        print("🔧 Testing Transfer Form Functionality...")
        print("=" * 60)
        
        try:
            # 1. Setup test environment
            self.setup_test_environment()
            
            # 2. Test form URLs accessibility
            self.test_form_urls()
            
            # 3. Test API endpoints used by forms
            self.test_form_api_endpoints()
            
            # 4. Test JavaScript functionality
            self.test_javascript_functionality()
            
            # 5. Generate recommendations
            self.generate_recommendations()
            
            # Determine overall status
            self.determine_overall_status()
            
            # Generate report
            self.generate_test_report()
            
        except Exception as e:
            self.test_results['issues_found'].append(f"Testing failed: {str(e)}")
            self.test_results['overall_status'] = 'ERROR'
            print(f"💥 Testing failed: {str(e)}")
        
        return self.test_results
    
    def setup_test_environment(self):
        """Setup test environment with required data"""
        print("🔧 Setting up test environment...")
        
        # Create test user
        self.test_user, created = User.objects.get_or_create(
            username='transfer_test_user',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_active': True
            }
        )
        
        # Get JWT token
        self.token = AccessToken.for_user(self.test_user)
        self.auth_headers = {'HTTP_AUTHORIZATION': f'Bearer {str(self.token)}'}
        
        # Verify required data exists
        self.verify_required_data()
        
        print("✅ Test environment setup complete")
    
    def verify_required_data(self):
        """Verify required data exists for testing"""
        issues = []
        
        # Check customers
        customer_count = Customer.objects.filter(is_active=True).count()
        if customer_count < 2:
            issues.append(f"Need at least 2 customers for transfer testing (found {customer_count})")
        
        # Check currencies
        currency_count = Currency.objects.filter(is_active=True).count()
        if currency_count < 2:
            issues.append(f"Need at least 2 currencies for testing (found {currency_count})")
        
        # Check locations
        location_count = Location.objects.filter(is_active=True).count()
        if location_count < 1:
            issues.append(f"Need at least 1 location for testing (found {location_count})")
        
        # Check transaction types
        required_types = ['INTERNAL_TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNATIONAL_TRANSFER']
        for type_code in required_types:
            if not TransactionType.objects.filter(code=type_code, is_active=True).exists():
                issues.append(f"Missing transaction type: {type_code}")
        
        if issues:
            self.test_results['issues_found'].extend(issues)
            print("⚠️ Data verification issues found:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print("✅ All required data verified")
    
    def test_form_urls(self):
        """Test form URL accessibility"""
        print("\n🌐 Testing Form URL Accessibility...")
        
        form_urls = [
            ('/transactions/transfer/internal/add/', 'Internal Transfer Form'),
            ('/transactions/transfer/external/add/', 'External Transfer Form'),
            ('/transactions/transfer/international/add/', 'International Transfer Form')
        ]
        
        for url, name in form_urls:
            try:
                response = self.client.get(url, **self.auth_headers)
                
                form_test = {
                    'name': name,
                    'url': url,
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'issues': []
                }
                
                if response.status_code == 200:
                    print(f"✅ {name}: Accessible")
                    
                    # Check for required form elements
                    content = response.content.decode('utf-8')
                    required_elements = [
                        'id="transaction-form"',
                        'id="customer"',
                        'id="location"',
                        'id="from_currency"',
                        'id="from_amount"'
                    ]
                    
                    for element in required_elements:
                        if element not in content:
                            form_test['issues'].append(f"Missing form element: {element}")
                    
                    # Check for JavaScript includes
                    js_files = [
                        'arena-doviz.js',
                        'common.js'
                    ]
                    
                    for js_file in js_files:
                        if js_file not in content:
                            form_test['issues'].append(f"Missing JavaScript file: {js_file}")
                    
                    if form_test['issues']:
                        print(f"⚠️ {name}: Issues found - {len(form_test['issues'])} problems")
                        for issue in form_test['issues']:
                            print(f"     - {issue}")
                    else:
                        print(f"✅ {name}: All required elements present")
                        
                else:
                    form_test['issues'].append(f"HTTP {response.status_code} error")
                    print(f"❌ {name}: HTTP {response.status_code}")
                
                self.test_results['forms_tested'].append(form_test)
                
            except Exception as e:
                error_msg = f"Error testing {name}: {str(e)}"
                self.test_results['issues_found'].append(error_msg)
                print(f"❌ {name}: {str(e)}")
    
    def test_form_api_endpoints(self):
        """Test API endpoints used by forms"""
        print("\n🔌 Testing Form API Endpoints...")
        
        api_endpoints = [
            ('/api/v1/customers/customers/', 'Customer API'),
            ('/api/v1/locations/locations/', 'Location API'),
            ('/api/v1/currencies/currencies/', 'Currency API'),
            ('/api/v1/transactions/types/', 'Transaction Types API'),
            ('/api/v1/currencies/rates/current/?location=DXB', 'Exchange Rates API')
        ]
        
        for endpoint, name in api_endpoints:
            try:
                response = self.client.get(endpoint, **self.auth_headers)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Check data structure
                    if 'results' in data:
                        count = len(data['results'])
                        print(f"✅ {name}: Working ({count} items)")
                    elif isinstance(data, list):
                        count = len(data)
                        print(f"✅ {name}: Working ({count} items)")
                    else:
                        print(f"✅ {name}: Working")
                        
                elif response.status_code == 400:
                    # Some endpoints require parameters
                    print(f"⚠️ {name}: Requires parameters (400)")
                else:
                    error_msg = f"{name} returned HTTP {response.status_code}"
                    self.test_results['issues_found'].append(error_msg)
                    print(f"❌ {name}: HTTP {response.status_code}")
                    
            except Exception as e:
                error_msg = f"Error testing {name}: {str(e)}"
                self.test_results['issues_found'].append(error_msg)
                print(f"❌ {name}: {str(e)}")
    
    def test_javascript_functionality(self):
        """Test JavaScript functionality (basic checks)"""
        print("\n🖥️ Testing JavaScript Functionality...")
        
        js_files = [
            'src/static/js/arena-doviz.js',
            'src/static/js/transactions/common.js',
            'src/static/js/transactions/internal_transfer.js',
            'src/static/js/transactions/external_transfer.js',
            'src/static/js/transactions/international_transfer.js'
        ]
        
        for js_file in js_files:
            if os.path.exists(js_file):
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for common JavaScript errors
                issues = []
                
                # Check for super calls without extends
                if 'super.' in content and 'extends' not in content:
                    issues.append("Contains 'super' calls without class extension")
                
                # Check for undefined method calls
                if 'this.showAlert' in content and 'showAlert(' not in content:
                    issues.append("Calls undefined 'showAlert' method")
                
                # Check for TransactionUtils usage
                if 'TransactionUtils.' in content:
                    # This is good - using the global instance
                    pass
                
                if issues:
                    self.test_results['issues_found'].extend([f"{js_file}: {issue}" for issue in issues])
                    print(f"⚠️ {os.path.basename(js_file)}: {len(issues)} issues")
                    for issue in issues:
                        print(f"     - {issue}")
                else:
                    print(f"✅ {os.path.basename(js_file)}: No obvious issues")
            else:
                error_msg = f"JavaScript file not found: {js_file}"
                self.test_results['issues_found'].append(error_msg)
                print(f"❌ {os.path.basename(js_file)}: File not found")
    
    def generate_recommendations(self):
        """Generate recommendations based on test results"""
        print("\n💡 Generating Recommendations...")
        
        recommendations = []
        
        # Check for common issues
        issues = self.test_results['issues_found']
        
        if any('super' in issue for issue in issues):
            recommendations.append("Fix JavaScript inheritance issues - remove 'super' calls or add proper class extension")
        
        if any('showAlert' in issue for issue in issues):
            recommendations.append("Replace 'this.showAlert' with 'TransactionUtils.showAlert'")
        
        if any('Missing' in issue for issue in issues):
            recommendations.append("Ensure all required form elements and JavaScript files are included")
        
        if any('HTTP' in issue for issue in issues):
            recommendations.append("Check API endpoint configurations and authentication")
        
        # Add general recommendations
        recommendations.extend([
            "Test forms in actual browser environment for complete validation",
            "Implement proper error handling for API failures",
            "Add loading indicators for better user experience",
            "Validate form data before submission"
        ])
        
        self.test_results['recommendations'] = recommendations
        
        print(f"📋 Generated {len(recommendations)} recommendations")
    
    def determine_overall_status(self):
        """Determine overall test status"""
        issues_count = len(self.test_results['issues_found'])
        forms_count = len(self.test_results['forms_tested'])
        
        if issues_count == 0:
            self.test_results['overall_status'] = 'PASS'
        elif issues_count <= 3:
            self.test_results['overall_status'] = 'WARNING'
        else:
            self.test_results['overall_status'] = 'FAIL'
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 TRANSFER FORM TEST REPORT")
        print("=" * 60)
        
        status_emoji = {
            'PASS': '✅',
            'WARNING': '⚠️',
            'FAIL': '❌',
            'ERROR': '💥'
        }
        
        status = self.test_results['overall_status']
        print(f"Overall Status: {status_emoji.get(status, '❓')} {status}")
        
        print(f"\n📊 Test Summary:")
        print(f"   Forms Tested: {len(self.test_results['forms_tested'])}")
        print(f"   Issues Found: {len(self.test_results['issues_found'])}")
        
        if self.test_results['issues_found']:
            print(f"\n🚨 ISSUES FOUND ({len(self.test_results['issues_found'])}):")
            for issue in self.test_results['issues_found']:
                print(f"   - {issue}")
        
        if self.test_results.get('recommendations'):
            print(f"\n💡 RECOMMENDATIONS ({len(self.test_results['recommendations'])}):")
            for rec in self.test_results['recommendations']:
                print(f"   - {rec}")
        
        # Save detailed report
        report_path = Path('reports') / f"transfer_form_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: {report_path}")
        print("=" * 60)

def main():
    """Main function"""
    tester = TransferFormTester()
    results = tester.run_transfer_form_tests()
    return results

if __name__ == "__main__":
    main()
