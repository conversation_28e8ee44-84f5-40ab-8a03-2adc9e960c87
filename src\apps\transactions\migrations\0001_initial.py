# Generated by Django 4.2.23 on 2025-08-13 08:44

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('locations', '0001_initial'),
        ('currencies', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommissionRule',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('name', models.CharField(help_text='Descriptive name for this commission rule', max_length=100, verbose_name='Rule name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of when this rule applies', verbose_name='Description')),
                ('applicable_for', models.CharField(choices=[('all', 'All Transactions'), ('exchange', 'Currency Exchange'), ('bank_transfer', 'Bank Transfer'), ('swift', 'SWIFT Transfer'), ('cash', 'Cash Transaction'), ('internal', 'Internal Transfer'), ('courier', 'Courier Delivery')], default='all', help_text='What type of transactions this rule applies to', max_length=20, verbose_name='Applicable for')),
                ('min_amount', models.DecimalField(blank=True, decimal_places=6, help_text='Minimum transaction amount for this rule to apply', max_digits=15, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Minimum amount')),
                ('max_amount', models.DecimalField(blank=True, decimal_places=6, help_text='Maximum transaction amount for this rule to apply', max_digits=15, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Maximum amount')),
                ('commission_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed_amount', 'Fixed Amount'), ('tiered', 'Tiered'), ('hybrid', 'Hybrid (Fixed + Percentage)')], default='percentage', help_text='How commission is calculated', max_length=20, verbose_name='Commission type')),
                ('percentage_rate', models.DecimalField(blank=True, decimal_places=4, help_text='Commission percentage (e.g., 1.5 for 1.5%)', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0')), django.core.validators.MaxValueValidator(Decimal('100'))], verbose_name='Percentage rate')),
                ('fixed_amount', models.DecimalField(blank=True, decimal_places=6, help_text='Fixed commission amount', max_digits=15, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Fixed amount')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this rule is currently active', verbose_name='Is active')),
                ('priority', models.PositiveIntegerField(default=100, help_text='Rule priority (lower numbers have higher priority)', verbose_name='Priority')),
                ('effective_from', models.DateTimeField(blank=True, help_text='Date from which this rule is effective', null=True, verbose_name='Effective from')),
                ('effective_until', models.DateTimeField(blank=True, help_text='Date until which this rule is effective', null=True, verbose_name='Effective until')),
                ('commission_currency', models.ForeignKey(blank=True, help_text='Currency for commission (defaults to from_currency)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='commission_rules', to='currencies.currency', verbose_name='Commission currency')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('from_currency', models.ForeignKey(blank=True, help_text='Source currency (null for any currency)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='commission_rules_from', to='currencies.currency', verbose_name='From currency')),
                ('location', models.ForeignKey(blank=True, help_text='Location where this rule applies (null for all locations)', null=True, on_delete=django.db.models.deletion.CASCADE, to='locations.location', verbose_name='Location')),
                ('to_currency', models.ForeignKey(blank=True, help_text='Target currency (null for any currency)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='commission_rules_to', to='currencies.currency', verbose_name='To currency')),
            ],
            options={
                'verbose_name': 'Commission Rule',
                'verbose_name_plural': 'Commission Rules',
                'ordering': ['priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('transaction_number', models.CharField(help_text='Unique transaction number (auto-generated)', max_length=50, unique=True, verbose_name='Transaction number')),
                ('description', models.TextField(help_text='Description of the transaction', verbose_name='Description')),
                ('reference_number', models.CharField(blank=True, help_text='External reference number (bank reference, etc.)', max_length=100, verbose_name='Reference number')),
                ('from_amount', models.DecimalField(decimal_places=6, help_text='Amount in the from currency', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.000001'))], verbose_name='From amount')),
                ('to_amount', models.DecimalField(decimal_places=6, help_text='Amount in the to currency', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.000001'))], verbose_name='To amount')),
                ('exchange_rate', models.DecimalField(decimal_places=6, help_text='Exchange rate used for the transaction', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.000001'))], verbose_name='Exchange rate')),
                ('commission_amount', models.DecimalField(decimal_places=6, default=Decimal('0'), help_text='Commission charged for the transaction', max_digits=15, verbose_name='Commission amount')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending Approval'), ('approved', 'Approved'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rejected', 'Rejected')], default='draft', help_text='Current status of the transaction', max_length=20, verbose_name='Status')),
                ('delivery_method', models.CharField(choices=[('in_person', 'In Person'), ('courier', 'Courier'), ('bank_transfer', 'Bank Transfer'), ('swift', 'SWIFT Transfer'), ('cash', 'Cash'), ('internal', 'Internal Transfer')], default='in_person', help_text='Method of delivery for the transaction', max_length=20, verbose_name='Delivery method')),
                ('delivery_address', models.TextField(blank=True, help_text='Address for delivery (if applicable)', verbose_name='Delivery address')),
                ('tracking_code', models.CharField(blank=True, help_text='Tracking code for delivery', max_length=100, verbose_name='Tracking code')),
                ('approved_at', models.DateTimeField(blank=True, help_text='Date and time when transaction was approved', null=True, verbose_name='Approved at')),
                ('completed_at', models.DateTimeField(blank=True, help_text='Date and time when transaction was completed', null=True, verbose_name='Completed at')),
                ('step_number', models.PositiveIntegerField(default=1, help_text='Step number in multi-step transaction', verbose_name='Step number')),
                ('total_steps', models.PositiveIntegerField(default=1, help_text='Total number of steps in multi-step transaction', verbose_name='Total steps')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the transaction', verbose_name='Notes')),
                ('approved_by', models.ForeignKey(blank=True, help_text='User who approved the transaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transactions', to=settings.AUTH_USER_MODEL, verbose_name='Approved by')),
                ('commission_currency', models.ForeignKey(blank=True, help_text='Currency in which commission is charged', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='commission_transactions', to='currencies.currency', verbose_name='Commission currency')),
                ('courier', models.ForeignKey(blank=True, help_text='Courier assigned for delivery (if applicable)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='courier_transactions', to=settings.AUTH_USER_MODEL, verbose_name='Courier')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('customer', models.ForeignKey(help_text='Customer involved in the transaction', on_delete=django.db.models.deletion.PROTECT, related_name='transactions', to='customers.customer', verbose_name='Customer')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('from_currency', models.ForeignKey(help_text='Currency being exchanged from', on_delete=django.db.models.deletion.PROTECT, related_name='transactions_from', to='currencies.currency', verbose_name='From currency')),
                ('location', models.ForeignKey(help_text='Location where the transaction takes place', on_delete=django.db.models.deletion.PROTECT, related_name='transactions', to='locations.location', verbose_name='Location')),
                ('parent_transaction', models.ForeignKey(blank=True, help_text='Parent transaction for multi-step transactions', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_transactions', to='transactions.transaction', verbose_name='Parent transaction')),
                ('to_currency', models.ForeignKey(help_text='Currency being exchanged to', on_delete=django.db.models.deletion.PROTECT, related_name='transactions_to', to='currencies.currency', verbose_name='To currency')),
            ],
            options={
                'verbose_name': 'Transaction',
                'verbose_name_plural': 'Transactions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TransactionType',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('code', models.CharField(help_text='Unique code for the transaction type (e.g., JV, TSN, TRQ)', max_length=10, unique=True, verbose_name='Transaction type code')),
                ('name', models.CharField(help_text='Human-readable name for the transaction type', max_length=100, verbose_name='Transaction type name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the transaction type', verbose_name='Description')),
                ('is_exchange', models.BooleanField(default=False, help_text='Whether this transaction type involves currency exchange', verbose_name='Is exchange transaction')),
                ('requires_approval', models.BooleanField(default=False, help_text='Whether transactions of this type require approval', verbose_name='Requires approval')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this transaction type is currently active', verbose_name='Is active')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Order in which transaction types should be displayed', verbose_name='Sort order')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Transaction Type',
                'verbose_name_plural': 'Transaction Types',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='TransactionDocument',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('document_type', models.CharField(choices=[('receipt', 'Receipt'), ('invoice', 'Invoice'), ('delivery_confirmation', 'Delivery Confirmation'), ('bank_slip', 'Bank Slip'), ('swift_confirmation', 'SWIFT Confirmation'), ('courier_receipt', 'Courier Receipt'), ('customer_id', 'Customer ID Copy'), ('contract', 'Contract'), ('other', 'Other')], help_text='Type of document', max_length=50, verbose_name='Document type')),
                ('title', models.CharField(help_text='Title or description of the document', max_length=200, verbose_name='Document title')),
                ('file', models.FileField(help_text='Document file', upload_to='transaction_documents/%Y/%m/', verbose_name='File')),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True, verbose_name='File size')),
                ('mime_type', models.CharField(blank=True, help_text='MIME type of the file', max_length=100, verbose_name='MIME type')),
                ('is_required', models.BooleanField(default=False, help_text='Whether this document is required for transaction completion', verbose_name='Is required')),
                ('is_verified', models.BooleanField(default=False, help_text='Whether this document has been verified', verbose_name='Is verified')),
                ('verified_at', models.DateTimeField(blank=True, help_text='Date and time when document was verified', null=True, verbose_name='Verified at')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this document', verbose_name='Notes')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='transactions.transaction', verbose_name='Transaction')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('uploaded_by', models.ForeignKey(blank=True, help_text='User who uploaded this document', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_transaction_documents', to=settings.AUTH_USER_MODEL, verbose_name='Uploaded by')),
                ('verified_by', models.ForeignKey(blank=True, help_text='User who verified this document', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_transaction_documents', to=settings.AUTH_USER_MODEL, verbose_name='Verified by')),
            ],
            options={
                'verbose_name': 'Transaction Document',
                'verbose_name_plural': 'Transaction Documents',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='transaction',
            name='transaction_type',
            field=models.ForeignKey(help_text='Type of transaction', on_delete=django.db.models.deletion.PROTECT, related_name='transactions', to='transactions.transactiontype', verbose_name='Transaction type'),
        ),
        migrations.AddField(
            model_name='transaction',
            name='updated_by',
            field=models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by'),
        ),
        migrations.CreateModel(
            name='CommissionTier',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('min_amount', models.DecimalField(decimal_places=6, help_text='Minimum amount for this tier', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Minimum amount')),
                ('max_amount', models.DecimalField(blank=True, decimal_places=6, help_text='Maximum amount for this tier (null for unlimited)', max_digits=15, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Maximum amount')),
                ('percentage_rate', models.DecimalField(blank=True, decimal_places=4, help_text='Commission percentage for this tier', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0')), django.core.validators.MaxValueValidator(Decimal('100'))], verbose_name='Percentage rate')),
                ('fixed_amount', models.DecimalField(blank=True, decimal_places=6, help_text='Fixed commission amount for this tier', max_digits=15, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Fixed amount')),
                ('commission_rule', models.ForeignKey(help_text='The commission rule this tier belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='tiers', to='transactions.commissionrule', verbose_name='Commission rule')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Commission Tier',
                'verbose_name_plural': 'Commission Tiers',
                'ordering': ['commission_rule', 'min_amount'],
            },
        ),
        migrations.AddField(
            model_name='commissionrule',
            name='transaction_type',
            field=models.ForeignKey(blank=True, help_text='Transaction type this rule applies to (null for all types)', null=True, on_delete=django.db.models.deletion.CASCADE, to='transactions.transactiontype', verbose_name='Transaction type'),
        ),
        migrations.AddField(
            model_name='commissionrule',
            name='updated_by',
            field=models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by'),
        ),
        migrations.CreateModel(
            name='BalanceEntry',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('amount', models.DecimalField(decimal_places=6, help_text='Amount of the balance change (positive for credit, negative for debit)', max_digits=15, verbose_name='Amount')),
                ('entry_type', models.CharField(choices=[('debit', 'Debit'), ('credit', 'Credit')], help_text='Type of balance entry (debit or credit)', max_length=10, verbose_name='Entry type')),
                ('description', models.TextField(help_text='Description of the balance entry', verbose_name='Description')),
                ('running_balance', models.DecimalField(blank=True, decimal_places=6, help_text='Running balance after this entry', max_digits=15, null=True, verbose_name='Running balance')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('currency', models.ForeignKey(help_text='Currency of the balance entry', on_delete=django.db.models.deletion.PROTECT, related_name='balance_entries', to='currencies.currency', verbose_name='Currency')),
                ('customer', models.ForeignKey(blank=True, help_text='Customer whose balance is affected (null for company balance)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='balance_entries', to='customers.customer', verbose_name='Customer')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('location', models.ForeignKey(help_text='Location where the balance change occurred', on_delete=django.db.models.deletion.PROTECT, related_name='balance_entries', to='locations.location', verbose_name='Location')),
                ('transaction', models.ForeignKey(help_text='Transaction that created this balance entry', on_delete=django.db.models.deletion.PROTECT, related_name='balance_entries', to='transactions.transaction', verbose_name='Transaction')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Balance Entry',
                'verbose_name_plural': 'Balance Entries',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='transactiontype',
            index=models.Index(fields=['code'], name='transaction_code_30a833_idx'),
        ),
        migrations.AddIndex(
            model_name='transactiontype',
            index=models.Index(fields=['is_active'], name='transaction_is_acti_030b98_idx'),
        ),
        migrations.AddIndex(
            model_name='transactiontype',
            index=models.Index(fields=['is_exchange'], name='transaction_is_exch_77184f_idx'),
        ),
        migrations.AddIndex(
            model_name='transactiondocument',
            index=models.Index(fields=['transaction', 'document_type'], name='transaction_transac_2523cc_idx'),
        ),
        migrations.AddIndex(
            model_name='transactiondocument',
            index=models.Index(fields=['is_required'], name='transaction_is_requ_8d742b_idx'),
        ),
        migrations.AddIndex(
            model_name='transactiondocument',
            index=models.Index(fields=['is_verified'], name='transaction_is_veri_a0dd52_idx'),
        ),
        migrations.AddIndex(
            model_name='transactiondocument',
            index=models.Index(fields=['created_at'], name='transaction_created_416790_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['transaction_number'], name='transaction_transac_e5eca2_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['customer', 'status'], name='transaction_custome_9f3449_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['location', 'status'], name='transaction_locatio_cf380f_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['status'], name='transaction_status_71abbb_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['created_at'], name='transaction_created_67ce7b_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['from_currency', 'to_currency'], name='transaction_from_cu_e1feeb_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['parent_transaction'], name='transaction_parent__ba8440_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='commissiontier',
            unique_together={('commission_rule', 'min_amount')},
        ),
        migrations.AddIndex(
            model_name='commissionrule',
            index=models.Index(fields=['location', 'is_active'], name='transaction_locatio_7fe530_idx'),
        ),
        migrations.AddIndex(
            model_name='commissionrule',
            index=models.Index(fields=['transaction_type', 'is_active'], name='transaction_transac_def7de_idx'),
        ),
        migrations.AddIndex(
            model_name='commissionrule',
            index=models.Index(fields=['from_currency', 'to_currency'], name='transaction_from_cu_012098_idx'),
        ),
        migrations.AddIndex(
            model_name='commissionrule',
            index=models.Index(fields=['priority', 'is_active'], name='transaction_priorit_3bcfe4_idx'),
        ),
        migrations.AddIndex(
            model_name='balanceentry',
            index=models.Index(fields=['customer', 'currency'], name='transaction_custome_618951_idx'),
        ),
        migrations.AddIndex(
            model_name='balanceentry',
            index=models.Index(fields=['location', 'currency'], name='transaction_locatio_4b797e_idx'),
        ),
        migrations.AddIndex(
            model_name='balanceentry',
            index=models.Index(fields=['transaction'], name='transaction_transac_2b73a7_idx'),
        ),
        migrations.AddIndex(
            model_name='balanceentry',
            index=models.Index(fields=['created_at'], name='transaction_created_55cc4f_idx'),
        ),
        migrations.AddIndex(
            model_name='balanceentry',
            index=models.Index(fields=['entry_type'], name='transaction_entry_t_10763a_idx'),
        ),
    ]
