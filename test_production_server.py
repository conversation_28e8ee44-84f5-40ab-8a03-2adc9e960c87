#!/usr/bin/env python3
"""
Test Arena Doviz Production Server
"""
import requests
import time

def test_server():
    """Test if server is running and accessible"""
    print("🔍 Testing Arena Doviz Production Server...")
    
    endpoints = [
        ('Health Check', 'http://127.0.0.1:8000/health/'),
        ('Home Page', 'http://127.0.0.1:8000/'),
        ('Login Page', 'http://127.0.0.1:8000/accounts/login/'),
        ('Monitoring Dashboard', 'http://127.0.0.1:8000/monitoring/'),
    ]
    
    results = {}
    
    for name, url in endpoints:
        try:
            print(f"Testing {name}...")
            response = requests.get(url, timeout=10)
            results[name] = {
                'status_code': response.status_code,
                'accessible': response.status_code in [200, 302],
                'response_time': response.elapsed.total_seconds()
            }
            
            if response.status_code in [200, 302]:
                print(f"✅ {name}: Working (HTTP {response.status_code}) - {response.elapsed.total_seconds():.2f}s")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            results[name] = {
                'status_code': 0,
                'accessible': False,
                'error': 'Connection refused'
            }
            print(f"❌ {name}: Server not accessible")
        except Exception as e:
            results[name] = {
                'status_code': 0,
                'accessible': False,
                'error': str(e)
            }
            print(f"❌ {name}: Error - {str(e)}")
    
    # Summary
    working_endpoints = sum(1 for r in results.values() if r.get('accessible', False))
    total_endpoints = len(results)
    
    print(f"\n📊 Server Test Summary:")
    print(f"Working endpoints: {working_endpoints}/{total_endpoints}")
    
    if working_endpoints == total_endpoints:
        print("🎉 All endpoints are working! Server is fully operational.")
        return True
    elif working_endpoints > 0:
        print("⚠️ Some endpoints are working. Server is partially operational.")
        return True
    else:
        print("❌ No endpoints are working. Server is not operational.")
        return False

if __name__ == "__main__":
    test_server()
