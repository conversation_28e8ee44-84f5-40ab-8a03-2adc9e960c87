# Generated by Django 4.2.23 on 2025-08-13 08:44

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('currencies', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('locations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.Bo<PERSON>an<PERSON>ield(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('customer_type', models.CharField(choices=[('individual', 'Individual'), ('corporate', 'Corporate')], default='individual', help_text='Type of customer (individual or corporate)', max_length=20, verbose_name='Customer type')),
                ('first_name', models.CharField(blank=True, help_text='First name (for individual customers)', max_length=100, verbose_name='First name')),
                ('last_name', models.CharField(blank=True, help_text='Last name (for individual customers)', max_length=100, verbose_name='Last name')),
                ('company_name', models.CharField(blank=True, help_text='Company name (for corporate customers)', max_length=200, verbose_name='Company name')),
                ('phone_number', models.CharField(help_text='Primary contact phone number', max_length=20, validators=[django.core.validators.RegexValidator(message='Phone number must be entered in the format: "+*********". Up to 15 digits allowed.', regex='^\\+?1?\\d{9,15}$')], verbose_name='Phone number')),
                ('email', models.EmailField(blank=True, help_text='Email address (optional)', max_length=254, validators=[django.core.validators.EmailValidator()], verbose_name='Email')),
                ('address', models.TextField(blank=True, help_text='Full address', verbose_name='Address')),
                ('city', models.CharField(blank=True, help_text='City', max_length=100, verbose_name='City')),
                ('country', models.CharField(blank=True, help_text='Country', max_length=100, verbose_name='Country')),
                ('description', models.TextField(blank=True, help_text='Additional description or business details', verbose_name='Description')),
                ('whatsapp_group_id', models.CharField(blank=True, help_text='WhatsApp group ID for customer communications (7-8 person team + customer)', max_length=100, verbose_name='WhatsApp group ID')),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('suspended', 'Suspended'), ('blocked', 'Blocked')], default='active', help_text='Current status of the customer', max_length=20, verbose_name='Status')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0.0, help_text='Maximum negative balance allowed for this customer', max_digits=15, verbose_name='Credit limit')),
                ('notes', models.TextField(blank=True, help_text='Internal notes about this customer (not visible to customer)', verbose_name='Internal notes')),
                ('customer_code', models.CharField(blank=True, help_text='Unique customer code (auto-generated if not provided)', max_length=20, unique=True, verbose_name='Customer code')),
                ('risk_level', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='low', help_text='Risk assessment level for this customer', max_length=20, verbose_name='Risk level')),
                ('registration_date', models.DateField(auto_now_add=True, help_text='Date when customer was registered', verbose_name='Registration date')),
                ('last_transaction_date', models.DateTimeField(blank=True, help_text='Date of the last transaction with this customer', null=True, verbose_name='Last transaction date')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('preferred_currency', models.ForeignKey(blank=True, help_text="Customer's preferred currency for transactions", null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='preferred_by_customers', to='currencies.currency', verbose_name='Preferred currency')),
                ('preferred_location', models.ForeignKey(blank=True, help_text="Customer's preferred location for transactions", null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='preferred_by_customers', to='locations.location', verbose_name='Preferred location')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomerDocument',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('document_type', models.CharField(choices=[('id_card', 'ID Card'), ('passport', 'Passport'), ('business_license', 'Business License'), ('tax_certificate', 'Tax Certificate'), ('bank_statement', 'Bank Statement'), ('contract', 'Contract'), ('other', 'Other')], help_text='Type of document', max_length=50, verbose_name='Document type')),
                ('title', models.CharField(help_text='Title or description of the document', max_length=200, verbose_name='Document title')),
                ('file', models.FileField(help_text='Document file', upload_to='customer_documents/%Y/%m/', verbose_name='File')),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True, verbose_name='File size')),
                ('mime_type', models.CharField(blank=True, help_text='MIME type of the file', max_length=100, verbose_name='MIME type')),
                ('expiry_date', models.DateField(blank=True, help_text='Document expiry date (if applicable)', null=True, verbose_name='Expiry date')),
                ('is_verified', models.BooleanField(default=False, help_text='Whether this document has been verified', verbose_name='Is verified')),
                ('verified_at', models.DateTimeField(blank=True, help_text='Date and time when document was verified', null=True, verbose_name='Verified at')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this document', verbose_name='Notes')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='customers.customer', verbose_name='Customer')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('verified_by', models.ForeignKey(blank=True, help_text='User who verified this document', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_documents', to=settings.AUTH_USER_MODEL, verbose_name='Verified by')),
            ],
            options={
                'verbose_name': 'Customer Document',
                'verbose_name_plural': 'Customer Documents',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['customer', 'document_type'], name='customers_c_custome_a892d3_idx'), models.Index(fields=['is_verified'], name='customers_c_is_veri_bef30e_idx'), models.Index(fields=['expiry_date'], name='customers_c_expiry__f65026_idx'), models.Index(fields=['created_at'], name='customers_c_created_936712_idx')],
            },
        ),
        migrations.CreateModel(
            name='CustomerContact',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether this record has been soft deleted', verbose_name='Is deleted')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='Date and time when the record was deleted', null=True, verbose_name='Deleted at')),
                ('contact_type', models.CharField(choices=[('primary', 'Primary Contact'), ('secondary', 'Secondary Contact'), ('billing', 'Billing Contact'), ('technical', 'Technical Contact'), ('emergency', 'Emergency Contact')], default='primary', help_text='Type of contact', max_length=50, verbose_name='Contact type')),
                ('name', models.CharField(help_text='Name of the contact person', max_length=200, verbose_name='Contact name')),
                ('title', models.CharField(blank=True, help_text='Job title or position', max_length=100, verbose_name='Title/Position')),
                ('phone_number', models.CharField(blank=True, help_text='Contact phone number', max_length=20, validators=[django.core.validators.RegexValidator(message='Phone number must be entered in the format: "+*********". Up to 15 digits allowed.', regex='^\\+?1?\\d{9,15}$')], verbose_name='Phone number')),
                ('email', models.EmailField(blank=True, help_text='Contact email address', max_length=254, verbose_name='Email')),
                ('is_primary', models.BooleanField(default=False, help_text='Whether this is the primary contact for the customer', verbose_name='Is primary contact')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this contact', verbose_name='Notes')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contacts', to='customers.customer', verbose_name='Customer')),
                ('deleted_by', models.ForeignKey(blank=True, help_text='User who deleted this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, help_text='User who last updated this record', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Customer Contact',
                'verbose_name_plural': 'Customer Contacts',
                'ordering': ['-is_primary', 'name'],
                'indexes': [models.Index(fields=['customer', 'contact_type'], name='customers_c_custome_886907_idx'), models.Index(fields=['is_primary'], name='customers_c_is_prim_42c110_idx'), models.Index(fields=['phone_number'], name='customers_c_phone_n_490508_idx'), models.Index(fields=['email'], name='customers_c_email_a130fd_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['customer_type'], name='customers_c_custome_47da94_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['status'], name='customers_c_status_ce44cf_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['phone_number'], name='customers_c_phone_n_cabfe1_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['email'], name='customers_c_email_4fdeb3_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['customer_code'], name='customers_c_custome_116ac0_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['last_transaction_date'], name='customers_c_last_tr_798f71_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['preferred_location'], name='customers_c_preferr_7c5355_idx'),
        ),
    ]
