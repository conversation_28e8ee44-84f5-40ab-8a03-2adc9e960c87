{"overall_status": "FAIL", "timestamp": "2025-08-20T02:32:09.089984", "production_url": "http://*************:8000", "summary": {"connectivity": "ERROR", "authentication": "ERROR", "api_endpoints_passed": 0, "api_endpoints_total": 5, "pages_passed": 0, "pages_total": 5}, "detailed_results": {"timestamp": "2025-08-20T02:32:09.089984", "production_url": "http://*************:8000", "connectivity": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71EA78C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "api_endpoints": {"/api/v1/customers/customers/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /api/v1/customers/customers/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71F9DA90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "/api/v1/transactions/transactions/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /api/v1/transactions/transactions/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71F9E350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "/api/v1/currencies/currencies/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /api/v1/currencies/currencies/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71F4AB10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "/api/v1/locations/locations/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /api/v1/locations/locations/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71F4B230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "/api/v1/currencies/rates/current/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /api/v1/currencies/rates/current/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71F5EC30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}}, "authentication": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "token_received": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /api/v1/auth/token/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71FA89E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "key_pages": {"/accounts/login/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /accounts/login/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71FA9150>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "/dashboard/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /dashboard/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71EDFCE0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "/transactions/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /transactions/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71EDFF00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "/customers/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /customers/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71EDFAC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}, "/reports/": {"status": "ERROR", "status_code": 0, "response_time_ms": 0, "accessible": false, "error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: /reports/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D71EDF9B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"}}}}