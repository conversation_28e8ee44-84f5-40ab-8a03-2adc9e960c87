"""
Signal handlers for Arena Doviz Reports app.
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import GeneratedReport, ReportTemplate
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=ReportTemplate)
def report_template_post_save(sender, instance, created, **kwargs):
    """Handle report template post-save signal."""
    if created:
        logger.info(f"New report template created: {instance.name}")
    else:
        logger.info(f"Report template updated: {instance.name}")


@receiver(post_delete, sender=ReportTemplate)
def report_template_post_delete(sender, instance, **kwargs):
    """Handle report template post-delete signal."""
    logger.info(f"Report template deleted: {instance.name}")


@receiver(post_save, sender=GeneratedReport)
def generated_report_post_save(sender, instance, created, **kwargs):
    """Handle generated report post-save signal."""
    if created:
        logger.info(f"New report generated: {instance.template.name}")
    else:
        logger.info(f"Generated report updated: {instance.template.name}")


@receiver(post_delete, sender=GeneratedReport)
def generated_report_post_delete(sender, instance, **kwargs):
    """Handle generated report post-delete signal."""
    logger.info(f"Generated report deleted: {instance.template.name}")
