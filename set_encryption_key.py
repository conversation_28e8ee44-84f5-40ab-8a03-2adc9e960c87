#!/usr/bin/env python
"""
Script to generate and set encryption key for Arena Doviz production.
"""

import os
import secrets
import base64

def generate_encryption_key():
    """Generate a secure 256-bit encryption key."""
    # Generate 32 bytes (256 bits) of random data
    key_bytes = secrets.token_bytes(32)
    # Encode as base64 for storage
    key_b64 = base64.b64encode(key_bytes).decode('utf-8')
    return key_b64

def main():
    """Generate and set encryption key."""
    print("🔐 Arena Doviz Encryption Key Setup")
    print("=" * 40)
    
    # Check if key already exists
    existing_key = os.environ.get('ARENA_ENCRYPTION_KEY')
    if existing_key:
        print(f"✅ Encryption key already set: {existing_key[:16]}...")
        return existing_key
    
    # Generate new key
    encryption_key = generate_encryption_key()
    
    # Set environment variable for current session
    os.environ['ARENA_ENCRYPTION_KEY'] = encryption_key
    
    print(f"🔑 Generated encryption key: {encryption_key[:16]}...")
    print(f"✅ Environment variable ARENA_ENCRYPTION_KEY set for current session")
    
    # Instructions for permanent setup
    print("\n📝 To make this permanent, add the following to your environment:")
    print(f"   set ARENA_ENCRYPTION_KEY={encryption_key}")
    print("\n   Or add to your .env file:")
    print(f"   ARENA_ENCRYPTION_KEY={encryption_key}")
    
    return encryption_key

if __name__ == '__main__':
    main()
