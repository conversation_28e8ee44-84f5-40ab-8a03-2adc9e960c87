"""
Signal handlers for Arena Doviz Currencies app.
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Currency, ExchangeRate
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Currency)
def currency_post_save(sender, instance, created, **kwargs):
    """Handle currency post-save signal."""
    if created:
        logger.info(f"New currency created: {instance.code}")
    else:
        logger.info(f"Currency updated: {instance.code}")


@receiver(post_delete, sender=Currency)
def currency_post_delete(sender, instance, **kwargs):
    """Handle currency post-delete signal."""
    logger.info(f"Currency deleted: {instance.code}")


@receiver(post_save, sender=ExchangeRate)
def exchange_rate_post_save(sender, instance, created, **kwargs):
    """Handle exchange rate post-save signal."""
    if created:
        logger.info(f"New exchange rate created: {instance.from_currency} -> {instance.to_currency}")
    else:
        logger.info(f"Exchange rate updated: {instance.from_currency} -> {instance.to_currency}")


@receiver(post_delete, sender=ExchangeRate)
def exchange_rate_post_delete(sender, instance, **kwargs):
    """Handle exchange rate post-delete signal."""
    logger.info(f"Exchange rate deleted: {instance.from_currency} -> {instance.to_currency}")
