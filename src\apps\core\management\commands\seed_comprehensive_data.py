"""
Comprehensive data seeding script for Arena Doviz Exchange System.
Creates sample data for all entities including customers, operators, transactions, and more.
"""

import random
import uuid
from decimal import Decimal
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction

from apps.locations.models import Location, LocationSettings
from apps.customers.models import Customer, CustomerContact, CustomerDocument
from apps.transactions.models import (
    Transaction, TransactionType, BalanceEntry
)
from apps.transactions.commission_models import CommissionRule
from apps.currencies.models import Currency, ExchangeRate

User = get_user_model()


class Command(BaseCommand):
    help = 'Seed comprehensive sample data for Arena Doviz system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before seeding',
        )
        parser.add_argument(
            '--customers',
            type=int,
            default=50,
            help='Number of customers to create (default: 50)',
        )
        parser.add_argument(
            '--transactions',
            type=int,
            default=200,
            help='Number of transactions to create (default: 200)',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting comprehensive data seeding...'))
        
        if options['clear']:
            self.clear_existing_data()
        
        with transaction.atomic():
            # Create data in order of dependencies
            self.create_currencies()
            self.create_locations()
            self.create_users()
            self.create_transaction_types()
            self.create_commission_rules()
            self.create_exchange_rates()
            self.create_customers(options['customers'])
            self.create_transactions(options['transactions'])
            
        self.stdout.write(self.style.SUCCESS('Data seeding completed successfully!'))
        self.print_summary()

    def clear_existing_data(self):
        """Clear existing data (except superuser)."""
        self.stdout.write('Clearing existing data...')
        
        # Clear in reverse dependency order
        Transaction.objects.all().delete()
        BalanceEntry.objects.all().delete()
        CustomerDocument.objects.all().delete()
        CustomerContact.objects.all().delete()
        Customer.objects.all().delete()
        CommissionRule.objects.all().delete()
        ExchangeRate.objects.all().delete()
        TransactionType.objects.all().delete()
        LocationSettings.objects.all().delete()
        Location.objects.all().delete()
        
        # Keep superuser, delete others
        User.objects.filter(is_superuser=False).delete()
        
        self.stdout.write(self.style.WARNING('Existing data cleared.'))

    def create_currencies(self):
        """Create currency records."""
        self.stdout.write('Creating currencies...')
        
        currencies = [
            {'code': 'USD', 'name': 'US Dollar', 'symbol': '$', 'decimal_places': 2},
            {'code': 'AED', 'name': 'UAE Dirham', 'symbol': 'د.إ', 'decimal_places': 2},
            {'code': 'IRR', 'name': 'Iranian Rial', 'symbol': '﷼', 'decimal_places': 0},
        ]
        
        for curr_data in currencies:
            currency, created = Currency.objects.get_or_create(
                code=curr_data['code'],
                defaults=curr_data
            )
            if created:
                self.stdout.write(f'  Created currency: {currency.code}')

    def create_locations(self):
        """Create location records."""
        self.stdout.write('Creating locations...')
        
        locations_data = [
            {
                'name': 'Istanbul Main Office',
                'code': 'IST',
                'country': 'Turkey',
                'city': 'Istanbul',
                'address': 'Sultanahmet, Fatih, Istanbul',
                'phone_number': '+905551234567',
                'email': '<EMAIL>',
                'timezone': 'Europe/Istanbul',
                'is_main_office': True,
                'commission_rate': Decimal('0.0100'),
                'opening_time': '09:00',
                'closing_time': '18:00',
            },
            {
                'name': 'Tabriz Branch',
                'code': 'TBZ',
                'country': 'Iran',
                'city': 'Tabriz',
                'address': 'Valiasr Street, Tabriz',
                'phone_number': '+984133334444',
                'email': '<EMAIL>',
                'timezone': 'Asia/Tehran',
                'commission_rate': Decimal('0.0120'),
                'opening_time': '08:30',
                'closing_time': '17:30',
            },
            {
                'name': 'Tehran Branch',
                'code': 'THR',
                'country': 'Iran',
                'city': 'Tehran',
                'address': 'Ferdowsi Square, Tehran',
                'phone_number': '+982133334444',
                'email': '<EMAIL>',
                'timezone': 'Asia/Tehran',
                'commission_rate': Decimal('0.0110'),
                'opening_time': '08:00',
                'closing_time': '18:00',
            },
            {
                'name': 'Dubai Branch',
                'code': 'DXB',
                'country': 'UAE',
                'city': 'Dubai',
                'address': 'Gold Souk, Deira, Dubai',
                'phone_number': '+971501234567',
                'email': '<EMAIL>',
                'timezone': 'Asia/Dubai',
                'commission_rate': Decimal('0.0090'),
                'opening_time': '09:00',
                'closing_time': '19:00',
            },
            {
                'name': 'Shanghai Branch',
                'code': 'SHA',
                'country': 'China',
                'city': 'Shanghai',
                'address': 'Nanjing Road, Shanghai',
                'phone_number': '+862133334444',
                'email': '<EMAIL>',
                'timezone': 'Asia/Shanghai',
                'commission_rate': Decimal('0.0150'),
                'opening_time': '09:30',
                'closing_time': '17:30',
            },
        ]
        
        for loc_data in locations_data:
            location, created = Location.objects.get_or_create(
                code=loc_data['code'],
                defaults=loc_data
            )
            if created:
                self.stdout.write(f'  Created location: {location.name}')
                
                # Create location settings
                LocationSettings.objects.create(
                    location=location,
                    whatsapp_enabled=True,
                    email_notifications=True,
                    sms_notifications=True,
                    require_approval_above=Decimal('10000.00'),
                    auto_approve_below=Decimal('1000.00'),
                    session_timeout=30,
                    backup_enabled=True,
                    backup_frequency='daily',
                )

    def create_users(self):
        """Create user accounts."""
        self.stdout.write('Creating users...')
        
        users_data = [
            {
                'username': 'admin_istanbul',
                'email': '<EMAIL>',
                'first_name': 'Mehmet',
                'last_name': 'Yılmaz',
                'role': 'admin',
                'location_code': 'IST',
            },
            {
                'username': 'operator_istanbul',
                'email': '<EMAIL>',
                'first_name': 'Ayşe',
                'last_name': 'Demir',
                'role': 'branch_employee',
                'location_code': 'IST',
            },
            {
                'username': 'admin_tabriz',
                'email': '<EMAIL>',
                'first_name': 'علی',
                'last_name': 'احمدی',
                'role': 'admin',
                'location_code': 'TBZ',
            },
            {
                'username': 'operator_tabriz',
                'email': '<EMAIL>',
                'first_name': 'فاطمه',
                'last_name': 'محمدی',
                'role': 'branch_employee',
                'location_code': 'TBZ',
            },
            {
                'username': 'admin_tehran',
                'email': '<EMAIL>',
                'first_name': 'حسن',
                'last_name': 'رضایی',
                'role': 'admin',
                'location_code': 'THR',
            },
            {
                'username': 'operator_tehran',
                'email': '<EMAIL>',
                'first_name': 'مریم',
                'last_name': 'کریمی',
                'role': 'branch_employee',
                'location_code': 'THR',
            },
            {
                'username': 'admin_dubai',
                'email': '<EMAIL>',
                'first_name': 'Ahmed',
                'last_name': 'Al-Rashid',
                'role': 'admin',
                'location_code': 'DXB',
            },
            {
                'username': 'operator_dubai',
                'email': '<EMAIL>',
                'first_name': 'Fatima',
                'last_name': 'Al-Zahra',
                'role': 'branch_employee',
                'location_code': 'DXB',
            },
            {
                'username': 'admin_shanghai',
                'email': '<EMAIL>',
                'first_name': '王',
                'last_name': '明',
                'role': 'admin',
                'location_code': 'SHA',
            },
            {
                'username': 'operator_shanghai',
                'email': '<EMAIL>',
                'first_name': '李',
                'last_name': '华',
                'role': 'branch_employee',
                'location_code': 'SHA',
            },
            {
                'username': 'accountant_main',
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'role': 'accountant',
                'location_code': 'IST',
            },
        ]
        
        for user_data in users_data:
            location = Location.objects.get(code=user_data.pop('location_code'))
            
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    **user_data,
                    'is_active': True,
                    'location': location,
                }
            )
            
            if created:
                user.set_password('password123')  # Default password
                user.save()
                self.stdout.write(f'  Created user: {user.username}')
                
                # Assign as manager for admin users
                if user_data['role'] == 'admin' and not location.manager:
                    location.manager = user
                    location.save()

    def create_transaction_types(self):
        """Create transaction types."""
        self.stdout.write('Creating transaction types...')
        
        types_data = [
            {
                'name': 'Currency Exchange',
                'code': 'EXCHANGE',
                'description': 'Standard currency exchange transaction',
                'is_active': True,
                'requires_approval': False,
                'is_exchange': True,
            },
            {
                'name': 'Money Transfer',
                'code': 'TRANSFER',
                'description': 'Money transfer between locations',
                'is_active': True,
                'requires_approval': True,
                'is_exchange': False,
            },
            {
                'name': 'Cash Deposit',
                'code': 'DEPOSIT',
                'description': 'Cash deposit to customer account',
                'is_active': True,
                'requires_approval': False,
                'is_exchange': False,
            },
            {
                'name': 'Cash Withdrawal',
                'code': 'WITHDRAWAL',
                'description': 'Cash withdrawal from customer account',
                'is_active': True,
                'requires_approval': True,
                'is_exchange': False,
            },
        ]
        
        for type_data in types_data:
            tx_type, created = TransactionType.objects.get_or_create(
                code=type_data['code'],
                defaults=type_data
            )
            if created:
                self.stdout.write(f'  Created transaction type: {tx_type.name}')

    def create_commission_rules(self):
        """Create commission rules."""
        self.stdout.write('Creating commission rules...')
        
        locations = Location.objects.all()
        tx_types = TransactionType.objects.all()
        
        for location in locations:
            for tx_type in tx_types:
                rule, created = CommissionRule.objects.get_or_create(
                    name=f'{location.code} - {tx_type.code}',
                    defaults={
                        'location': location,
                        'transaction_type': tx_type,
                        'commission_type': 'percentage',
                        'percentage_rate': location.commission_rate * 100,  # Convert to percentage
                        'min_amount': Decimal('10.00'),
                        'max_amount': Decimal('100000.00'),
                        'is_active': True,
                    }
                )
                if created:
                    self.stdout.write(f'  Created commission rule: {rule.name}')

    def create_exchange_rates(self):
        """Create exchange rates."""
        self.stdout.write('Creating exchange rates...')
        
        # Sample exchange rates (these would normally come from external APIs)
        rates_data = [
            {'from_currency': 'USD', 'to_currency': 'AED', 'buy_rate': Decimal('3.6725'), 'sell_rate': Decimal('3.6750')},
            {'from_currency': 'AED', 'to_currency': 'USD', 'buy_rate': Decimal('0.2722'), 'sell_rate': Decimal('0.2724')},
            {'from_currency': 'USD', 'to_currency': 'IRR', 'buy_rate': Decimal('42000.00'), 'sell_rate': Decimal('42100.00')},
            {'from_currency': 'IRR', 'to_currency': 'USD', 'buy_rate': Decimal('0.0000238'), 'sell_rate': Decimal('0.0000240')},
            {'from_currency': 'AED', 'to_currency': 'IRR', 'buy_rate': Decimal('11440.00'), 'sell_rate': Decimal('11460.00')},
            {'from_currency': 'IRR', 'to_currency': 'AED', 'buy_rate': Decimal('0.0000873'), 'sell_rate': Decimal('0.0000875')},
        ]
        
        locations = Location.objects.all()

        for rate_data in rates_data:
            from_curr = Currency.objects.get(code=rate_data['from_currency'])
            to_curr = Currency.objects.get(code=rate_data['to_currency'])

            # Create exchange rates for each location
            for location in locations:
                rate, created = ExchangeRate.objects.get_or_create(
                    from_currency=from_curr,
                    to_currency=to_curr,
                    location=location,
                    effective_from__date=timezone.now().date(),
                    defaults={
                        'buy_rate': rate_data['buy_rate'],
                        'sell_rate': rate_data['sell_rate'],
                        'is_active': True,
                        'effective_from': timezone.now(),
                    }
                )
                if created:
                    self.stdout.write(f'  Created exchange rate: {from_curr.code} -> {to_curr.code} @ {location.code}')

    def create_customers(self, count):
        """Create sample customers."""
        self.stdout.write(f'Creating {count} customers...')

        # Sample customer data templates
        individual_names = [
            ('John', 'Smith'), ('Jane', 'Doe'), ('Michael', 'Johnson'), ('Sarah', 'Williams'),
            ('David', 'Brown'), ('Lisa', 'Davis'), ('Robert', 'Miller'), ('Emily', 'Wilson'),
            ('Ahmed', 'Al-Rashid'), ('Fatima', 'Al-Zahra'), ('Omar', 'Hassan'), ('Aisha', 'Ibrahim'),
            ('علی', 'احمدی'), ('فاطمه', 'محمدی'), ('حسن', 'رضایی'), ('مریم', 'کریمی'),
            ('王', '明'), ('李', '华'), ('张', '伟'), ('刘', '芳'),
            ('Mehmet', 'Yılmaz'), ('Ayşe', 'Demir'), ('Mustafa', 'Kaya'), ('Zeynep', 'Özkan'),
        ]

        company_names = [
            'Global Trading LLC', 'International Exchange Corp', 'Middle East Imports',
            'Asia Pacific Trading', 'European Business Solutions', 'Turkish Exports Ltd',
            'Iranian Trade Company', 'Dubai Commerce Group', 'Shanghai Trading Co',
            'Istanbul Business Center', 'Tehran Commercial Group', 'UAE Trading House',
        ]

        locations = list(Location.objects.all())

        for i in range(count):
            is_corporate = random.choice([True, False])
            location = random.choice(locations)

            if is_corporate:
                company_name = random.choice(company_names)
                customer_data = {
                    'customer_type': 'corporate',
                    'company_name': f'{company_name} {i+1}',
                }
            else:
                first_name, last_name = random.choice(individual_names)
                customer_data = {
                    'customer_type': 'individual',
                    'first_name': first_name,
                    'last_name': last_name,
                }

            # Common customer data
            customer_data.update({
                'customer_code': f'CUS{str(i+1).zfill(6)}',
                'phone_number': f'+{random.randint(90, 98)}{random.randint(5000000000, 5999999999)}',
                'email': f'customer{i+1}@example.com',
                'preferred_location': location,
                'status': random.choice(['active', 'active', 'active', 'inactive']),  # 75% active
                'notes': f'Sample customer {i+1} created by seeding script',
            })

            customer = Customer.objects.create(**customer_data)
            self.stdout.write(f'  Created customer: {customer.get_display_name()}')

            # Create customer contacts
            self.create_customer_contacts(customer)

            # Create customer documents (for some customers)
            if random.choice([True, False]):
                self.create_customer_documents(customer)

    def create_customer_contacts(self, customer):
        """Create contacts for a customer."""
        contact_types = ['primary', 'secondary', 'billing', 'technical', 'emergency']

        # Primary contact (always create)
        CustomerContact.objects.create(
            customer=customer,
            contact_type='primary',
            name=customer.get_display_name(),
            phone_number=customer.phone_number,
            email=customer.email,
            is_primary=True,
            notes='Primary contact'
        )

        # Additional contacts (random)
        for contact_type in random.sample(contact_types[1:], random.randint(1, 2)):
            name = f'{customer.get_display_name()} - {contact_type.title()}'

            CustomerContact.objects.create(
                customer=customer,
                contact_type=contact_type,
                name=name,
                phone_number=customer.phone_number if contact_type == 'secondary' else '',
                email=customer.email if contact_type == 'billing' else '',
                is_primary=False,
                notes=f'Additional {contact_type} contact'
            )

    def create_customer_documents(self, customer):
        """Create documents for a customer."""
        doc_types = ['id_card', 'passport', 'business_license', 'tax_certificate']

        for doc_type in random.sample(doc_types, random.randint(1, 2)):
            # Create a dummy file path (in real system, this would be an actual file)
            from django.core.files.base import ContentFile
            dummy_content = ContentFile(b'Dummy document content', name=f'{doc_type}.pdf')

            CustomerDocument.objects.create(
                customer=customer,
                document_type=doc_type,
                title=f'{doc_type.replace("_", " ").title()} - {customer.get_display_name()}',
                file=dummy_content,
                notes=f'Sample {doc_type} document'
            )

    def create_transactions(self, count):
        """Create sample transactions."""
        self.stdout.write(f'Creating {count} transactions...')

        customers = list(Customer.objects.filter(status='active'))
        locations = list(Location.objects.filter(is_active=True))
        tx_types = list(TransactionType.objects.filter(is_active=True))
        currencies = list(Currency.objects.all())
        users = list(User.objects.filter(is_active=True))

        if not customers:
            self.stdout.write(self.style.WARNING('No active customers found. Skipping transaction creation.'))
            return

        for i in range(count):
            customer = random.choice(customers)
            location = random.choice(locations)
            tx_type = random.choice(tx_types)
            created_by = random.choice([u for u in users if u.location == location] or users)

            # Random transaction amounts
            from_currency = random.choice(currencies)
            to_currency = random.choice([c for c in currencies if c != from_currency])

            # Get exchange rate
            try:
                exchange_rate_obj = ExchangeRate.objects.filter(
                    from_currency=from_currency,
                    to_currency=to_currency,
                    location=location,
                    is_active=True
                ).first()

                if not exchange_rate_obj:
                    # Try to get any exchange rate for this currency pair
                    exchange_rate_obj = ExchangeRate.objects.filter(
                        from_currency=from_currency,
                        to_currency=to_currency,
                        is_active=True
                    ).first()

                if exchange_rate_obj:
                    exchange_rate = exchange_rate_obj.sell_rate
                else:
                    exchange_rate = Decimal('1.0')
            except Exception as e:
                self.stdout.write(f'Error getting exchange rate: {e}')
                exchange_rate = Decimal('1.0')

            from_amount = Decimal(str(random.uniform(100, 10000))).quantize(Decimal('0.01'))
            to_amount = (from_amount * exchange_rate).quantize(Decimal('0.01'))
            commission_amount = (from_amount * location.commission_rate).quantize(Decimal('0.01'))

            # Create transaction
            transaction_data = {
                'customer': customer,
                'location': location,
                'transaction_type': tx_type,
                'from_currency': from_currency,
                'to_currency': to_currency,
                'from_amount': from_amount,
                'to_amount': to_amount,
                'exchange_rate': exchange_rate,
                'commission_amount': commission_amount,
                'status': random.choice(['completed', 'completed', 'completed', 'pending', 'cancelled']),
                'description': f'Sample {tx_type.name.lower()} transaction',
                'reference_number': f'REF{random.randint(100000, 999999)}',
                'created_by': created_by,
                'created_at': timezone.now() - timedelta(days=random.randint(0, 90)),
            }

            transaction_obj = Transaction.objects.create(**transaction_data)
            self.stdout.write(f'  Created transaction: {transaction_obj.transaction_number}')



    def print_summary(self):
        """Print summary of created data."""
        self.stdout.write(self.style.SUCCESS('\n=== DATA SEEDING SUMMARY ==='))
        self.stdout.write(f'Locations: {Location.objects.count()}')
        self.stdout.write(f'Users: {User.objects.count()}')
        self.stdout.write(f'Customers: {Customer.objects.count()}')
        self.stdout.write(f'Customer Contacts: {CustomerContact.objects.count()}')
        self.stdout.write(f'Customer Documents: {CustomerDocument.objects.count()}')
        self.stdout.write(f'Transaction Types: {TransactionType.objects.count()}')
        self.stdout.write(f'Commission Rules: {CommissionRule.objects.count()}')
        self.stdout.write(f'Exchange Rates: {ExchangeRate.objects.count()}')
        self.stdout.write(f'Transactions: {Transaction.objects.count()}')
        self.stdout.write(f'Balance Entries: {BalanceEntry.objects.count()}')
        self.stdout.write(self.style.SUCCESS('=== END SUMMARY ===\n'))
