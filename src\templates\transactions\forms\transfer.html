{% extends 'transactions/base_form.html' %}
{% load i18n %}

{% block additional_basic_fields %}
<div class="col-md-6">
    <div class="mb-3">
        <label for="recipient_customer" class="form-label">{% trans "Recipient Customer" %} <span class="text-danger">*</span></label>
        <select class="form-select" id="recipient_customer" name="recipient_customer" required>
            <option value="">{% trans "Select recipient..." %}</option>
        </select>
        <div class="form-text">{% trans "Customer who will receive the transfer" %}</div>
    </div>
</div>
{% endblock %}

{% block transaction_specific_fields %}
<!-- Transfer Type Selection -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-arrow-left-right"></i>
            {% trans "Transfer Type" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <div class="mb-3">
                    <label for="transfer_type" class="form-label">{% trans "Transfer Type" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="transfer_type" name="transfer_type" required>
                        <option value="">{% trans "Select transfer type..." %}</option>
                        <option value="internal">{% trans "Internal Transfer (Between Customers)" %}</option>
                        <option value="external">{% trans "External Transfer (To Bank/Other Institution)" %}</option>
                        <option value="international">{% trans "International Transfer" %}</option>
                    </select>
                    <div class="form-text">{% trans "Choose the type of money transfer" %}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Details Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-send"></i>
            {% trans "Transfer Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Transfer Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.000001" required placeholder="0.00">
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="commission_amount" class="form-label">
                        {% trans "Transfer Fee" %}
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="calculateCommission()">
                            <i class="bi bi-calculator"></i> {% trans "Auto Calculate" %}
                        </button>
                    </label>
                    <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.000001" placeholder="0.00">
                    <div class="form-text" id="commission_info"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="tracking_code" class="form-label">{% trans "Tracking Code" %}</label>
                    <input type="text" class="form-control" id="tracking_code" name="tracking_code" placeholder="{% trans 'Transfer tracking code...' %}">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Method Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-credit-card"></i>
            {% trans "Transfer Method" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="delivery_method" class="form-label">{% trans "Transfer Method" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="delivery_method" name="delivery_method" required>
                        <option value="">{% trans "Select transfer method..." %}</option>
                        <option value="bank_transfer">{% trans "Bank Transfer" %}</option>
                        <option value="swift">{% trans "SWIFT Transfer" %}</option>
                        <option value="internal">{% trans "Internal Transfer" %}</option>
                        <option value="courier">{% trans "Courier Delivery" %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="destination_location" class="form-label">{% trans "Destination Location" %}</label>
                    <select class="form-select" id="destination_location" name="destination_location">
                        <option value="">{% trans "Select destination..." %}</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="mb-3" id="swift-details" style="display: none;">
            <label for="swift_code" class="form-label">{% trans "SWIFT Code" %}</label>
            <input type="text" class="form-control" id="swift_code" name="swift_code" placeholder="{% trans 'SWIFT/BIC code...' %}">
        </div>
        
        <div class="mb-3" id="bank-details" style="display: none;">
            <label for="bank_details" class="form-label">{% trans "Bank Details" %}</label>
            <textarea class="form-control" id="bank_details" name="bank_details" rows="3" placeholder="{% trans 'Bank name, account number, routing details...' %}"></textarea>
        </div>
        
        <div class="mb-3">
            <label for="delivery_address" class="form-label">{% trans "Recipient Address" %}</label>
            <textarea class="form-control" id="delivery_address" name="delivery_address" rows="2" placeholder="{% trans 'Recipient address...' %}"></textarea>
        </div>
    </div>
</div>

<!-- Multi-step Transfer -->
<div class="card mb-3">
    <div class="card-header">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="is_multi_step" name="is_multi_step">
            <label class="form-check-label" for="is_multi_step">
                {% trans "Multi-step Transfer" %}
            </label>
        </div>
    </div>
    <div class="card-body" id="multi-step-section" style="display: none;">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="step_number" class="form-label">{% trans "Step Number" %}</label>
                    <input type="number" class="form-control" id="step_number" name="step_number" min="1" value="1">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="total_steps" class="form-label">{% trans "Total Steps" %}</label>
                    <input type="number" class="form-control" id="total_steps" name="total_steps" min="1" value="1">
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="/static/js/transactions/transfer.js"></script>
{% endblock %}
