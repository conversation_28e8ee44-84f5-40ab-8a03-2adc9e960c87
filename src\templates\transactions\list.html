{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transactions" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-arrow-left-right"></i>
                {% trans "Transactions" %}
            </h1>
            <a href="{% url 'transactions_web:add' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {% trans "New Transaction" %}
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {% trans "Filters" %}
                </h5>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-3">
                        <label for="status-filter" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status-filter" name="status">
                            <option value="">{% trans "All Statuses" %}</option>
                            <option value="draft">{% trans "Draft" %}</option>
                            <option value="pending">{% trans "Pending" %}</option>
                            <option value="approved">{% trans "Approved" %}</option>
                            <option value="completed">{% trans "Completed" %}</option>
                            <option value="cancelled">{% trans "Cancelled" %}</option>
                            <option value="rejected">{% trans "Rejected" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="customer-filter" class="form-label">{% trans "Customer" %}</label>
                        <select class="form-select" id="customer-filter" name="customer">
                            <option value="">{% trans "All Customers" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="location-filter" class="form-label">{% trans "Location" %}</label>
                        <select class="form-select" id="location-filter" name="location">
                            <option value="">{% trans "All Locations" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="currency-filter" class="form-label">{% trans "Currency" %}</label>
                        <select class="form-select" id="currency-filter" name="currency">
                            <option value="">{% trans "All Currencies" %}</option>
                            <option value="USD">USD</option>
                            <option value="AED">AED</option>
                            <option value="IRR">IRR</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date-from" class="form-label">{% trans "Date From" %}</label>
                        <input type="date" class="form-control" id="date-from" name="date_from">
                    </div>
                    <div class="col-md-3">
                        <label for="date-to" class="form-label">{% trans "Date To" %}</label>
                        <input type="date" class="form-control" id="date-to" name="date_to">
                    </div>
                    <div class="col-md-3">
                        <label for="search" class="form-label">{% trans "Search" %}</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="{% trans 'Transaction number, customer name...' %}">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search"></i>
                            {% trans "Filter" %}
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                            <i class="bi bi-x-circle"></i>
                            {% trans "Clear" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {% trans "Transaction List" %}
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i>
                        {% trans "Refresh" %}
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                        <i class="bi bi-download"></i>
                        {% trans "Export" %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="transactions-table">
                        <thead>
                            <tr>
                                <th>{% trans "Transaction #" %}</th>
                                <th>{% trans "Customer" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Location" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be populated by DataTables -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Actions Modal -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalTitle">{% trans "Transaction Action" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="action-form">
                    <input type="hidden" id="action-transaction-id">
                    <input type="hidden" id="action-type">
                    
                    <div class="mb-3">
                        <label for="action-notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="action-notes" name="notes" rows="3" placeholder="{% trans 'Optional notes for this action...' %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="confirm-action">{% trans "Confirm" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let transactionsTable;
let selectedTransactionId = null;
let selectedAction = null;

// Ensure jQuery is loaded before running
function initializeTransactionPage() {
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded. Retrying in 100ms...');
        setTimeout(initializeTransactionPage, 100);
        return;
    }

    console.log('Initializing transaction page...');

    // Initialize page
    loadCustomers();
    loadLocations();
    initializeTransactionsTable();

    // Event handlers
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        transactionsTable.ajax.reload();
    });

    $('#clear-filters').on('click', function() {
        $('#filter-form')[0].reset();
        transactionsTable.ajax.reload();
    });

    $('#refresh-btn').on('click', function() {
        transactionsTable.ajax.reload();
    });

    $('#export-btn').on('click', function() {
        exportTransactions();
    });

    // Action modal handlers
    $('#confirm-action').on('click', function() {
        performTransactionAction();
    });
}

// Try to initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeTransactionPage);
} else {
    initializeTransactionPage();
}

// Also try with jQuery when available
if (typeof $ !== 'undefined') {
    $(document).ready(initializeTransactionPage);
}

function loadCustomers() {
    $.ajax({
        url: '/api/v1/customers/customers/',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
        },
        success: function(data) {
            const select = $('#customer-filter');
            select.find('option:not(:first)').remove();
            
            if (data.results) {
                data.results.forEach(function(customer) {
                    select.append(`<option value="${customer.id}">${customer.display_name}</option>`);
                });
            }
        }
    });
}

function loadLocations() {
    $.ajax({
        url: '/api/v1/locations/locations/',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
        },
        success: function(data) {
            const select = $('#location-filter');
            select.find('option:not(:first)').remove();
            
            if (data.results) {
                data.results.forEach(function(location) {
                    select.append(`<option value="${location.id}">${location.name}</option>`);
                });
            }
        }
    });
}

function initializeTransactionsTable() {
    // Check if DataTable already exists and destroy it
    if ($.fn.DataTable.isDataTable('#transactions-table')) {
        $('#transactions-table').DataTable().destroy();
        console.log('Existing DataTable destroyed');
    }

    // Debug authentication
    const accessToken = ArenaDoviz.auth.getAccessToken();
    console.log('JWT Access Token:', accessToken ? 'Present' : 'Missing');

    if (!accessToken) {
        console.error('No JWT access token found. User may need to log in again.');
        showAlert('error', 'Authentication required. Please refresh the page and log in again.');
        return;
    }

    const columns = [
        {
            data: 'transaction_number',
            name: 'transaction_number',
            render: function(data, type, row) {
                return `<code>${data}</code>`;
            }
        },
        {
            data: 'customer_name',
            name: 'customer__first_name',
            render: function(data, type, row) {
                return `<a href="/customers/${row.customer}/" class="text-decoration-none">${data}</a>`;
            }
        },
        { data: 'transaction_type_name', name: 'transaction_type__name' },
        {
            data: 'display_amount',
            name: 'from_amount',
            className: 'text-end'
        },
        {
            data: 'status_display',
            name: 'status',
            render: function(data, type, row) {
                const statusClass = getStatusClass(row.status);
                return `<span class="badge bg-${statusClass}">${data}</span>`;
            }
        },
        { data: 'location_name', name: 'location__name' },
        {
            data: 'created_at',
            name: 'created_at',
            render: function(data, type, row) {
                return new Date(data).toLocaleString();
            }
        },
        {
            data: null,
            orderable: false,
            render: function(data, type, row) {
                return generateActionButtons(row);
            }
        }
    ];

    // Initialize DataTable with custom configuration
    transactionsTable = $('#transactions-table').DataTable({
        responsive: true,
        processing: true,
        serverSide: true,
        order: [[6, 'desc']], // Order by created_at
        ajax: {
            url: '/api/v1/transactions/transactions/',
            type: 'GET',
            headers: {
                'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
            },
            data: function(d) {
                // Add filter parameters
                const formData = new FormData($('#filter-form')[0]);
                const filters = {};

                for (let [key, value] of formData.entries()) {
                    if (value) {
                        filters[key] = value;
                    }
                }

                return Object.assign({
                    page: Math.floor(d.start / d.length) + 1,
                    page_size: d.length,
                    search: d.search.value,
                    ordering: d.order.length > 0 ?
                        (d.order[0].dir === 'desc' ? '-' : '') + columns[d.order[0].column].name :
                        '-created_at'
                }, filters);
            },
            dataSrc: function(json) {
                console.log('Transaction API response:', json);
                console.log('Transaction count:', json.count);
                console.log('Transaction results:', json.results);

                json.recordsTotal = json.count;
                json.recordsFiltered = json.count;
                return json.results;
            },
            error: function(xhr, error, code) {
                console.error('DataTables AJAX error:', error, code, xhr.responseText);
                console.error('Request details:', {
                    url: '/api/v1/transactions/transactions/',
                    headers: {
                        'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
                    },
                    status: xhr.status,
                    statusText: xhr.statusText
                });

                let errorMessage = 'Failed to load transactions.';
                if (xhr.status === 401) {
                    errorMessage += ' Authentication required.';
                } else if (xhr.status === 403) {
                    errorMessage += ' Access denied.';
                } else if (xhr.status === 404) {
                    errorMessage += ' API endpoint not found.';
                } else if (xhr.status >= 500) {
                    errorMessage += ' Server error.';
                }

                showAlert('error', errorMessage);
            }
        },
        columns: columns,
        language: {
            processing: "Loading transactions...",
            emptyTable: "No transactions found",
            zeroRecords: "No matching transactions found"
        }
    });
}



function generateActionButtons(transaction) {
    let buttons = `
        <a href="/transactions/${transaction.id}/" class="btn btn-sm btn-outline-primary me-1" title="{% trans 'View Details' %}">
            <i class="bi bi-eye"></i>
        </a>
    `;
    
    if (transaction.can_be_approved) {
        buttons += `
            <button class="btn btn-sm btn-outline-success me-1" onclick="showActionModal('${transaction.id}', 'approve')" title="{% trans 'Approve' %}">
                <i class="bi bi-check-circle"></i>
            </button>
        `;
    }
    
    if (transaction.status === 'approved') {
        buttons += `
            <button class="btn btn-sm btn-outline-info me-1" onclick="showActionModal('${transaction.id}', 'complete')" title="{% trans 'Complete' %}">
                <i class="bi bi-check2-circle"></i>
            </button>
        `;
    }
    
    if (transaction.can_be_cancelled) {
        buttons += `
            <button class="btn btn-sm btn-outline-danger" onclick="showActionModal('${transaction.id}', 'cancel')" title="{% trans 'Cancel' %}">
                <i class="bi bi-x-circle"></i>
            </button>
        `;
    }
    
    return buttons;
}

function showActionModal(transactionId, actionType) {
    $('#action-transaction-id').val(transactionId);
    $('#action-type').val(actionType);
    
    const titles = {
        'approve': '{% trans "Approve Transaction" %}',
        'complete': '{% trans "Complete Transaction" %}',
        'cancel': '{% trans "Cancel Transaction" %}',
        'reject': '{% trans "Reject Transaction" %}'
    };
    
    $('#actionModalTitle').text(titles[actionType]);
    $('#actionModal').modal('show');
}

function performTransactionAction() {
    const transactionId = $('#action-transaction-id').val();
    const actionType = $('#action-type').val();
    const notes = $('#action-notes').val();
    
    $.ajax({
        url: `/api/v1/transactions/transactions/${transactionId}/${actionType}/`,
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({ notes: notes }),
        success: function(data) {
            $('#actionModal').modal('hide');
            loadTransactions();
            showAlert('success', data.message || '{% trans "Action completed successfully" %}');
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || '{% trans "Action failed" %}';
            showAlert('danger', error);
        }
    });
}



function exportTransactions() {
    // Check authentication
    const accessToken = ArenaDoviz.auth.getAccessToken();
    if (!accessToken) {
        showAlert('error', 'Authentication required. Please refresh the page and log in again.');
        return;
    }

    // Get filter parameters
    const formData = new FormData($('#filter-form')[0]);
    const params = new URLSearchParams();

    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }

    params.append('format', 'csv'); // Use CSV format since it's implemented

    // Create a temporary form to submit with authentication
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = '/api/v1/transactions/transactions/export/?' + params.toString();
    form.style.display = 'none';

    // Add authorization header via a hidden iframe approach
    // Since we can't add headers to window.open, we'll use fetch and blob download
    fetch('/api/v1/transactions/transactions/export/?' + params.toString(), {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + accessToken
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Export failed: ${response.status} ${response.statusText}`);
        }
        return response.blob();
    })
    .then(blob => {
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `transactions_${new Date().toISOString().slice(0, 10)}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        showAlert('success', 'Transactions exported successfully!');
    })
    .catch(error => {
        console.error('Export error:', error);
        showAlert('error', 'Failed to export transactions: ' + error.message);
    });
}

function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('main .container-fluid').prepend(alert);

    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

function getStatusClass(status) {
    // Map transaction status to Bootstrap badge classes
    const statusMap = {
        'draft': 'secondary',
        'pending': 'warning',
        'approved': 'info',
        'completed': 'success',
        'cancelled': 'danger',
        'rejected': 'danger'
    };

    return statusMap[status] || 'secondary';
}
</script>
{% endblock %}
