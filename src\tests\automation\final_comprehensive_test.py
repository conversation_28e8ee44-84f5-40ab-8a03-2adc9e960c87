"""
Final Comprehensive Test for Arena Doviz Issues Resolution
"""
import os
import sys
import json
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
import django
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import AccessToken

User = get_user_model()

class FinalComprehensiveTest:
    """Final comprehensive test for all issues"""
    
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'transfer_forms': {},
            'production_deployment': {},
            'security_audit': {},
            'overall_status': 'UNKNOWN'
        }
        
    def run_comprehensive_test(self):
        """Run comprehensive test of all fixes"""
        print("🎯 FINAL COMPREHENSIVE TEST - ARENA DOVIZ")
        print("=" * 60)
        
        # Test Issue 1: Transfer Form Fixes
        self.test_transfer_form_fixes()
        
        # Test Issue 2: Production Deployment
        self.test_production_deployment()
        
        # Test Security Audit
        self.test_security_audit()
        
        # Generate final report
        self.generate_final_report()
        
        return self.test_results
    
    def test_transfer_form_fixes(self):
        """Test transfer form functionality fixes"""
        print("\n🔧 Testing Transfer Form Fixes...")
        
        # Create test client and authenticate
        client = Client()
        user, created = User.objects.get_or_create(
            username='final_test_user',
            defaults={'email': '<EMAIL>', 'is_staff': True}
        )
        client.force_login(user)
        
        # Test form URLs
        form_urls = [
            '/transactions/transfer/internal/add/',
            '/transactions/transfer/external/add/',
            '/transactions/transfer/international/add/'
        ]
        
        form_results = {}
        
        for url in form_urls:
            try:
                response = client.get(url)
                form_name = url.split('/')[-3].title() + ' Transfer'
                
                form_results[form_name] = {
                    'url': url,
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'has_form': 'id="transaction-form"' in response.content.decode('utf-8') if response.status_code == 200 else False
                }
                
                if response.status_code == 200:
                    print(f"✅ {form_name}: Accessible and working")
                else:
                    print(f"❌ {form_name}: HTTP {response.status_code}")
                    
            except Exception as e:
                form_results[form_name] = {
                    'url': url,
                    'status_code': 0,
                    'accessible': False,
                    'error': str(e)
                }
                print(f"❌ {form_name}: Error - {str(e)}")
        
        # Test API endpoints used by forms
        token = AccessToken.for_user(user)
        api_headers = {'HTTP_AUTHORIZATION': f'Bearer {str(token)}'}
        
        api_endpoints = [
            '/api/v1/customers/customers/',
            '/api/v1/locations/locations/',
            '/api/v1/currencies/currencies/',
            '/api/v1/transactions/types/'
        ]
        
        api_results = {}
        
        for endpoint in api_endpoints:
            try:
                response = client.get(endpoint, **api_headers)
                api_name = endpoint.split('/')[-2].title()
                
                api_results[api_name] = {
                    'endpoint': endpoint,
                    'status_code': response.status_code,
                    'working': response.status_code == 200
                }
                
                if response.status_code == 200:
                    print(f"✅ {api_name} API: Working")
                else:
                    print(f"❌ {api_name} API: HTTP {response.status_code}")
                    
            except Exception as e:
                api_results[api_name] = {
                    'endpoint': endpoint,
                    'status_code': 0,
                    'working': False,
                    'error': str(e)
                }
                print(f"❌ {api_name} API: Error - {str(e)}")
        
        self.test_results['transfer_forms'] = {
            'forms': form_results,
            'apis': api_results,
            'status': 'PASS' if all(f['accessible'] for f in form_results.values()) else 'FAIL'
        }
    
    def test_production_deployment(self):
        """Test production deployment readiness"""
        print("\n🚀 Testing Production Deployment Readiness...")
        
        deployment_checks = {}
        
        # Check if production scripts exist
        scripts = [
            'src/scripts/setup_production.py',
            'src/scripts/security_audit.py',
            'src/tests/load_testing/load_test.py',
            'src/apps/core/monitoring.py',
            'src/apps/core/management/commands/health_check.py'
        ]
        
        for script in scripts:
            script_name = os.path.basename(script)
            deployment_checks[script_name] = {
                'path': script,
                'exists': os.path.exists(script),
                'size': os.path.getsize(script) if os.path.exists(script) else 0
            }
            
            if os.path.exists(script):
                print(f"✅ {script_name}: Available")
            else:
                print(f"❌ {script_name}: Missing")
        
        # Check production settings
        try:
            from config.settings import prod
            
            prod_settings = {
                'debug_disabled': not getattr(prod, 'DEBUG', True),
                'allowed_hosts_configured': len(getattr(prod, 'ALLOWED_HOSTS', [])) > 0,
                'database_configured': 'postgresql' in str(getattr(prod, 'DATABASES', {})),
                'security_headers': hasattr(prod, 'SECURE_SSL_REDIRECT')
            }
            
            deployment_checks['production_settings'] = prod_settings
            
            for setting, status in prod_settings.items():
                if status:
                    print(f"✅ {setting.replace('_', ' ').title()}: Configured")
                else:
                    print(f"⚠️ {setting.replace('_', ' ').title()}: Needs attention")
                    
        except Exception as e:
            deployment_checks['production_settings'] = {'error': str(e)}
            print(f"❌ Production settings: Error - {str(e)}")
        
        self.test_results['production_deployment'] = {
            'scripts': deployment_checks,
            'status': 'READY' if all(c.get('exists', False) for c in deployment_checks.values() if isinstance(c, dict) and 'exists' in c) else 'PARTIAL'
        }
    
    def test_security_audit(self):
        """Test security audit functionality"""
        print("\n🔒 Testing Security Audit...")
        
        try:
            # Import and test security auditor
            sys.path.append('src/scripts')
            from security_audit import SecurityAuditor
            
            auditor = SecurityAuditor()
            
            # Run basic audit checks
            auditor.audit_django_settings()
            auditor.audit_user_accounts()
            auditor.audit_file_permissions()
            
            security_results = {
                'auditor_available': True,
                'findings_count': len(auditor.findings),
                'recommendations_count': len(auditor.recommendations),
                'status': 'WORKING'
            }
            
            print(f"✅ Security Audit: Working ({security_results['findings_count']} findings)")
            
        except Exception as e:
            security_results = {
                'auditor_available': False,
                'error': str(e),
                'status': 'ERROR'
            }
            print(f"❌ Security Audit: Error - {str(e)}")
        
        self.test_results['security_audit'] = security_results
    
    def generate_final_report(self):
        """Generate final comprehensive report"""
        print("\n" + "=" * 60)
        print("📊 FINAL COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        # Determine overall status
        transfer_status = self.test_results['transfer_forms']['status']
        deployment_status = self.test_results['production_deployment']['status']
        security_status = self.test_results['security_audit']['status']
        
        if all(status in ['PASS', 'READY', 'WORKING'] for status in [transfer_status, deployment_status, security_status]):
            self.test_results['overall_status'] = 'ALL_ISSUES_RESOLVED'
            overall_emoji = '🎉'
        elif transfer_status == 'PASS':
            self.test_results['overall_status'] = 'TRANSFER_FORMS_FIXED'
            overall_emoji = '✅'
        else:
            self.test_results['overall_status'] = 'ISSUES_REMAIN'
            overall_emoji = '⚠️'
        
        print(f"Overall Status: {overall_emoji} {self.test_results['overall_status']}")
        
        # Transfer Forms Report
        print(f"\n🔧 TRANSFER FORMS: {transfer_status}")
        forms = self.test_results['transfer_forms']['forms']
        working_forms = sum(1 for f in forms.values() if f['accessible'])
        print(f"   Working Forms: {working_forms}/{len(forms)}")
        
        for form_name, result in forms.items():
            status_icon = "✅" if result['accessible'] else "❌"
            print(f"   {status_icon} {form_name}")
        
        # API Status
        apis = self.test_results['transfer_forms']['apis']
        working_apis = sum(1 for a in apis.values() if a['working'])
        print(f"   Working APIs: {working_apis}/{len(apis)}")
        
        # Production Deployment Report
        print(f"\n🚀 PRODUCTION DEPLOYMENT: {deployment_status}")
        scripts = self.test_results['production_deployment']['scripts']
        available_scripts = sum(1 for s in scripts.values() if isinstance(s, dict) and s.get('exists', False))
        script_count = sum(1 for s in scripts.values() if isinstance(s, dict) and 'exists' in s)
        print(f"   Available Scripts: {available_scripts}/{script_count}")
        
        # Security Audit Report
        print(f"\n🔒 SECURITY AUDIT: {security_status}")
        if self.test_results['security_audit']['auditor_available']:
            findings = self.test_results['security_audit']['findings_count']
            print(f"   Security Findings: {findings}")
        
        # Summary
        print(f"\n📋 SUMMARY:")
        if self.test_results['overall_status'] == 'ALL_ISSUES_RESOLVED':
            print("🎉 ALL CRITICAL ISSUES HAVE BEEN RESOLVED!")
            print("   ✅ Transfer forms are working correctly")
            print("   ✅ Production deployment scripts are ready")
            print("   ✅ Security audit system is functional")
            print("   🚀 System is ready for production deployment")
        elif self.test_results['overall_status'] == 'TRANSFER_FORMS_FIXED':
            print("✅ TRANSFER FORMS ISSUES RESOLVED!")
            print("   ✅ All transfer forms are now accessible and working")
            print("   ✅ API endpoints are functioning correctly")
            print("   🔧 Production deployment tools are available")
        else:
            print("⚠️ SOME ISSUES REMAIN")
            print("   Please review the detailed findings above")
        
        # Save detailed report
        report_path = Path('reports') / f"final_comprehensive_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: {report_path}")
        print("=" * 60)

def main():
    """Main function"""
    tester = FinalComprehensiveTest()
    results = tester.run_comprehensive_test()
    return results

if __name__ == "__main__":
    main()
