"""
Signal handlers for Arena Doviz Customers app.
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Customer
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Customer)
def customer_post_save(sender, instance, created, **kwargs):
    """Handle customer post-save signal."""
    if created:
        logger.info(f"New customer created: {instance.customer_code}")
    else:
        logger.info(f"Customer updated: {instance.customer_code}")


@receiver(post_delete, sender=Customer)
def customer_post_delete(sender, instance, **kwargs):
    """Handle customer post-delete signal."""
    logger.info(f"Customer deleted: {instance.customer_code}")
