#!/usr/bin/env python3
"""
Production Setup Script for Arena Doviz
"""
import os
import sys
import subprocess
import secrets
from pathlib import Path
from cryptography.fernet import Fernet

def generate_encryption_key():
    """Generate a new encryption key"""
    return Fernet.generate_key().decode()

def generate_secret_key():
    """Generate a new Django secret key"""
    return secrets.token_urlsafe(50)

def create_env_file():
    """Create production environment file"""
    env_content = f"""# Arena Doviz Production Environment Configuration
# Generated on {os.popen('date').read().strip()}

# Django Settings
DJANGO_SETTINGS_MODULE=config.settings.prod
SECRET_KEY={generate_secret_key()}

# Database Configuration (PostgreSQL recommended)
DB_NAME=arena_doviz_prod
DB_USER=arena_user
DB_PASSWORD=your_secure_password_here
DB_HOST=localhost
DB_PORT=5432

# Encryption Key (REQUIRED for production)
ARENA_ENCRYPTION_KEY={generate_encryption_key()}

# Security Settings
ARENA_USE_HTTPS=false
# Set to true when SSL certificate is configured

# Email Configuration
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=true

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
"""
    
    env_file = Path('.env.production')
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print(f"✅ Created {env_file}")
    print("⚠️  Please update the database and email credentials in .env.production")
    return env_file

def setup_postgresql():
    """Setup PostgreSQL database"""
    print("\n🐘 PostgreSQL Setup Instructions:")
    print("1. Install PostgreSQL:")
    print("   Ubuntu/Debian: sudo apt-get install postgresql postgresql-contrib")
    print("   CentOS/RHEL: sudo yum install postgresql-server postgresql-contrib")
    print("   Windows: Download from https://www.postgresql.org/download/windows/")
    
    print("\n2. Create database and user:")
    print("   sudo -u postgres psql")
    print("   CREATE DATABASE arena_doviz_prod;")
    print("   CREATE USER arena_user WITH PASSWORD 'your_secure_password';")
    print("   GRANT ALL PRIVILEGES ON DATABASE arena_doviz_prod TO arena_user;")
    print("   \\q")
    
    print("\n3. Update .env.production with your database credentials")

def setup_nginx():
    """Create Nginx configuration"""
    nginx_config = """# Arena Doviz Nginx Configuration
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Redirect HTTP to HTTPS (uncomment when SSL is configured)
    # return 301 https://$server_name$request_uri;
    
    # Static files
    location /static/ {
        alias /path/to/arena-doviz/src/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Media files
    location /media/ {
        alias /path/to/arena-doviz/src/media/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # Django application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Security headers
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
}

# HTTPS configuration (uncomment when SSL certificate is ready)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com www.your-domain.com;
#     
#     ssl_certificate /path/to/ssl/certificate.crt;
#     ssl_certificate_key /path/to/ssl/private.key;
#     
#     # SSL configuration
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # Include the location blocks from above
# }
"""
    
    nginx_file = Path('nginx.conf.example')
    with open(nginx_file, 'w') as f:
        f.write(nginx_config)
    
    print(f"✅ Created {nginx_file}")
    print("📝 Copy this configuration to your Nginx sites-available directory")

def setup_systemd():
    """Create systemd service file"""
    service_config = """[Unit]
Description=Arena Doviz Django Application
After=network.target postgresql.service

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/arena-doviz/src
Environment=DJANGO_SETTINGS_MODULE=config.settings.prod
EnvironmentFile=/path/to/arena-doviz/.env.production
ExecStart=/path/to/arena-doviz/venv/bin/python manage.py runserver 127.0.0.1:8000
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
"""
    
    service_file = Path('arena-doviz.service.example')
    with open(service_file, 'w') as f:
        f.write(service_config)
    
    print(f"✅ Created {service_file}")
    print("📝 Copy this to /etc/systemd/system/ and update paths")

def create_backup_script():
    """Create database backup script"""
    backup_script = """#!/bin/bash
# Arena Doviz Database Backup Script

# Configuration
DB_NAME="arena_doviz_prod"
DB_USER="arena_user"
BACKUP_DIR="/var/backups/arena-doviz"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/arena_doviz_backup_$DATE.sql"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create database backup
pg_dump -h localhost -U $DB_USER -d $DB_NAME > $BACKUP_FILE

# Compress the backup
gzip $BACKUP_FILE

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_FILE.gz"
"""
    
    backup_file = Path('backup_database.sh')
    with open(backup_file, 'w') as f:
        f.write(backup_script)
    
    os.chmod(backup_file, 0o755)
    print(f"✅ Created {backup_file}")
    print("📝 Add to crontab for automated backups: 0 2 * * * /path/to/backup_database.sh")

def main():
    """Main setup function"""
    print("🚀 Arena Doviz Production Setup")
    print("=" * 50)
    
    # Create environment file
    env_file = create_env_file()
    
    # Setup database instructions
    setup_postgresql()
    
    # Create Nginx configuration
    print("\n🌐 Creating Nginx Configuration...")
    setup_nginx()
    
    # Create systemd service
    print("\n⚙️ Creating Systemd Service...")
    setup_systemd()
    
    # Create backup script
    print("\n💾 Creating Backup Script...")
    create_backup_script()
    
    print("\n" + "=" * 50)
    print("🎉 Production Setup Complete!")
    print("\n📋 Next Steps:")
    print("1. Update database credentials in .env.production")
    print("2. Install and configure PostgreSQL")
    print("3. Install and configure Nginx")
    print("4. Set up SSL certificate (Let's Encrypt recommended)")
    print("5. Configure systemd service")
    print("6. Set up automated backups")
    print("7. Run: python manage.py migrate --settings=config.settings.prod")
    print("8. Run: python manage.py collectstatic --settings=config.settings.prod")
    print("9. Create superuser: python manage.py createsuperuser --settings=config.settings.prod")
    print("\n⚠️  Security Checklist:")
    print("- Change default passwords")
    print("- Configure firewall")
    print("- Set up SSL certificate")
    print("- Configure monitoring")
    print("- Test backup and restore procedures")

if __name__ == "__main__":
    main()
