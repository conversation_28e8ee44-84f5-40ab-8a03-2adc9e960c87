"""
Complete Production Deployment Test for Arena Doviz
"""
import os
import sys
import json
import time
import subprocess
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
import django
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import AccessToken

User = get_user_model()

class CompleteProductionTest:
    """Complete production deployment test"""
    
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'production_components': {},
            'transfer_forms': {},
            'monitoring_system': {},
            'security_features': {},
            'performance_metrics': {},
            'overall_status': 'UNKNOWN'
        }
        
    def run_complete_production_test(self):
        """Run complete production deployment test"""
        print("🚀 COMPLETE ARENA DOVIZ PRODUCTION DEPLOYMENT TEST")
        print("=" * 70)
        
        # Test all production components
        self.test_production_files()
        self.test_transfer_forms()
        self.test_monitoring_system()
        self.test_security_features()
        self.test_performance_metrics()
        self.test_deployment_scripts()
        
        # Generate final report
        self.generate_final_report()
        
        return self.test_results
    
    def test_production_files(self):
        """Test all production configuration files"""
        print("\n📁 Testing Production Configuration Files...")
        
        # Get the correct project root (3 levels up from this file)
        project_root = Path(__file__).parent.parent.parent.parent

        production_files = {
            str(project_root / '.env.production'): 'Environment configuration',
            str(project_root / 'nginx.conf.production'): 'Nginx configuration',
            str(project_root / 'arena-doviz.service'): 'Systemd service file',
            str(project_root / 'arena-doviz-backup.service'): 'Backup service file',
            str(project_root / 'arena-doviz-backup.timer'): 'Backup timer file',
            str(project_root / 'src/scripts/setup_production.py'): 'Production setup script',
            str(project_root / 'src/scripts/setup_postgresql.py'): 'PostgreSQL setup script',
            str(project_root / 'src/scripts/setup_ssl.sh'): 'SSL setup script',
            str(project_root / 'src/scripts/deploy_production.py'): 'Complete deployment script',
            str(project_root / 'src/scripts/backup_database.sh'): 'Database backup script',
            str(project_root / 'src/scripts/security_audit.py'): 'Security audit script',
            str(project_root / 'src/tests/load_testing/load_test.py'): 'Load testing script',
            str(project_root / 'src/apps/core/monitoring.py'): 'Monitoring system',
            str(project_root / 'src/templates/monitoring/dashboard.html'): 'Monitoring dashboard'
        }
        
        file_results = {}
        
        for file_path, description in production_files.items():
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                file_results[file_path] = {
                    'exists': True,
                    'size_kb': round(file_size / 1024, 2),
                    'description': description,
                    'status': 'READY'
                }
                print(f"✅ {description}: Ready ({file_results[file_path]['size_kb']} KB)")
            else:
                file_results[file_path] = {
                    'exists': False,
                    'description': description,
                    'status': 'MISSING'
                }
                print(f"❌ {description}: Missing")
        
        self.test_results['production_components'] = file_results
    
    def test_transfer_forms(self):
        """Test transfer form functionality"""
        print("\n🔧 Testing Transfer Forms...")
        
        # Create test client and authenticate
        client = Client()
        user, created = User.objects.get_or_create(
            username='production_test_user',
            defaults={'email': '<EMAIL>', 'is_staff': True}
        )
        client.force_login(user)
        
        # Test form URLs
        form_urls = [
            '/transactions/transfer/internal/add/',
            '/transactions/transfer/external/add/',
            '/transactions/transfer/international/add/'
        ]
        
        form_results = {}
        
        for url in form_urls:
            try:
                response = client.get(url)
                form_name = url.split('/')[-3].title() + ' Transfer'
                
                form_results[form_name] = {
                    'url': url,
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'has_form': 'id="transaction-form"' in response.content.decode('utf-8') if response.status_code == 200 else False
                }
                
                if response.status_code == 200:
                    print(f"✅ {form_name}: Working")
                else:
                    print(f"❌ {form_name}: HTTP {response.status_code}")
                    
            except Exception as e:
                form_results[form_name] = {
                    'url': url,
                    'status_code': 0,
                    'accessible': False,
                    'error': str(e)
                }
                print(f"❌ {form_name}: Error - {str(e)}")
        
        self.test_results['transfer_forms'] = form_results
    
    def test_monitoring_system(self):
        """Test monitoring system"""
        print("\n📊 Testing Monitoring System...")
        
        monitoring_results = {}
        
        # Test monitoring endpoints
        client = Client()
        user = User.objects.first()
        if user:
            client.force_login(user)
            
            # Test health check
            try:
                response = client.get('/health/')
                monitoring_results['health_check'] = {
                    'status_code': response.status_code,
                    'working': response.status_code == 200
                }
                
                if response.status_code == 200:
                    print("✅ Health Check Endpoint: Working")
                else:
                    print(f"❌ Health Check Endpoint: HTTP {response.status_code}")
                    
            except Exception as e:
                monitoring_results['health_check'] = {
                    'status_code': 0,
                    'working': False,
                    'error': str(e)
                }
                print(f"❌ Health Check Endpoint: Error - {str(e)}")
            
            # Test monitoring dashboard
            try:
                response = client.get('/monitoring/')
                monitoring_results['dashboard'] = {
                    'status_code': response.status_code,
                    'working': response.status_code == 200
                }
                
                if response.status_code == 200:
                    print("✅ Monitoring Dashboard: Working")
                else:
                    print(f"❌ Monitoring Dashboard: HTTP {response.status_code}")
                    
            except Exception as e:
                monitoring_results['dashboard'] = {
                    'status_code': 0,
                    'working': False,
                    'error': str(e)
                }
                print(f"❌ Monitoring Dashboard: Error - {str(e)}")
        
        # Test monitoring scripts
        project_root = Path(__file__).parent.parent.parent.parent
        monitoring_scripts = [
            str(project_root / 'src/apps/core/monitoring.py'),
            str(project_root / 'src/apps/core/management/commands/health_check.py')
        ]

        for script in monitoring_scripts:
            script_name = os.path.basename(script)
            monitoring_results[script_name] = {
                'exists': os.path.exists(script),
                'executable': os.access(script, os.R_OK) if os.path.exists(script) else False
            }

            if os.path.exists(script):
                print(f"✅ {script_name}: Available")
            else:
                print(f"❌ {script_name}: Missing")
        
        self.test_results['monitoring_system'] = monitoring_results
    
    def test_security_features(self):
        """Test security features"""
        print("\n🔒 Testing Security Features...")
        
        security_results = {}
        
        # Test security audit script
        project_root = Path(__file__).parent.parent.parent.parent
        security_script = str(project_root / 'src/scripts/security_audit.py')
        if os.path.exists(security_script):
            security_results['security_audit'] = {
                'exists': True,
                'executable': os.access(security_script, os.R_OK)
            }
            print("✅ Security Audit Script: Available")
        else:
            security_results['security_audit'] = {
                'exists': False,
                'executable': False
            }
            print("❌ Security Audit Script: Missing")
        
        # Test production settings
        try:
            from config.settings import prod
            
            security_settings = {
                'debug_disabled': not getattr(prod, 'DEBUG', True),
                'allowed_hosts_configured': len(getattr(prod, 'ALLOWED_HOSTS', [])) > 0,
                'encryption_key_configured': hasattr(prod, 'ARENA_ENCRYPTION_KEY'),
                'security_headers_configured': hasattr(prod, 'SECURE_SSL_REDIRECT')
            }
            
            security_results['production_settings'] = security_settings
            
            for setting, status in security_settings.items():
                if status:
                    print(f"✅ {setting.replace('_', ' ').title()}: Configured")
                else:
                    print(f"⚠️ {setting.replace('_', ' ').title()}: Needs attention")
                    
        except Exception as e:
            security_results['production_settings'] = {'error': str(e)}
            print(f"❌ Production settings: Error - {str(e)}")
        
        self.test_results['security_features'] = security_results
    
    def test_performance_metrics(self):
        """Test performance metrics"""
        print("\n⚡ Testing Performance Metrics...")
        
        performance_results = {}
        
        # Test load testing script
        project_root = Path(__file__).parent.parent.parent.parent
        load_test_script = str(project_root / 'src/tests/load_testing/load_test.py')
        if os.path.exists(load_test_script):
            performance_results['load_testing'] = {
                'exists': True,
                'size_kb': round(os.path.getsize(load_test_script) / 1024, 2)
            }
            print("✅ Load Testing Script: Available")
        else:
            performance_results['load_testing'] = {
                'exists': False
            }
            print("❌ Load Testing Script: Missing")
        
        # Test database performance
        try:
            from django.db import connection
            start_time = time.time()
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            db_response_time = (time.time() - start_time) * 1000
            performance_results['database'] = {
                'response_time_ms': round(db_response_time, 2),
                'status': 'GOOD' if db_response_time < 100 else 'SLOW'
            }
            
            print(f"✅ Database Response Time: {db_response_time:.2f}ms")
            
        except Exception as e:
            performance_results['database'] = {
                'error': str(e),
                'status': 'ERROR'
            }
            print(f"❌ Database Performance: Error - {str(e)}")
        
        self.test_results['performance_metrics'] = performance_results
    
    def test_deployment_scripts(self):
        """Test deployment scripts"""
        print("\n🚀 Testing Deployment Scripts...")

        project_root = Path(__file__).parent.parent.parent.parent
        deployment_scripts = {
            str(project_root / 'src/scripts/deploy_production.py'): 'Complete deployment automation',
            str(project_root / 'src/scripts/setup_production.py'): 'Production environment setup',
            str(project_root / 'src/scripts/setup_postgresql.py'): 'PostgreSQL setup',
            str(project_root / 'src/scripts/setup_ssl.sh'): 'SSL certificate setup',
            str(project_root / 'src/scripts/backup_database.sh'): 'Database backup automation'
        }
        
        script_results = {}
        
        for script_path, description in deployment_scripts.items():
            if os.path.exists(script_path):
                script_results[script_path] = {
                    'exists': True,
                    'size_kb': round(os.path.getsize(script_path) / 1024, 2),
                    'description': description,
                    'status': 'READY'
                }
                print(f"✅ {description}: Ready")
            else:
                script_results[script_path] = {
                    'exists': False,
                    'description': description,
                    'status': 'MISSING'
                }
                print(f"❌ {description}: Missing")
        
        self.test_results['deployment_scripts'] = script_results
    
    def generate_final_report(self):
        """Generate final comprehensive report"""
        print("\n" + "=" * 70)
        print("📊 COMPLETE PRODUCTION DEPLOYMENT TEST REPORT")
        print("=" * 70)
        
        # Calculate overall status
        total_components = 0
        working_components = 0
        
        # Count production files
        for file_result in self.test_results['production_components'].values():
            total_components += 1
            if file_result.get('exists', False):
                working_components += 1
        
        # Count transfer forms
        for form_result in self.test_results['transfer_forms'].values():
            total_components += 1
            if form_result.get('accessible', False):
                working_components += 1
        
        # Count monitoring components
        for monitor_result in self.test_results['monitoring_system'].values():
            total_components += 1
            if monitor_result.get('working', False) or monitor_result.get('exists', False):
                working_components += 1
        
        # Calculate success rate
        success_rate = (working_components / total_components * 100) if total_components > 0 else 0
        
        if success_rate >= 95:
            self.test_results['overall_status'] = 'PRODUCTION_READY'
            status_emoji = '🎉'
        elif success_rate >= 85:
            self.test_results['overall_status'] = 'MOSTLY_READY'
            status_emoji = '✅'
        elif success_rate >= 70:
            self.test_results['overall_status'] = 'NEEDS_WORK'
            status_emoji = '⚠️'
        else:
            self.test_results['overall_status'] = 'NOT_READY'
            status_emoji = '❌'
        
        print(f"Overall Status: {status_emoji} {self.test_results['overall_status']}")
        print(f"Success Rate: {success_rate:.1f}% ({working_components}/{total_components})")
        
        # Component breakdown
        print(f"\n📋 COMPONENT BREAKDOWN:")
        
        # Production files
        prod_files = self.test_results['production_components']
        ready_files = sum(1 for f in prod_files.values() if f.get('exists', False))
        print(f"   Production Files: {ready_files}/{len(prod_files)} ready")
        
        # Transfer forms
        transfer_forms = self.test_results['transfer_forms']
        working_forms = sum(1 for f in transfer_forms.values() if f.get('accessible', False))
        print(f"   Transfer Forms: {working_forms}/{len(transfer_forms)} working")
        
        # Monitoring system
        monitoring = self.test_results['monitoring_system']
        working_monitoring = sum(1 for m in monitoring.values() if m.get('working', False) or m.get('exists', False))
        print(f"   Monitoring Components: {working_monitoring}/{len(monitoring)} working")
        
        # Final verdict
        print(f"\n🎯 FINAL VERDICT:")
        if self.test_results['overall_status'] == 'PRODUCTION_READY':
            print("🎉 ARENA DOVIZ IS FULLY PRODUCTION READY!")
            print("   ✅ All critical components are working")
            print("   ✅ Transfer forms are functional")
            print("   ✅ Monitoring system is operational")
            print("   ✅ Security features are configured")
            print("   ✅ Deployment scripts are ready")
            print("   🚀 System can be deployed to production immediately")
        elif self.test_results['overall_status'] == 'MOSTLY_READY':
            print("✅ ARENA DOVIZ IS MOSTLY PRODUCTION READY!")
            print("   ✅ Core functionality is working")
            print("   ⚠️ Minor issues need to be addressed")
            print("   🔧 Review warnings and fix before deployment")
        else:
            print("⚠️ ARENA DOVIZ NEEDS MORE WORK")
            print("   ❌ Critical components are missing or broken")
            print("   🔧 Address all issues before production deployment")
        
        # Save detailed report
        report_path = Path('reports') / f"complete_production_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: {report_path}")
        print("=" * 70)

def main():
    """Main function"""
    tester = CompleteProductionTest()
    results = tester.run_complete_production_test()
    return results

if __name__ == "__main__":
    main()
