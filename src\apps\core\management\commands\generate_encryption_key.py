"""
Django management command to generate encryption keys for Arena Doviz.
"""

from django.core.management.base import BaseCommand
from apps.core.encryption import generate_encryption_key


class Command(BaseCommand):
    """Generate a new AES-256 encryption key for sensitive data."""
    
    help = 'Generate a new AES-256 encryption key for Arena Doviz sensitive data encryption'
    
    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--output-format',
            choices=['env', 'key-only'],
            default='env',
            help='Output format: env (environment variable format) or key-only (just the key)'
        )
        
        parser.add_argument(
            '--quiet',
            action='store_true',
            help='Only output the key/env variable without additional text'
        )
    
    def handle(self, *args, **options):
        """Handle the command execution."""
        try:
            # Generate new encryption key
            key = generate_encryption_key()
            
            if options['output_format'] == 'env':
                output = f"ARENA_ENCRYPTION_KEY={key}"
            else:
                output = key
            
            if options['quiet']:
                self.stdout.write(output)
            else:
                self.stdout.write(
                    self.style.SUCCESS('Successfully generated encryption key!')
                )
                self.stdout.write('')
                
                if options['output_format'] == 'env':
                    self.stdout.write('Add this to your environment variables:')
                    self.stdout.write('')
                    self.stdout.write(self.style.WARNING(output))
                    self.stdout.write('')
                    self.stdout.write('For .env file:')
                    self.stdout.write(output)
                else:
                    self.stdout.write('Encryption key:')
                    self.stdout.write('')
                    self.stdout.write(self.style.WARNING(key))
                
                self.stdout.write('')
                self.stdout.write(
                    self.style.WARNING(
                        'IMPORTANT: Store this key securely! '
                        'If you lose it, encrypted data cannot be recovered.'
                    )
                )
                self.stdout.write('')
                self.stdout.write(
                    'For production deployment:'
                )
                self.stdout.write('1. Set the ARENA_ENCRYPTION_KEY environment variable')
                self.stdout.write('2. Ensure the key is not stored in version control')
                self.stdout.write('3. Use secure key management practices')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to generate encryption key: {str(e)}')
            )
            return
