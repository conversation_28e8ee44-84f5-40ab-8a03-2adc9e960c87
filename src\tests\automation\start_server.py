"""
Start Django server for testing
"""
import subprocess
import sys
import os
import time

def start_django_server():
    """Start Django development server"""
    # Change to src directory
    src_dir = os.path.join(os.path.dirname(__file__), '..', '..', '..')
    src_dir = os.path.abspath(src_dir)
    
    print(f"Starting Django server from: {src_dir}")
    
    # Start the server
    try:
        process = subprocess.Popen([
            sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'
        ], cwd=src_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("Django server started. Waiting for it to be ready...")
        time.sleep(5)  # Give server time to start
        
        return process
    except Exception as e:
        print(f"Failed to start Django server: {e}")
        return None

if __name__ == "__main__":
    start_django_server()
