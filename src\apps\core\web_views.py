"""
Web views for Arena Doviz Core app.
"""

from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.contrib import messages
from django.urls import reverse
import logging

logger = logging.getLogger(__name__)


@method_decorator(login_required, name='dispatch')
class DashboardView(TemplateView):
    """
    Dashboard view that requires authentication.
    """
    template_name = 'core/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.request.user
        return context


@method_decorator(login_required, name='dispatch')
class AnalyticsView(TemplateView):
    """
    Analytics view that requires authentication.
    """
    template_name = 'core/analytics.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.request.user
        return context


@method_decorator(login_required, name='dispatch')
class FrontendTestView(TemplateView):
    """
    Frontend testing view for debugging and validation.
    """
    template_name = 'test_frontend.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.request.user
        return context
