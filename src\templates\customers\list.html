{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Customers" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-people"></i>
                {% trans "Customers" %}
            </h1>
            <a href="{% url 'customers_web:add' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {% trans "New Customer" %}
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {% trans "Filters" %}
                </h5>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-3">
                        <label for="customer-type-filter" class="form-label">{% trans "Customer Type" %}</label>
                        <select class="form-select" id="customer-type-filter" name="customer_type">
                            <option value="">{% trans "All Types" %}</option>
                            <option value="individual">{% trans "Individual" %}</option>
                            <option value="corporate">{% trans "Corporate" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="status-filter" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status-filter" name="status">
                            <option value="">{% trans "All Statuses" %}</option>
                            <option value="active">{% trans "Active" %}</option>
                            <option value="inactive">{% trans "Inactive" %}</option>
                            <option value="suspended">{% trans "Suspended" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="location-filter" class="form-label">{% trans "Preferred Location" %}</label>
                        <select class="form-select" id="location-filter" name="preferred_location">
                            <option value="">{% trans "All Locations" %}</option>
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="search-input" class="form-label">{% trans "Search Customer" %}</label>
                        <div class="position-relative">
                            <input type="text" class="form-control" id="search-input" name="search"
                                   placeholder="{% trans 'Search by name or code...' %}" autocomplete="off">
                            <div id="search-suggestions" class="position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm"
                                 style="display: none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label for="date-from" class="form-label">{% trans "Registration Date From" %}</label>
                        <input type="date" class="form-control" id="date-from" name="date_from">
                    </div>
                    
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i>
                            {% trans "Apply Filters" %}
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                            <i class="bi bi-x-circle"></i>
                            {% trans "Clear Filters" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Customers Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {% trans "Customer List" %}
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i>
                        {% trans "Refresh" %}
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                        <i class="bi bi-download"></i>
                        {% trans "Export" %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="customers-table">
                        <thead>
                            <tr>
                                <th>{% trans "Customer Code" %}</th>
                                <th>{% trans "Name" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Phone" %}</th>
                                <th>{% trans "Email" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Registration Date" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be populated by DataTables -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let customersTable;

// Ensure jQuery is loaded before running
function initializeCustomerPage() {
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded. Retrying in 100ms...');
        setTimeout(initializeCustomerPage, 100);
        return;
    }

    console.log('Initializing customer page...');

    // Initialize page
    loadLocations();
    initializeCustomersTable();

    // Event handlers
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        customersTable.ajax.reload();
    });

    $('#clear-filters').on('click', function() {
        $('#filter-form')[0].reset();
        customersTable.ajax.reload();
    });

    $('#refresh-btn').on('click', function() {
        customersTable.ajax.reload();
    });

    $('#export-btn').on('click', function() {
        exportCustomers();
    });

    // Autocomplete search functionality
    let searchTimeout;
    $('#search-input').on('input', function() {
        const query = $(this).val().trim();

        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        if (query.length < 2) {
            $('#search-suggestions').hide();
            return;
        }

        // Debounce search requests
        searchTimeout = setTimeout(() => {
            searchCustomers(query);
        }, 300);
    });

    // Handle suggestion clicks
    $(document).on('click', '.search-suggestion', function() {
        const customerId = $(this).data('customer-id');
        const customerName = $(this).text();

        $('#search-input').val(customerName);
        $('#search-suggestions').hide();

        // Filter table to show only this customer
        customersTable.search(customerName).draw();
    });

    // Hide suggestions when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#search-input, #search-suggestions').length) {
            $('#search-suggestions').hide();
        }
    });
}

// Try to initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeCustomerPage);
} else {
    initializeCustomerPage();
}

// Also try with jQuery when available
if (typeof $ !== 'undefined') {
    $(document).ready(initializeCustomerPage);
}

function searchCustomers(query) {
    $.ajax({
        url: '/api/v1/customers/customers/',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
        },
        data: {
            search: query,
            limit: 10
        },
        success: function(data) {
            displaySearchSuggestions(data.results || []);
        },
        error: function(xhr) {
            console.error('Error searching customers:', xhr);
            $('#search-suggestions').hide();
        }
    });
}

function displaySearchSuggestions(customers) {
    const suggestionsContainer = $('#search-suggestions');
    suggestionsContainer.empty();

    if (customers.length === 0) {
        suggestionsContainer.append(
            '<div class="p-2 text-muted">{% trans "No customers found" %}</div>'
        );
    } else {
        customers.forEach(function(customer) {
            const displayName = customer.display_name ||
                              (customer.first_name && customer.last_name ?
                               `${customer.first_name} ${customer.last_name}` :
                               customer.company_name || customer.customer_code);

            const suggestion = `
                <div class="search-suggestion p-2 border-bottom cursor-pointer"
                     data-customer-id="${customer.id}"
                     style="cursor: pointer;">
                    <div class="fw-bold">${displayName}</div>
                    <small class="text-muted">${customer.customer_code}</small>
                    ${customer.phone_number ? `<small class="text-muted ms-2">${customer.phone_number}</small>` : ''}
                </div>
            `;
            suggestionsContainer.append(suggestion);
        });
    }

    // Add hover effects
    suggestionsContainer.find('.search-suggestion').hover(
        function() { $(this).addClass('bg-light'); },
        function() { $(this).removeClass('bg-light'); }
    );

    suggestionsContainer.show();
}

function loadLocations() {
    $.ajax({
        url: '/api/v1/locations/locations/',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
        },
        success: function(data) {
            const select = $('#location-filter');
            select.find('option:not(:first)').remove();

            if (data.results) {
                data.results.forEach(function(location) {
                    select.append(`<option value="${location.id}">${location.name}</option>`);
                });
            }
        }
    });
}

function initializeCustomersTable() {
    // Check if DataTable already exists and destroy it
    if ($.fn.DataTable.isDataTable('#customers-table')) {
        $('#customers-table').DataTable().destroy();
        console.log('Existing customers DataTable destroyed');
    }

    // Debug authentication
    const accessToken = ArenaDoviz.auth.getAccessToken();
    console.log('JWT Access Token for customers:', accessToken ? 'Present' : 'Missing');

    if (!accessToken) {
        console.error('No JWT access token found for customers. User may need to log in again.');
        if (typeof showAlert === 'function') {
            showAlert('error', 'Authentication required. Please refresh the page and log in again.');
        }
        return;
    }

    const columns = [
        { data: 'customer_code', name: 'customer_code' },
        { 
            data: 'display_name', 
            name: 'display_name',
            render: function(data, type, row) {
                return `<a href="/customers/${row.id}/" class="text-decoration-none">${data}</a>`;
            }
        },
        { 
            data: 'customer_type_display', 
            name: 'customer_type',
            render: function(data, type, row) {
                const badgeClass = row.customer_type === 'individual' ? 'bg-primary' : 'bg-info';
                return `<span class="badge ${badgeClass}">${data}</span>`;
            }
        },
        { data: 'phone_number', name: 'phone_number' },
        { data: 'email', name: 'email' },
        { 
            data: 'status_display', 
            name: 'status',
            render: function(data, type, row) {
                const statusClasses = {
                    'active': 'bg-success',
                    'inactive': 'bg-secondary',
                    'suspended': 'bg-danger'
                };
                const badgeClass = statusClasses[row.status] || 'bg-secondary';
                return `<span class="badge ${badgeClass}">${data}</span>`;
            }
        },
        { 
            data: 'registration_date', 
            name: 'registration_date',
            render: function(data, type, row) {
                return new Date(data).toLocaleDateString();
            }
        },
        {
            data: null,
            orderable: false,
            render: function(data, type, row) {
                return `
                    <div class="btn-group" role="group">
                        <a href="/customers/${row.id}/" class="btn btn-sm btn-outline-primary" title="{% trans 'View Details' %}">
                            <i class="bi bi-eye"></i>
                        </a>
                        <a href="/customers/${row.id}/edit/" class="btn btn-sm btn-outline-secondary" title="{% trans 'Edit' %}">
                            <i class="bi bi-pencil"></i>
                        </a>
                        <button class="btn btn-sm btn-outline-info" onclick="viewBalance('${row.id}')" title="{% trans 'View Balance' %}">
                            <i class="bi bi-wallet"></i>
                        </button>
                    </div>
                `;
            }
        }
    ];

    customersTable = ArenaDoviz.tables.initServerSideTable(
        '#customers-table',
        '/api/v1/customers/customers/',
        columns,
        {
            order: [[6, 'desc']], // Order by registration date
            ajax: {
                url: '/api/v1/customers/customers/',
                type: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
                },
                data: function(d) {
                    // Add filter parameters
                    const formData = new FormData($('#filter-form')[0]);
                    const filters = {};

                    for (let [key, value] of formData.entries()) {
                        if (value) {
                            filters[key] = value;
                        }
                    }

                    return Object.assign({
                        page: Math.floor(d.start / d.length) + 1,
                        page_size: d.length,
                        search: d.search.value,
                        ordering: d.order.length > 0 ?
                            (d.order[0].dir === 'desc' ? '-' : '') + columns[d.order[0].column].name :
                            '-registration_date'
                    }, filters);
                },
                dataSrc: function(json) {
                    console.log('Customer API response:', json);
                    console.log('Customer count:', json.count);
                    console.log('Customer results:', json.results);

                    json.recordsTotal = json.count;
                    json.recordsFiltered = json.count;
                    return json.results;
                },
                error: function(xhr, error, code) {
                    console.error('Customer DataTables AJAX error:', error, code, xhr.responseText);
                    console.error('Request details:', {
                        url: '/api/v1/customers/customers/',
                        headers: {
                            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
                        },
                        status: xhr.status,
                        statusText: xhr.statusText
                    });

                    let errorMessage = 'Failed to load customers.';
                    if (xhr.status === 401) {
                        errorMessage += ' Authentication required.';
                    } else if (xhr.status === 403) {
                        errorMessage += ' Access denied.';
                    } else if (xhr.status === 404) {
                        errorMessage += ' API endpoint not found.';
                    } else if (xhr.status >= 500) {
                        errorMessage += ' Server error.';
                    }

                    // Show error message (assuming showAlert function exists)
                    if (typeof showAlert === 'function') {
                        showAlert('error', errorMessage);
                    } else {
                        console.error(errorMessage);
                    }
                }
            }
        }
    );
}

function viewBalance(customerId) {
    // Open balance modal or navigate to balance page
    window.location.href = `/customers/${customerId}/balance/`;
}

function getAuthToken() {
    return localStorage.getItem('arena_access_token') || '';
}

function exportCustomers() {
    // Check authentication
    const accessToken = ArenaDoviz.auth.getAccessToken();
    if (!accessToken) {
        if (typeof showAlert === 'function') {
            showAlert('error', 'Authentication required. Please refresh the page and log in again.');
        } else {
            alert('Authentication required. Please refresh the page and log in again.');
        }
        return;
    }

    // Get filter parameters
    const formData = new FormData($('#filter-form')[0]);
    const params = new URLSearchParams();

    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }

    params.append('format', 'csv'); // Use CSV format since it's implemented

    // Use fetch with authentication headers
    fetch('/api/v1/customers/customers/export/?' + params.toString(), {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + accessToken
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Export failed: ${response.status} ${response.statusText}`);
        }
        return response.blob();
    })
    .then(blob => {
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `customers_${new Date().toISOString().slice(0, 10)}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        if (typeof showAlert === 'function') {
            showAlert('success', 'Customers exported successfully!');
        } else {
            console.log('Customers exported successfully!');
        }
    })
    .catch(error => {
        console.error('Export error:', error);
        if (typeof showAlert === 'function') {
            showAlert('error', 'Failed to export customers: ' + error.message);
        } else {
            alert('Failed to export customers: ' + error.message);
        }
    });
}
</script>
{% endblock %}
