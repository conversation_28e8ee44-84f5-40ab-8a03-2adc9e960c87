# Arena Doviz Automated Testing and Debugging System

## 🎯 Overview

This comprehensive automated testing system provides end-to-end testing, debugging, and validation for the Arena Doviz transaction system. It includes browser automation, financial verification, issue detection, and comprehensive reporting.

## 🚀 Features

### 1. Browser Automation Testing
- **Playwright-based automation** for all transaction forms
- **Screenshot capture** at each step for visual verification
- **Console log monitoring** to detect JavaScript errors
- **Network request/response logging** for API interactions
- **Form validation testing** with various input combinations

### 2. System Integration Monitoring
- **Real-time system metrics** (CPU, memory, disk usage)
- **Django application log monitoring**
- **API request/response tracking**
- **Database query performance monitoring**
- **Performance threshold alerting**

### 3. Comprehensive Test Scenarios
- **All transaction types**: Exchange, Deposit, Withdrawal, Transfers, Remittance, Adjustments
- **Error scenario testing**: Invalid data, missing fields, insufficient balance
- **Edge case testing**: Zero amounts, maximum values, special characters
- **Complete workflow testing**: Customer creation → Balance setup → Transactions

### 4. Financial Accuracy Verification
- **Automated balance verification** after each transaction
- **Double-entry bookkeeping validation**
- **Commission calculation verification**
- **Exchange rate application checking**
- **Account statement accuracy validation**

### 5. Database Seeding and Test Data Management
- **Comprehensive test customer creation** (individuals and businesses)
- **Exchange rate setup** for all currency pairs and locations
- **Commission rule configuration**
- **Initial balance setup** with realistic amounts
- **Automated cleanup** after test completion

### 6. Automated Issue Detection and Debugging
- **Pattern-based error detection** from logs and console output
- **Issue categorization** by severity and component
- **Automated fix suggestions** based on error patterns
- **Q&A documentation generation** from detected issues
- **Root cause analysis** with reproduction steps

### 7. Production Environment Testing
- **Production deployment verification**
- **Service health monitoring**
- **End-to-end workflow validation**
- **Performance benchmarking**
- **Security and accessibility checks**

### 8. Comprehensive Reporting
- **Detailed test execution logs** with timestamps
- **Visual test result dashboards**
- **Issue prioritization and tracking**
- **Performance metrics and trends**
- **Actionable recommendations**

## 📋 Prerequisites

- **Python 3.8+**
- **Windows 10/11** (for batch scripts)
- **Internet connection** for Playwright browser downloads
- **Access to Arena Doviz system** (production or local)

## 🛠️ Installation

### 1. Quick Setup (Recommended)

```bash
# Clone or download the automation files to your project
cd src/tests/automation

# Run the automated setup
python setup.py
```

### 2. Manual Setup

```bash
# Create virtual environment
python -m venv venv
venv\Scripts\activate

# Install requirements
pip install -r requirements.txt

# Install Playwright browsers
playwright install

# Create directories
mkdir screenshots logs reports test_data
```

### 3. Configuration

Update the `.env` file with your credentials:

```env
TEST_ENVIRONMENT=production
BROWSER_TYPE=chromium
HEADLESS=false
SLOW_MO=500
TEST_USERNAME=your_username
TEST_PASSWORD=your_password
LOG_LEVEL=INFO
```

## 🎮 Usage

### Windows Batch Script (Easiest)

```bash
# Run full test suite
run_tests.bat

# Run specific test types
run_tests.bat --test-type production
run_tests.bat --test-type scenarios
run_tests.bat --test-type financial

# Run in headless mode
run_tests.bat --headless

# Test local environment
run_tests.bat --environment local
```

### Python Direct Execution

```bash
# Activate virtual environment
venv\Scripts\activate

# Run full test suite
python main_test_runner.py

# Run specific test types
python main_test_runner.py --test-type production
python main_test_runner.py --test-type scenarios --headless
python main_test_runner.py --test-type financial --environment local
```

## 📊 Test Types

### 1. Full Test Suite (`--test-type full`)
- Complete production environment testing
- All transaction scenario execution
- Financial accuracy verification
- Issue detection and analysis
- Comprehensive reporting

### 2. Production Testing (`--test-type production`)
- Environment accessibility verification
- Service health checks
- Deployment validation
- Critical workflow testing
- Performance monitoring

### 3. Scenario Testing (`--test-type scenarios`)
- All transaction form testing
- Error handling validation
- Edge case verification
- User workflow simulation

### 4. Financial Testing (`--test-type financial`)
- Transaction accuracy verification
- Balance calculation validation
- Commission verification
- Exchange rate checking

## 📁 Output Files

### Reports Directory
- `arena_doviz_test_report_YYYYMMDD_HHMMSS.json` - Comprehensive test report
- `production_test_report_YYYYMMDD_HHMMSS.json` - Production-specific results
- `issues_report_YYYYMMDD_HHMMSS.json` - Detected issues and fixes
- `qa_documentation_YYYYMMDD_HHMMSS.json` - Q&A documentation

### Screenshots Directory
- `YYYYMMDD_HHMMSS_test_name_status.png` - Test execution screenshots
- `before_submit_action.png` - Pre-submission states
- `after_submit_action.png` - Post-submission results

### Logs Directory
- `django.log` - Django application logs
- `api.log` - API request/response logs
- `database.log` - Database query logs
- `videos/` - Browser session recordings (production only)

## 🔍 Understanding Results

### Test Status Codes
- **PASS** ✅ - Test completed successfully
- **FAIL** ❌ - Test failed with validation errors
- **ERROR** 💥 - Test encountered unexpected errors

### Issue Severity Levels
- **CRITICAL** 🔴 - System-breaking issues requiring immediate attention
- **HIGH** 🟠 - Important issues affecting functionality
- **MEDIUM** 🟡 - Moderate issues with workarounds available
- **LOW** 🟢 - Minor issues or improvements

### Performance Metrics
- **Response Time** - API and page load performance
- **Success Rate** - Percentage of successful test scenarios
- **Error Rate** - Frequency of errors and failures
- **Resource Usage** - CPU, memory, and disk utilization

## 🛠️ Troubleshooting

### Common Issues

#### 1. "Python not found"
```bash
# Install Python 3.8+ from python.org
# Ensure Python is added to PATH during installation
```

#### 2. "Playwright browsers not installed"
```bash
venv\Scripts\activate
playwright install
```

#### 3. "Authentication failed"
```bash
# Update .env file with correct credentials
# Ensure user has appropriate permissions
```

#### 4. "Connection refused"
```bash
# Verify Arena Doviz system is running
# Check firewall and network connectivity
# Confirm correct URL in configuration
```

### Debug Mode

Enable detailed logging:
```bash
# Set LOG_LEVEL=DEBUG in .env file
python main_test_runner.py --test-type scenarios
```

### Browser Debug Mode

Run with visible browser:
```bash
# Set HEADLESS=false in .env file
python main_test_runner.py --test-type scenarios
```

## 📈 Continuous Integration

### GitHub Actions Integration

```yaml
name: Arena Doviz Automated Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: |
          cd src/tests/automation
          python setup.py
      - name: Run tests
        run: |
          cd src/tests/automation
          python main_test_runner.py --test-type scenarios --headless
```

### Jenkins Integration

```groovy
pipeline {
    agent any
    stages {
        stage('Setup') {
            steps {
                bat 'cd src/tests/automation && python setup.py'
            }
        }
        stage('Test') {
            steps {
                bat 'cd src/tests/automation && python main_test_runner.py --headless'
            }
        }
        stage('Report') {
            steps {
                archiveArtifacts artifacts: 'src/tests/automation/reports/*'
            }
        }
    }
}
```

## 🤝 Contributing

1. **Add new test scenarios** in `test_scenarios.py`
2. **Extend issue detection patterns** in `issue_detector.py`
3. **Add financial verification rules** in `financial_verification.py`
4. **Improve reporting formats** in `main_test_runner.py`

## 📞 Support

For issues and questions:
1. Check the generated Q&A documentation
2. Review the troubleshooting guide above
3. Examine detailed logs in the `logs/` directory
4. Check issue reports for similar problems

## 🔄 Updates

The system automatically:
- **Captures new error patterns** and adds them to detection rules
- **Generates updated Q&A documentation** based on encountered issues
- **Provides specific fix suggestions** for detected problems
- **Tracks performance trends** over time

This ensures the testing system continuously improves and adapts to new scenarios and issues.
