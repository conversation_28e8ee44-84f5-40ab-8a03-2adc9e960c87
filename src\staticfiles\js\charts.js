/**
 * Arena Doviz Chart Utilities
 * Enhanced Chart.js integration with real-time data
 */

const ArenaDovizCharts = {
    // Chart instances storage
    instances: {},
    
    // Default chart colors
    colors: {
        primary: 'rgb(54, 162, 235)',
        success: 'rgb(40, 167, 69)',
        warning: 'rgb(255, 193, 7)',
        danger: 'rgb(220, 53, 69)',
        info: 'rgb(23, 162, 184)',
        secondary: 'rgb(108, 117, 125)',
        light: 'rgb(248, 249, 250)',
        dark: 'rgb(52, 58, 64)'
    },
    
    // Chart color palettes
    palettes: {
        default: [
            'rgb(255, 99, 132)',
            'rgb(54, 162, 235)',
            'rgb(255, 205, 86)',
            'rgb(75, 192, 192)',
            'rgb(153, 102, 255)',
            'rgb(255, 159, 64)',
            'rgb(199, 199, 199)',
            'rgb(83, 102, 255)'
        ],
        currency: [
            'rgb(255, 99, 132)',   // USD - Red
            'rgb(54, 162, 235)',   // AED - Blue
            'rgb(255, 205, 86)',   // IRR - Yellow
            'rgb(75, 192, 192)',   // EUR - Teal
            'rgb(153, 102, 255)',  // GBP - Purple
            'rgb(255, 159, 64)'    // Others - Orange
        ],
        status: {
            completed: 'rgb(40, 167, 69)',
            pending: 'rgb(255, 193, 7)',
            approved: 'rgb(0, 123, 255)',
            draft: 'rgb(108, 117, 125)',
            cancelled: 'rgb(220, 53, 69)',
            rejected: 'rgb(220, 53, 69)'
        }
    },
    
    /**
     * Initialize all charts with data
     */
    init: function(data) {
        this.destroyAll();
        
        if (data.transaction_volume) {
            this.createTransactionVolumeChart(data.transaction_volume);
        }
        
        if (data.currency_distribution) {
            this.createCurrencyDistributionChart(data.currency_distribution);
        }
        
        if (data.profit_analysis) {
            this.createProfitAnalysisChart(data.profit_analysis);
        }
        
        if (data.status_distribution) {
            this.createStatusDistributionChart(data.status_distribution);
        }
        
        if (data.location_performance) {
            this.createLocationPerformanceChart(data.location_performance);
        }
        
        if (data.balance_trends) {
            this.createBalanceTrendsChart(data.balance_trends);
        }
    },
    
    /**
     * Destroy all chart instances
     */
    destroyAll: function() {
        // First destroy all Chart.js instances
        Object.values(this.instances).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                try {
                    chart.destroy();
                } catch (e) {
                    console.warn('Error destroying chart:', e);
                }
            }
        });
        this.instances = {};

        // Force clear all canvas elements to prevent height issues
        const canvases = ['transactionVolumeChart', 'profitAnalysisChart', 'balanceTrendsChart', 'currencyDistributionChart'];
        canvases.forEach(canvasId => {
            const canvas = document.getElementById(canvasId);
            if (canvas) {
                try {
                    // Get parent container
                    const parent = canvas.parentNode;
                    const canvasClasses = canvas.className;

                    // Force destroy any remaining Chart.js instance
                    if (canvas.chart) {
                        canvas.chart.destroy();
                        delete canvas.chart;
                    }

                    // Clear the canvas context completely
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        // Reset canvas size to default
                        canvas.width = 0;
                        canvas.height = 0;
                    }

                    // Remove all inline styles that might affect height
                    canvas.removeAttribute('style');
                    canvas.style.cssText = '';

                    // Remove the canvas completely
                    canvas.remove();

                    // Create a completely new canvas element
                    const newCanvas = document.createElement('canvas');
                    newCanvas.id = canvasId;
                    newCanvas.className = canvasClasses;

                    // Set fixed dimensions to prevent growing
                    newCanvas.style.maxHeight = '400px';
                    newCanvas.style.height = '400px';
                    newCanvas.style.width = '100%';
                    newCanvas.style.display = 'block';

                    // Append the new canvas
                    parent.appendChild(newCanvas);

                    console.log(`Recreated canvas: ${canvasId}`);
                } catch (e) {
                    console.error(`Error recreating canvas ${canvasId}:`, e);
                }
            }
        });

        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
    },
    
    /**
     * Create transaction volume chart
     */
    createTransactionVolumeChart: function(data) {
        const ctx = document.getElementById('transactionChart');
        if (!ctx) return;
        
        this.instances.transaction = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels.map(date => this.formatDate(date)),
                datasets: data.datasets.map(dataset => ({
                    ...dataset,
                    backgroundColor: dataset.backgroundColor || this.colors.primary + '80',
                    borderColor: dataset.borderColor || this.colors.primary
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Transaction Count'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Volume (USD)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => this.formatTooltipLabel(context)
                        }
                    }
                }
            }
        });
    },
    
    /**
     * Create currency distribution chart
     */
    createCurrencyDistributionChart: function(data) {
        const ctx = document.getElementById('currencyChart');
        if (!ctx) return;
        
        this.instances.currency = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    ...data.datasets[0],
                    backgroundColor: this.palettes.currency.slice(0, data.labels.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: $${context.parsed.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    },
    
    /**
     * Create profit analysis chart
     */
    createProfitAnalysisChart: function(data) {
        const ctx = document.getElementById('profitChart');
        if (!ctx) return;
        
        this.instances.profit = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels.map(date => this.formatDate(date)),
                datasets: data.datasets.map(dataset => ({
                    ...dataset,
                    borderColor: dataset.borderColor || this.colors.success,
                    backgroundColor: dataset.backgroundColor || this.colors.success + '20',
                    tension: 0.4,
                    fill: true
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Commission ($)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: (context) => `${context.dataset.label}: $${context.parsed.y.toLocaleString()}`
                        }
                    }
                }
            }
        });
    },
    
    /**
     * Create status distribution chart
     */
    createStatusDistributionChart: function(data) {
        const ctx = document.getElementById('statusChart');
        if (!ctx) return;
        
        this.instances.status = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: data.labels,
                datasets: [{
                    ...data.datasets[0],
                    backgroundColor: data.datasets[0].backgroundColor || this.palettes.default,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    },
    
    /**
     * Create location performance chart
     */
    createLocationPerformanceChart: function(data) {
        const ctx = document.getElementById('locationChart');
        if (!ctx) return;
        
        this.instances.location = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: data.datasets.map((dataset, index) => ({
                    ...dataset,
                    backgroundColor: index === 0 ? this.colors.primary + '80' : this.colors.success + '80',
                    borderColor: index === 0 ? this.colors.primary : this.colors.success
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Transaction Volume'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Commission ($)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: (context) => this.formatTooltipLabel(context)
                        }
                    }
                }
            }
        });
    },
    
    /**
     * Create balance trends chart
     */
    createBalanceTrendsChart: function(data) {
        const ctx = document.getElementById('balanceChart');
        if (!ctx) return;
        
        this.instances.balance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels.map(date => this.formatDate(date)),
                datasets: data.datasets.map((dataset, index) => ({
                    ...dataset,
                    borderColor: this.palettes.default[index % this.palettes.default.length],
                    backgroundColor: this.palettes.default[index % this.palettes.default.length] + '20',
                    tension: 0.4,
                    fill: false
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: 'Balance'
                        },
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: (context) => `${context.dataset.label}: $${context.parsed.y.toLocaleString()}`
                        }
                    }
                }
            }
        });
    },
    
    /**
     * Format date for chart labels
     */
    formatDate: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric' 
        });
    },
    
    /**
     * Format tooltip labels
     */
    formatTooltipLabel: function(context) {
        let label = context.dataset.label || '';
        if (label) {
            label += ': ';
        }
        
        if (context.dataset.label && context.dataset.label.includes('Volume')) {
            label += '$' + context.parsed.y.toLocaleString();
        } else if (context.dataset.label && context.dataset.label.includes('Commission')) {
            label += '$' + context.parsed.y.toLocaleString();
        } else {
            label += context.parsed.y.toLocaleString();
        }
        
        return label;
    },
    
    /**
     * Update chart data
     */
    updateChart: function(chartName, newData) {
        const chart = this.instances[chartName];
        if (chart) {
            chart.data = newData;
            chart.update('active');
        }
    },
    
    /**
     * Resize all charts
     */
    resizeAll: function() {
        Object.values(this.instances).forEach(chart => {
            if (chart && typeof chart.resize === 'function') {
                chart.resize();
            }
        });
    }
};

// Auto-resize charts on window resize
window.addEventListener('resize', function() {
    ArenaDovizCharts.resizeAll();
});

// Export for global use
window.ArenaDovizCharts = ArenaDovizCharts;
