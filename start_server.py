#!/usr/bin/env python
"""
Start the Django development server
"""
import os
import sys
import subprocess

# Change to src directory
src_dir = os.path.join(os.path.dirname(__file__), 'src')
os.chdir(src_dir)

# Set environment variables
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
os.environ.setdefault('DEBUG', 'True')

# Start the server
try:
    print("Starting Arena Doviz development server...")
    print(f"Working directory: {os.getcwd()}")
    subprocess.run([sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'], check=True)
except KeyboardInterrupt:
    print("\nServer stopped by user")
except Exception as e:
    print(f"Error starting server: {e}")
