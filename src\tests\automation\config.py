"""
Arena Doviz Automated Testing Configuration
"""
import os
from pathlib import Path
from typing import Dict, Any

# Base configuration
BASE_DIR = Path(__file__).parent
PROJECT_ROOT = BASE_DIR.parent.parent
SCREENSHOTS_DIR = BASE_DIR / "screenshots"
LOGS_DIR = BASE_DIR / "logs"
REPORTS_DIR = BASE_DIR / "reports"
TEST_DATA_DIR = BASE_DIR / "test_data"

# Create directories if they don't exist
for directory in [SCREENSHOTS_DIR, LOGS_DIR, REPORTS_DIR, TEST_DATA_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# Environment configuration
class TestConfig:
    # Application URLs
    PRODUCTION_URL = "http://*************:8000"
    LOCAL_URL = "http://localhost:8000"
    
    # Test environment (can be 'production' or 'local')
    ENVIRONMENT = os.getenv('TEST_ENVIRONMENT', 'production')
    BASE_URL = PRODUCTION_URL if ENVIRONMENT == 'production' else LOCAL_URL
    
    # Browser configuration
    BROWSER_TYPE = os.getenv('BROWSER_TYPE', 'chromium')  # chromium, firefox, webkit
    HEADLESS = os.getenv('HEADLESS', 'false').lower() == 'true'
    SLOW_MO = int(os.getenv('SLOW_MO', '500'))  # Milliseconds delay between actions
    
    # Test user credentials
    TEST_USERNAME = os.getenv('TEST_USERNAME', 'admin')
    TEST_PASSWORD = os.getenv('TEST_PASSWORD', 'admin123')
    
    # Timeouts (in milliseconds)
    DEFAULT_TIMEOUT = 30000
    NAVIGATION_TIMEOUT = 60000
    API_TIMEOUT = 30000
    
    # Screenshot configuration
    SCREENSHOT_ON_FAILURE = True
    SCREENSHOT_ON_SUCCESS = False
    FULL_PAGE_SCREENSHOTS = True
    
    # Logging configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    CONSOLE_LOG_CAPTURE = True
    NETWORK_LOG_CAPTURE = True
    
    # Test data configuration
    MAX_TEST_CUSTOMERS = 10
    MAX_TEST_TRANSACTIONS = 50
    CLEANUP_AFTER_TESTS = True
    
    # Performance monitoring
    MONITOR_PERFORMANCE = True
    PERFORMANCE_THRESHOLD_MS = 5000
    
    # Financial validation
    BALANCE_PRECISION = 6  # Decimal places for balance calculations
    COMMISSION_PRECISION = 6
    EXCHANGE_RATE_PRECISION = 6

# Transaction types configuration
TRANSACTION_TYPES = {
    'EXCHANGE': {
        'name': 'Currency Exchange',
        'url_path': '/transactions/exchange/add/',
        'form_id': 'transaction-form',
        'required_fields': ['customer', 'from_currency', 'to_currency', 'from_amount', 'exchange_rate']
    },
    'DEPOSIT': {
        'name': 'Cash Deposit',
        'url_path': '/transactions/cash/deposit/add/',
        'form_id': 'transaction-form',
        'required_fields': ['customer', 'from_currency', 'from_amount']
    },
    'WITHDRAWAL': {
        'name': 'Cash Withdrawal',
        'url_path': '/transactions/cash/withdrawal/add/',
        'form_id': 'transaction-form',
        'required_fields': ['customer', 'from_currency', 'from_amount']
    },
    'INTERNAL_TRANSFER': {
        'name': 'Internal Transfer',
        'url_path': '/transactions/transfer/internal/add/',
        'form_id': 'transaction-form',
        'required_fields': ['customer', 'recipient_customer', 'from_currency', 'from_amount']
    },
    'EXTERNAL_TRANSFER': {
        'name': 'External Transfer',
        'url_path': '/transactions/transfer/external/add/',
        'form_id': 'transaction-form',
        'required_fields': ['customer', 'from_currency', 'from_amount', 'recipient_details']
    },
    'INTERNATIONAL_TRANSFER': {
        'name': 'International Transfer',
        'url_path': '/transactions/transfer/international/add/',
        'form_id': 'transaction-form',
        'required_fields': ['customer', 'from_currency', 'to_currency', 'from_amount', 'recipient_details']
    },
    'REMITTANCE': {
        'name': 'Remittance',
        'url_path': '/transactions/remittance/add/',
        'form_id': 'transaction-form',
        'required_fields': ['customer', 'from_currency', 'to_currency', 'from_amount', 'recipient_details']
    },
    'ADJUSTMENT': {
        'name': 'Balance Adjustment',
        'url_path': '/transactions/adjustment/add/',
        'form_id': 'transaction-form',
        'required_fields': ['customer', 'from_currency', 'from_amount', 'adjustment_type', 'reason']
    }
}

# Test data templates
TEST_DATA_TEMPLATES = {
    'customers': [
        {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'phone_number': '+1234567890',
            'customer_type': 'individual'
        },
        {
            'first_name': 'Jane',
            'last_name': 'Smith',
            'email': '<EMAIL>',
            'phone_number': '+1234567891',
            'customer_type': 'individual'
        },
        {
            'company_name': 'Test Corp',
            'email': '<EMAIL>',
            'phone_number': '+1234567892',
            'customer_type': 'business'
        }
    ],
    'currencies': ['USD', 'AED', 'IRR'],
    'locations': ['IST', 'DXB', 'TAB', 'TEH', 'CHN'],
    'amounts': [100.00, 500.00, 1000.00, 2500.00, 5000.00]
}

# Error patterns to detect in logs
ERROR_PATTERNS = [
    r'ERROR',
    r'CRITICAL',
    r'Exception',
    r'Traceback',
    r'500 Internal Server Error',
    r'404 Not Found',
    r'ValidationError',
    r'IntegrityError',
    r'DatabaseError',
    r'ConnectionError'
]

# JavaScript error patterns
JS_ERROR_PATTERNS = [
    r'Uncaught',
    r'TypeError',
    r'ReferenceError',
    r'SyntaxError',
    r'RangeError',
    r'EvalError',
    r'URIError',
    r'console\.error',
    r'Failed to fetch',
    r'Network Error'
]
