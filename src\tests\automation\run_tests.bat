@echo off
REM Arena Doviz Automated Testing Batch Script for Windows

echo ========================================
echo Arena Doviz Automated Testing System
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Run setup if first time
if not exist ".env" (
    echo Running first-time setup...
    python setup.py
    if errorlevel 1 (
        echo ERROR: Setup failed
        pause
        exit /b 1
    )
)

REM Parse command line arguments
set TEST_TYPE=full
set ENVIRONMENT=production
set HEADLESS=false

:parse_args
if "%1"=="" goto run_tests
if "%1"=="--test-type" (
    set TEST_TYPE=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--environment" (
    set ENVIRONMENT=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--headless" (
    set HEADLESS=true
    shift
    goto parse_args
)
if "%1"=="--help" (
    echo Usage: run_tests.bat [options]
    echo Options:
    echo   --test-type [full^|production^|scenarios^|financial]  Type of tests to run
    echo   --environment [production^|local]                     Environment to test
    echo   --headless                                           Run in headless mode
    echo   --help                                               Show this help
    pause
    exit /b 0
)
shift
goto parse_args

:run_tests
echo ========================================
echo Running tests with configuration:
echo Test Type: %TEST_TYPE%
echo Environment: %ENVIRONMENT%
echo Headless: %HEADLESS%
echo ========================================

REM Run the tests
if "%HEADLESS%"=="true" (
    python main_test_runner.py --test-type %TEST_TYPE% --environment %ENVIRONMENT% --headless
) else (
    python main_test_runner.py --test-type %TEST_TYPE% --environment %ENVIRONMENT%
)

if errorlevel 1 (
    echo ========================================
    echo ERROR: Test execution failed
    echo Check the logs and reports for details
    echo ========================================
) else (
    echo ========================================
    echo SUCCESS: Test execution completed
    echo Check the reports directory for results
    echo ========================================
)

REM Keep window open to see results
pause
