/**
 * Remittance Transaction Form Handler
 */

class RemittanceTransactionForm {
    constructor() {
        this.form = document.getElementById('transaction-form');
        this.transactionTypeCode = 'REMITTANCE';
        this.init();
    }

    init() {
        this.loadFormData();
        this.bindEvents();
        this.setupValidation();
    }

    loadFormData() {
        this.loadTransactionTypes();
        TransactionUtils.loadCustomers();
        TransactionUtils.loadLocations();
        TransactionUtils.loadCurrencies();
    }

    bindEvents() {
        $('#customer').on('change', () => {
            TransactionUtils.loadCustomerBalance();
        });

        $('#from_currency, #to_currency').on('change', () => {
            this.loadCurrentRates();
        });

        $('#from_amount, #exchange_rate').on('input', () => {
            this.calculateToAmount();
            this.updateTransactionPreview();
        });

        $('#get-current-rate').on('click', () => {
            this.getCurrentRate();
        });

        $('#delivery_method').on('change', () => {
            this.toggleDeliveryFields();
        });

        $('#remittance_purpose').on('change', () => {
            this.togglePurposeDetails();
        });

        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            const action = e.submitter?.dataset?.action || 'save';
            this.submitTransaction(action);
        });

        $('#document_files').on('change', () => {
            TransactionUtils.handleFileSelection();
        });

        $('#transaction-form input, #transaction-form select, #transaction-form textarea').on('change input', () => {
            this.updateTransactionPreview();
        });
    }

    setupValidation() {
        this.form.addEventListener('submit', (e) => {
            if (!this.validateRemittanceForm()) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    }

    validateRemittanceForm() {
        let isValid = true;
        const errors = [];

        const amount = parseFloat($('#from_amount').val());
        if (amount <= 0) {
            errors.push('Remittance amount must be greater than zero');
            isValid = false;
        }

        if (!$('#beneficiary_name').val()) {
            errors.push('Beneficiary name is required');
            isValid = false;
        }

        if (!$('#beneficiary_address').val()) {
            errors.push('Beneficiary address is required');
            isValid = false;
        }

        if (!$('#destination_country').val()) {
            errors.push('Destination country is required');
            isValid = false;
        }

        if (!$('#destination_city').val()) {
            errors.push('Destination city is required');
            isValid = false;
        }

        if (!$('#delivery_method').val()) {
            errors.push('Delivery method is required');
            isValid = false;
        }

        if (!$('#remittance_purpose').val()) {
            errors.push('Purpose of remittance is required');
            isValid = false;
        }

        const deliveryMethod = $('#delivery_method').val();
        if (deliveryMethod === 'bank_transfer' && !$('#bank_details').val()) {
            errors.push('Bank details are required for bank transfers');
            isValid = false;
        }

        if (deliveryMethod === 'mobile_wallet' && !$('#mobile_wallet').val()) {
            errors.push('Mobile wallet number is required');
            isValid = false;
        }

        if (!isValid) {
            this.showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    loadTransactionTypes() {
        $.ajax({
            url: '/api/v1/transactions/types/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const remittanceType = data.results.find(type => type.code === 'REMITTANCE');
                if (remittanceType) {
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'transaction_type',
                        name: 'transaction_type',
                        value: remittanceType.id
                    }).appendTo(this.form);
                }
            }
        });
    }

    loadCustomers() {
        $.ajax({
            url: '/api/v1/customers/customers/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const customerSelect = $('#customer');
                customerSelect.empty().append('<option value="">Select customer...</option>');
                data.results.forEach(customer => {
                    customerSelect.append(`<option value="${customer.id}">${customer.first_name} ${customer.last_name}</option>`);
                });
            }
        });
    }

    loadLocations() {
        $.ajax({
            url: '/api/v1/locations/locations/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const locationSelect = $('#location');
                locationSelect.empty().append('<option value="">Select location...</option>');
                data.results.forEach(location => {
                    locationSelect.append(`<option value="${location.id}">${location.name}</option>`);
                });
            }
        });
    }

    loadCurrencies() {
        $.ajax({
            url: '/api/v1/currencies/currencies/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const fromCurrencySelect = $('#from_currency');
                const toCurrencySelect = $('#to_currency');
                
                fromCurrencySelect.empty().append('<option value="">Select currency...</option>');
                toCurrencySelect.empty().append('<option value="">Select currency...</option>');
                
                data.results.forEach(currency => {
                    const option = `<option value="${currency.id}">${currency.code} - ${currency.name}</option>`;
                    fromCurrencySelect.append(option);
                    toCurrencySelect.append(option);
                });
            }
        });
    }

    loadCustomerBalance() {
        const customerId = $('#customer').val();
        if (!customerId) {
            $('#customer-balance').html('<div class="text-muted text-center py-3"><i class="bi bi-person"></i> Select customer to view balance</div>');
            return;
        }

        $.ajax({
            url: `/api/v1/transactions/api/customer/${customerId}/balance/`,
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                let balanceHtml = '<div class="list-group list-group-flush">';
                
                if (data.balances && data.balances.length > 0) {
                    data.balances.forEach(balance => {
                        const balanceClass = balance.amount >= 0 ? 'text-success' : 'text-danger';
                        balanceHtml += `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span>${balance.currency_code}</span>
                                <span class="${balanceClass}">${balance.formatted_amount}</span>
                            </div>
                        `;
                    });
                } else {
                    balanceHtml += '<div class="list-group-item text-muted text-center">No balance records found</div>';
                }
                
                balanceHtml += '</div>';
                $('#customer-balance').html(balanceHtml);
            }
        });
    }

    loadCurrentRates() {
        const fromCurrency = $('#from_currency').val();
        const toCurrency = $('#to_currency').val();
        const location = $('#location').val();

        if (!fromCurrency || !toCurrency || !location) {
            return;
        }

        // Get currency codes for API call
        const fromCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
        const toCurrencyCode = $('#to_currency option:selected').text().split(' - ')[0];
        const locationCode = $('#location option:selected').data('code') || location;

        TransactionUtils.getExchangeRate(fromCurrencyCode, toCurrencyCode, locationCode, 'sell')
            .then((data) => {
                if (data && data.length > 0) {
                    // Find the matching rate
                    const rate = data.find(r => r.from_currency === fromCurrencyCode && r.to_currency === toCurrencyCode);
                    if (rate) {
                        this.currentRate = rate.sell_rate;
                        $('#exchange_rate').val(this.currentRate);
                        this.calculateToAmount();
                    }
                }
            })
            .catch((error) => {
                console.error('Error loading exchange rates:', error);
                TransactionUtils.showAlert('warning', 'Unable to load current exchange rates');
            });
    }

    getCurrentRate() {
        if (this.currentRate) {
            $('#exchange_rate').val(this.currentRate);
            this.calculateToAmount();
            this.updateTransactionPreview();
        } else {
            this.loadCurrentRates();
        }
    }

    calculateToAmount() {
        const fromAmount = parseFloat($('#from_amount').val()) || 0;
        const exchangeRate = parseFloat($('#exchange_rate').val()) || 0;

        if (fromAmount > 0 && exchangeRate > 0) {
            const toAmount = fromAmount * exchangeRate;
            $('#to_amount').val(toAmount.toFixed(6));
        } else {
            $('#to_amount').val('');
        }
    }

    toggleDeliveryFields() {
        const deliveryMethod = $('#delivery_method').val();
        
        $('#bank-details, #mobile-wallet-details').hide();
        
        switch (deliveryMethod) {
            case 'bank_transfer':
                $('#bank-details').show();
                break;
            case 'mobile_wallet':
                $('#mobile-wallet-details').show();
                break;
        }
    }

    togglePurposeDetails() {
        const purpose = $('#remittance_purpose').val();
        if (purpose === 'other') {
            $('#purpose-details').show();
        } else {
            $('#purpose-details').hide();
        }
    }

    updateTransactionPreview() {
        const formData = new FormData(this.form);
        const preview = {
            customer: $('#customer option:selected').text(),
            beneficiary: formData.get('beneficiary_name'),
            fromCurrency: $('#from_currency option:selected').text(),
            toCurrency: $('#to_currency option:selected').text(),
            fromAmount: formData.get('from_amount'),
            toAmount: formData.get('to_amount'),
            commission: formData.get('commission_amount'),
            destination: `${formData.get('destination_city')}, ${$('#destination_country option:selected').text()}`,
            deliveryMethod: $('#delivery_method option:selected').text(),
            purpose: $('#remittance_purpose option:selected').text()
        };

        let previewHtml = '<div class="list-group list-group-flush">';
        
        if (preview.customer && preview.customer !== 'Select customer...') {
            previewHtml += `<div class="list-group-item"><strong>Sender:</strong> ${preview.customer}</div>`;
        }
        
        if (preview.beneficiary) {
            previewHtml += `<div class="list-group-item"><strong>Beneficiary:</strong> ${preview.beneficiary}</div>`;
        }
        
        if (preview.fromAmount && preview.fromCurrency && preview.fromCurrency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Send:</strong> ${preview.fromAmount} ${preview.fromCurrency.split(' - ')[0]}</div>`;
        }
        
        if (preview.toAmount && preview.toCurrency && preview.toCurrency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Receive:</strong> ${preview.toAmount} ${preview.toCurrency.split(' - ')[0]}</div>`;
        }
        
        if (preview.commission) {
            previewHtml += `<div class="list-group-item"><strong>Service Fee:</strong> ${preview.commission}</div>`;
        }
        
        if (preview.destination && preview.destination !== ', Select country...') {
            previewHtml += `<div class="list-group-item"><strong>Destination:</strong> ${preview.destination}</div>`;
        }
        
        if (preview.deliveryMethod && preview.deliveryMethod !== 'Select delivery method...') {
            previewHtml += `<div class="list-group-item"><strong>Delivery:</strong> ${preview.deliveryMethod}</div>`;
        }
        
        if (preview.purpose && preview.purpose !== 'Select purpose...') {
            previewHtml += `<div class="list-group-item"><strong>Purpose:</strong> ${preview.purpose}</div>`;
        }
        
        previewHtml += '</div>';
        
        if (previewHtml === '<div class="list-group list-group-flush"></div>') {
            previewHtml = '<div class="text-muted text-center py-4"><i class="bi bi-info-circle"></i> Fill in the form to see transaction preview</div>';
        }
        
        $('#transaction-preview').html(previewHtml);
    }

    submitTransaction(action) {
        if (!this.validateRemittanceForm()) {
            return;
        }

        const formData = new FormData(this.form);
        const data = Object.fromEntries(formData.entries());
        
        data.status = action === 'submit' ? 'pending' : 'draft';

        $.ajax({
            url: '/api/v1/transactions/transactions/',
            method: 'POST',
            headers: this.getAuthHeaders(),
            data: JSON.stringify(data),
            success: (response) => {
                const transactionId = response.id;
                const files = $('#document_files')[0].files;
                if (files.length > 0) {
                    this.uploadDocuments(transactionId, files);
                } else {
                    this.showAlert('success', 'Remittance created successfully');
                    setTimeout(() => {
                        window.location.href = '/transactions/type/REMITTANCE/';
                    }, 2000);
                }
            },
            error: (xhr) => {
                const errors = xhr.responseJSON;
                let errorMessage = 'Error creating remittance';
                if (errors) {
                    errorMessage += ':<br>';
                    for (let field in errors) {
                        errorMessage += `${field}: ${errors[field]}<br>`;
                    }
                }
                this.showAlert('danger', errorMessage);
            }
        });
    }

    uploadDocuments(transactionId, files) {
        const formData = new FormData();
        const documentType = $('#document_type').val();

        for (let i = 0; i < files.length; i++) {
            formData.append('files', files[i]);
        }
        formData.append('document_type', documentType);
        formData.append('transaction', transactionId);

        $.ajax({
            url: '/api/v1/transactions/documents/',
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
            },
            data: formData,
            processData: false,
            contentType: false,
            success: () => {
                this.showAlert('success', 'Remittance and documents uploaded successfully');
                setTimeout(() => {
                    window.location.href = '/transactions/type/REMITTANCE/';
                }, 2000);
            },
            error: () => {
                this.showAlert('warning', 'Remittance created but document upload failed');
                setTimeout(() => {
                    window.location.href = '/transactions/type/REMITTANCE/';
                }, 5000);
            }
        });
    }

    handleFileSelection() {
        const files = $('#document_files')[0].files;
        if (files.length > 0) {
            let fileListHtml = '';
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                fileListHtml += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-file-earmark"></i>
                            <span class="ms-2">${file.name}</span>
                            <small class="text-muted ms-2">(${fileSize} MB)</small>
                        </div>
                    </div>
                `;
            }
            $('#file-list').html(fileListHtml);
            $('#uploaded-files-preview').show();
        } else {
            $('#uploaded-files-preview').hide();
        }
    }

    getAuthHeaders() {
        return {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        };
    }

    showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        $('.alert').remove();
        $('.row').first().before(alertHtml);
        
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
}

// Global function for commission calculation
function calculateRemittanceCommission() {
    const amount = parseFloat($('#from_amount').val()) || 0;
    const fromCurrency = $('#from_currency').val();
    const toCurrency = $('#to_currency').val();
    const location = $('#location').val();

    if (!amount || !fromCurrency) {
        TransactionUtils.showAlert('warning', 'Please enter amount and select currency first');
        return;
    }

    TransactionUtils.calculateCommission(amount, 'REMITTANCE', fromCurrency, toCurrency || fromCurrency, location)
        .then(result => {
            $('#commission_amount').val(result.commission_amount.toFixed(6));
            $('#commission_info').html(`
                <small class="text-muted">
                    Commission: ${result.commission_percentage}% = ${TransactionUtils.formatCurrency(result.commission_amount, fromCurrency)}
                </small>
            `);
        })
        .catch(error => {
            console.error('Error calculating commission:', error);
            TransactionUtils.showAlert('danger', 'Error calculating commission');
        });
}

$(document).ready(() => {
    new RemittanceTransactionForm();
});
