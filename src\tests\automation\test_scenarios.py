"""
Arena Doviz Comprehensive Test Scenarios
"""
import asyncio
import json
import logging
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict

from browser_automation import BrowserAutomation, TransactionFormAutomation
from system_monitor import SystemMonitor
from config import TestConfig, TRANSACTION_TYPES, TEST_DATA_TEMPLATES

logger = logging.getLogger("arena_scenarios")

@dataclass
class TestScenario:
    """Test scenario definition"""
    name: str
    description: str
    transaction_type: str
    test_data: Dict[str, Any]
    expected_outcome: str  # 'success', 'validation_error', 'business_error'
    validation_rules: List[str]
    priority: int  # 1=high, 2=medium, 3=low

@dataclass
class ScenarioResult:
    """Test scenario execution result"""
    scenario: TestScenario
    status: str  # 'PASS', 'FAIL', 'ERROR'
    execution_time_ms: int
    errors: List[str]
    validation_results: Dict[str, bool]
    form_data_captured: Dict[str, Any]
    api_responses: List[Dict]
    screenshots: List[str]

class TestScenarioGenerator:
    """Generate comprehensive test scenarios for Arena Doviz"""
    
    def __init__(self):
        self.scenarios: List[TestScenario] = []
        self._generate_all_scenarios()
    
    def _generate_all_scenarios(self) -> None:
        """Generate all test scenarios"""
        # Generate scenarios for each transaction type
        for transaction_type in TRANSACTION_TYPES.keys():
            self._generate_transaction_scenarios(transaction_type)
        
        # Generate workflow scenarios
        self._generate_workflow_scenarios()
        
        # Generate edge case scenarios
        self._generate_edge_case_scenarios()
        
        # Generate error scenarios
        self._generate_error_scenarios()
        
        logger.info(f"Generated {len(self.scenarios)} test scenarios")
    
    def _generate_transaction_scenarios(self, transaction_type: str) -> None:
        """Generate scenarios for a specific transaction type"""
        base_scenarios = {
            'EXCHANGE': self._generate_exchange_scenarios(),
            'DEPOSIT': self._generate_deposit_scenarios(),
            'WITHDRAWAL': self._generate_withdrawal_scenarios(),
            'INTERNAL_TRANSFER': self._generate_internal_transfer_scenarios(),
            'EXTERNAL_TRANSFER': self._generate_external_transfer_scenarios(),
            'INTERNATIONAL_TRANSFER': self._generate_international_transfer_scenarios(),
            'REMITTANCE': self._generate_remittance_scenarios(),
            'ADJUSTMENT': self._generate_adjustment_scenarios()
        }
        
        if transaction_type in base_scenarios:
            self.scenarios.extend(base_scenarios[transaction_type])
    
    def _generate_exchange_scenarios(self) -> List[TestScenario]:
        """Generate currency exchange test scenarios"""
        scenarios = []
        
        # Basic exchange scenarios
        currency_pairs = [
            ('USD', 'AED'), ('AED', 'USD'), ('USD', 'IRR'), 
            ('IRR', 'USD'), ('AED', 'IRR'), ('IRR', 'AED')
        ]
        
        amounts = [100, 500, 1000, 5000, 10000]
        
        for from_curr, to_curr in currency_pairs:
            for amount in amounts:
                scenarios.append(TestScenario(
                    name=f"Exchange {amount} {from_curr} to {to_curr}",
                    description=f"Standard currency exchange from {from_curr} to {to_curr}",
                    transaction_type='EXCHANGE',
                    test_data={
                        'customer': 'test_customer_1',
                        'from_currency': from_curr,
                        'to_currency': to_curr,
                        'from_amount': amount,
                        'location': 'DXB'
                    },
                    expected_outcome='success',
                    validation_rules=[
                        'customer_balance_updated',
                        'exchange_rate_applied',
                        'commission_calculated',
                        'transaction_recorded'
                    ],
                    priority=1
                ))
        
        # High-value exchange
        scenarios.append(TestScenario(
            name="High Value Exchange USD to AED",
            description="High-value currency exchange requiring additional verification",
            transaction_type='EXCHANGE',
            test_data={
                'customer': 'test_customer_1',
                'from_currency': 'USD',
                'to_currency': 'AED',
                'from_amount': 50000,
                'location': 'DXB'
            },
            expected_outcome='success',
            validation_rules=[
                'customer_balance_updated',
                'exchange_rate_applied',
                'commission_calculated',
                'high_value_verification'
            ],
            priority=2
        ))
        
        return scenarios
    
    def _generate_deposit_scenarios(self) -> List[TestScenario]:
        """Generate cash deposit test scenarios"""
        scenarios = []
        
        currencies = ['USD', 'AED', 'IRR']
        amounts = [100, 500, 1000, 2500, 5000]
        
        for currency in currencies:
            for amount in amounts:
                scenarios.append(TestScenario(
                    name=f"Cash Deposit {amount} {currency}",
                    description=f"Cash deposit of {amount} {currency}",
                    transaction_type='DEPOSIT',
                    test_data={
                        'customer': 'test_customer_1',
                        'from_currency': currency,
                        'from_amount': amount,
                        'location': 'DXB',
                        'delivery_method': 'cash'
                    },
                    expected_outcome='success',
                    validation_rules=[
                        'customer_balance_increased',
                        'commission_calculated',
                        'transaction_recorded',
                        'receipt_generated'
                    ],
                    priority=1
                ))
        
        # Courier deposit scenario
        scenarios.append(TestScenario(
            name="Cash Deposit with Courier",
            description="Cash deposit delivered by courier",
            transaction_type='DEPOSIT',
            test_data={
                'customer': 'test_customer_1',
                'from_currency': 'USD',
                'from_amount': 1000,
                'location': 'DXB',
                'delivery_method': 'courier',
                'courier': 'test_courier_1'
            },
            expected_outcome='success',
            validation_rules=[
                'customer_balance_increased',
                'courier_assigned',
                'delivery_scheduled'
            ],
            priority=2
        ))
        
        return scenarios
    
    def _generate_withdrawal_scenarios(self) -> List[TestScenario]:
        """Generate cash withdrawal test scenarios"""
        scenarios = []
        
        currencies = ['USD', 'AED', 'IRR']
        amounts = [100, 500, 1000, 2500]
        
        for currency in currencies:
            for amount in amounts:
                scenarios.append(TestScenario(
                    name=f"Cash Withdrawal {amount} {currency}",
                    description=f"Cash withdrawal of {amount} {currency}",
                    transaction_type='WITHDRAWAL',
                    test_data={
                        'customer': 'test_customer_1',
                        'from_currency': currency,
                        'from_amount': amount,
                        'location': 'DXB',
                        'delivery_method': 'cash'
                    },
                    expected_outcome='success',
                    validation_rules=[
                        'sufficient_balance_check',
                        'customer_balance_decreased',
                        'commission_calculated',
                        'authorization_required'
                    ],
                    priority=1
                ))
        
        # Insufficient balance scenario
        scenarios.append(TestScenario(
            name="Withdrawal Insufficient Balance",
            description="Attempt withdrawal with insufficient balance",
            transaction_type='WITHDRAWAL',
            test_data={
                'customer': 'test_customer_empty',
                'from_currency': 'USD',
                'from_amount': 1000,
                'location': 'DXB'
            },
            expected_outcome='business_error',
            validation_rules=[
                'insufficient_balance_error',
                'transaction_rejected'
            ],
            priority=1
        ))
        
        return scenarios
    
    def _generate_internal_transfer_scenarios(self) -> List[TestScenario]:
        """Generate internal transfer test scenarios"""
        scenarios = []
        
        currencies = ['USD', 'AED', 'IRR']
        amounts = [100, 500, 1000, 2500]
        
        for currency in currencies:
            for amount in amounts:
                scenarios.append(TestScenario(
                    name=f"Internal Transfer {amount} {currency}",
                    description=f"Internal transfer of {amount} {currency} between customers",
                    transaction_type='INTERNAL_TRANSFER',
                    test_data={
                        'customer': 'test_customer_1',
                        'recipient_customer': 'test_customer_2',
                        'from_currency': currency,
                        'from_amount': amount,
                        'location': 'DXB'
                    },
                    expected_outcome='success',
                    validation_rules=[
                        'sender_balance_decreased',
                        'recipient_balance_increased',
                        'commission_calculated',
                        'transfer_recorded'
                    ],
                    priority=1
                ))
        
        return scenarios
    
    def _generate_external_transfer_scenarios(self) -> List[TestScenario]:
        """Generate external transfer test scenarios"""
        scenarios = []
        
        scenarios.append(TestScenario(
            name="External Transfer USD",
            description="External transfer to bank account",
            transaction_type='EXTERNAL_TRANSFER',
            test_data={
                'customer': 'test_customer_1',
                'from_currency': 'USD',
                'from_amount': 1000,
                'location': 'DXB',
                'recipient_details': {
                    'bank_name': 'Test Bank',
                    'account_number': '**********',
                    'recipient_name': 'John Smith'
                }
            },
            expected_outcome='success',
            validation_rules=[
                'sender_balance_decreased',
                'transfer_initiated',
                'recipient_details_validated'
            ],
            priority=2
        ))
        
        return scenarios
    
    def _generate_international_transfer_scenarios(self) -> List[TestScenario]:
        """Generate international transfer test scenarios"""
        scenarios = []
        
        scenarios.append(TestScenario(
            name="International Transfer USD to EUR",
            description="International SWIFT transfer",
            transaction_type='INTERNATIONAL_TRANSFER',
            test_data={
                'customer': 'test_customer_1',
                'from_currency': 'USD',
                'to_currency': 'EUR',
                'from_amount': 2000,
                'location': 'DXB',
                'recipient_details': {
                    'swift_code': 'DEUTDEFF',
                    'iban': '**********************',
                    'recipient_name': 'Hans Mueller',
                    'recipient_address': 'Berlin, Germany'
                }
            },
            expected_outcome='success',
            validation_rules=[
                'sender_balance_decreased',
                'swift_transfer_initiated',
                'compliance_check_passed'
            ],
            priority=2
        ))
        
        return scenarios
    
    def _generate_remittance_scenarios(self) -> List[TestScenario]:
        """Generate remittance test scenarios"""
        scenarios = []
        
        scenarios.append(TestScenario(
            name="Remittance USD to IRR",
            description="Remittance transfer to Iran",
            transaction_type='REMITTANCE',
            test_data={
                'customer': 'test_customer_1',
                'from_currency': 'USD',
                'to_currency': 'IRR',
                'from_amount': 1000,
                'location': 'DXB',
                'recipient_details': {
                    'recipient_name': 'Ali Hosseini',
                    'recipient_phone': '+98912345678',
                    'pickup_location': 'Tehran Branch'
                }
            },
            expected_outcome='success',
            validation_rules=[
                'sender_balance_decreased',
                'remittance_created',
                'pickup_code_generated'
            ],
            priority=2
        ))
        
        return scenarios
    
    def _generate_adjustment_scenarios(self) -> List[TestScenario]:
        """Generate balance adjustment test scenarios"""
        scenarios = []
        
        adjustment_types = ['credit', 'debit']
        currencies = ['USD', 'AED', 'IRR']
        
        for adj_type in adjustment_types:
            for currency in currencies:
                scenarios.append(TestScenario(
                    name=f"Balance Adjustment {adj_type.title()} {currency}",
                    description=f"Balance {adj_type} adjustment in {currency}",
                    transaction_type='ADJUSTMENT',
                    test_data={
                        'customer': 'test_customer_1',
                        'from_currency': currency,
                        'from_amount': 100 if adj_type == 'credit' else -100,
                        'adjustment_type': adj_type,
                        'reason': f'Test {adj_type} adjustment',
                        'location': 'DXB'
                    },
                    expected_outcome='success',
                    validation_rules=[
                        'balance_adjusted_correctly',
                        'adjustment_reason_recorded',
                        'authorization_logged'
                    ],
                    priority=2
                ))
        
        return scenarios
    
    def _generate_workflow_scenarios(self) -> None:
        """Generate complete workflow scenarios"""
        # Customer onboarding + first transaction
        self.scenarios.append(TestScenario(
            name="Complete Customer Workflow",
            description="Create customer, add balance, perform exchange",
            transaction_type='WORKFLOW',
            test_data={
                'workflow_steps': [
                    {'action': 'create_customer', 'data': TEST_DATA_TEMPLATES['customers'][0]},
                    {'action': 'deposit', 'data': {'amount': 1000, 'currency': 'USD'}},
                    {'action': 'exchange', 'data': {'from_currency': 'USD', 'to_currency': 'AED', 'amount': 500}}
                ]
            },
            expected_outcome='success',
            validation_rules=[
                'customer_created',
                'initial_deposit_successful',
                'exchange_completed',
                'final_balance_correct'
            ],
            priority=1
        ))
    
    def _generate_edge_case_scenarios(self) -> None:
        """Generate edge case scenarios"""
        # Zero amount transaction
        self.scenarios.append(TestScenario(
            name="Zero Amount Transaction",
            description="Attempt transaction with zero amount",
            transaction_type='EXCHANGE',
            test_data={
                'customer': 'test_customer_1',
                'from_currency': 'USD',
                'to_currency': 'AED',
                'from_amount': 0,
                'location': 'DXB'
            },
            expected_outcome='validation_error',
            validation_rules=['zero_amount_rejected'],
            priority=2
        ))
        
        # Negative amount transaction
        self.scenarios.append(TestScenario(
            name="Negative Amount Transaction",
            description="Attempt transaction with negative amount",
            transaction_type='DEPOSIT',
            test_data={
                'customer': 'test_customer_1',
                'from_currency': 'USD',
                'from_amount': -100,
                'location': 'DXB'
            },
            expected_outcome='validation_error',
            validation_rules=['negative_amount_rejected'],
            priority=2
        ))
        
        # Maximum amount transaction
        self.scenarios.append(TestScenario(
            name="Maximum Amount Transaction",
            description="Transaction at maximum allowed limit",
            transaction_type='EXCHANGE',
            test_data={
                'customer': 'test_customer_1',
                'from_currency': 'USD',
                'to_currency': 'AED',
                'from_amount': 999999.99,
                'location': 'DXB'
            },
            expected_outcome='success',
            validation_rules=['large_amount_processed'],
            priority=3
        ))
    
    def _generate_error_scenarios(self) -> None:
        """Generate error scenarios"""
        # Missing required fields
        self.scenarios.append(TestScenario(
            name="Missing Customer Field",
            description="Submit form without selecting customer",
            transaction_type='EXCHANGE',
            test_data={
                'from_currency': 'USD',
                'to_currency': 'AED',
                'from_amount': 100,
                'location': 'DXB'
            },
            expected_outcome='validation_error',
            validation_rules=['customer_required_error'],
            priority=1
        ))
        
        # Invalid currency combination
        self.scenarios.append(TestScenario(
            name="Invalid Currency Combination",
            description="Exchange between same currencies",
            transaction_type='EXCHANGE',
            test_data={
                'customer': 'test_customer_1',
                'from_currency': 'USD',
                'to_currency': 'USD',
                'from_amount': 100,
                'location': 'DXB'
            },
            expected_outcome='validation_error',
            validation_rules=['same_currency_error'],
            priority=2
        ))
    
    def get_scenarios_by_priority(self, priority: int) -> List[TestScenario]:
        """Get scenarios by priority level"""
        return [s for s in self.scenarios if s.priority == priority]
    
    def get_scenarios_by_type(self, transaction_type: str) -> List[TestScenario]:
        """Get scenarios by transaction type"""
        return [s for s in self.scenarios if s.transaction_type == transaction_type]
    
    def get_all_scenarios(self) -> List[TestScenario]:
        """Get all scenarios"""
        return self.scenarios

class TestScenarioExecutor:
    """Execute test scenarios and collect results"""

    def __init__(self, browser_automation: BrowserAutomation, system_monitor: SystemMonitor):
        self.browser = browser_automation
        self.form_automation = TransactionFormAutomation(browser_automation)
        self.system_monitor = system_monitor
        self.results: List[ScenarioResult] = []

    async def execute_scenario(self, scenario: TestScenario) -> ScenarioResult:
        """Execute a single test scenario"""
        start_time = datetime.now()
        errors = []
        validation_results = {}
        screenshots = []

        try:
            # Start test in browser automation
            await self.browser.start_test(scenario.name, scenario.transaction_type)

            logger.info(f"🧪 Executing scenario: {scenario.name}")

            # Handle different scenario types
            if scenario.transaction_type == 'WORKFLOW':
                success = await self._execute_workflow_scenario(scenario)
            else:
                success = await self._execute_transaction_scenario(scenario)

            # Validate results
            validation_results = await self._validate_scenario_results(scenario)

            # Determine final status
            if success and all(validation_results.values()):
                status = 'PASS'
            elif not success:
                status = 'FAIL'
                errors.append("Scenario execution failed")
            else:
                status = 'FAIL'
                failed_validations = [k for k, v in validation_results.items() if not v]
                errors.append(f"Validation failed: {', '.join(failed_validations)}")

        except Exception as e:
            status = 'ERROR'
            errors.append(f"Exception during execution: {str(e)}")
            logger.error(f"💥 Scenario execution error: {str(e)}")

        # End test and get results
        end_time = datetime.now()
        execution_time = int((end_time - start_time).total_seconds() * 1000)

        # Get form data if available
        form_data = {}
        try:
            form_data = await self.form_automation.get_form_data()
        except:
            pass

        # End browser test
        test_result = await self.browser.end_test(status, errors)

        # Create scenario result
        scenario_result = ScenarioResult(
            scenario=scenario,
            status=status,
            execution_time_ms=execution_time,
            errors=errors,
            validation_results=validation_results,
            form_data_captured=form_data,
            api_responses=test_result.api_responses if test_result else [],
            screenshots=[test_result.screenshot_path] if test_result and test_result.screenshot_path else []
        )

        self.results.append(scenario_result)
        return scenario_result

    async def _execute_transaction_scenario(self, scenario: TestScenario) -> bool:
        """Execute a single transaction scenario"""
        try:
            # Navigate to transaction form
            if not await self.form_automation.navigate_to_transaction_form(scenario.transaction_type):
                return False

            # Wait for form to load
            await asyncio.sleep(2)

            # Fill form fields
            for field_name, field_value in scenario.test_data.items():
                if field_name in ['customer', 'recipient_customer', 'location']:
                    # Handle dropdown fields
                    await self.form_automation.wait_for_dropdown_options(field_name)
                    await self.form_automation.fill_form_field(field_name, field_value, 'select')
                elif field_name == 'recipient_details':
                    # Handle complex recipient details
                    await self._fill_recipient_details(field_value)
                else:
                    # Handle regular input fields
                    await self.form_automation.fill_form_field(field_name, str(field_value))

                # Small delay between fields
                await asyncio.sleep(0.5)

            # Check for validation errors before submission
            validation_errors = await self.form_automation.check_form_validation_errors()
            if validation_errors and scenario.expected_outcome != 'validation_error':
                logger.warning(f"⚠️ Unexpected validation errors: {validation_errors}")
                return False

            # Submit form if no validation errors expected
            if scenario.expected_outcome != 'validation_error':
                return await self.form_automation.submit_form()
            else:
                # For validation error scenarios, just check that errors are present
                return len(validation_errors) > 0

        except Exception as e:
            logger.error(f"❌ Transaction scenario execution failed: {str(e)}")
            return False

    async def _execute_workflow_scenario(self, scenario: TestScenario) -> bool:
        """Execute a workflow scenario with multiple steps"""
        try:
            workflow_steps = scenario.test_data.get('workflow_steps', [])

            for step in workflow_steps:
                action = step['action']
                data = step['data']

                if action == 'create_customer':
                    success = await self._create_customer(data)
                elif action == 'deposit':
                    success = await self._perform_deposit(data)
                elif action == 'exchange':
                    success = await self._perform_exchange(data)
                else:
                    logger.warning(f"⚠️ Unknown workflow action: {action}")
                    continue

                if not success:
                    logger.error(f"❌ Workflow step failed: {action}")
                    return False

                # Wait between steps
                await asyncio.sleep(2)

            return True

        except Exception as e:
            logger.error(f"❌ Workflow scenario execution failed: {str(e)}")
            return False

    async def _fill_recipient_details(self, recipient_details: Dict[str, str]) -> None:
        """Fill recipient details fields"""
        field_mapping = {
            'bank_name': 'recipient_bank_name',
            'account_number': 'recipient_account_number',
            'recipient_name': 'recipient_name',
            'swift_code': 'swift_code',
            'iban': 'iban',
            'recipient_phone': 'recipient_phone',
            'pickup_location': 'pickup_location',
            'recipient_address': 'recipient_address'
        }

        for key, value in recipient_details.items():
            if key in field_mapping:
                await self.form_automation.fill_form_field(field_mapping[key], str(value))

    async def _create_customer(self, customer_data: Dict[str, str]) -> bool:
        """Create a new customer"""
        try:
            # Navigate to customer creation form
            await self.browser.page.goto(f"{TestConfig.BASE_URL}/customers/add/")
            await self.browser.page.wait_for_load_state('networkidle')

            # Fill customer form
            for field, value in customer_data.items():
                await self.form_automation.fill_form_field(field, str(value))

            # Submit customer form
            return await self.form_automation.submit_form()

        except Exception as e:
            logger.error(f"❌ Customer creation failed: {str(e)}")
            return False

    async def _perform_deposit(self, deposit_data: Dict[str, Any]) -> bool:
        """Perform a deposit transaction"""
        try:
            # Navigate to deposit form
            if not await self.form_automation.navigate_to_transaction_form('DEPOSIT'):
                return False

            # Fill deposit form
            await self.form_automation.fill_form_field('customer', 'test_customer_1', 'select')
            await self.form_automation.fill_form_field('from_currency', deposit_data['currency'], 'select')
            await self.form_automation.fill_form_field('from_amount', str(deposit_data['amount']))

            # Submit form
            return await self.form_automation.submit_form()

        except Exception as e:
            logger.error(f"❌ Deposit failed: {str(e)}")
            return False

    async def _perform_exchange(self, exchange_data: Dict[str, Any]) -> bool:
        """Perform an exchange transaction"""
        try:
            # Navigate to exchange form
            if not await self.form_automation.navigate_to_transaction_form('EXCHANGE'):
                return False

            # Fill exchange form
            await self.form_automation.fill_form_field('customer', 'test_customer_1', 'select')
            await self.form_automation.fill_form_field('from_currency', exchange_data['from_currency'], 'select')
            await self.form_automation.fill_form_field('to_currency', exchange_data['to_currency'], 'select')
            await self.form_automation.fill_form_field('from_amount', str(exchange_data['amount']))

            # Submit form
            return await self.form_automation.submit_form()

        except Exception as e:
            logger.error(f"❌ Exchange failed: {str(e)}")
            return False

    async def _validate_scenario_results(self, scenario: TestScenario) -> Dict[str, bool]:
        """Validate scenario results against expected rules"""
        validation_results = {}

        for rule in scenario.validation_rules:
            try:
                validation_results[rule] = await self._check_validation_rule(rule, scenario)
            except Exception as e:
                logger.error(f"❌ Validation rule check failed for {rule}: {str(e)}")
                validation_results[rule] = False

        return validation_results

    async def _check_validation_rule(self, rule: str, scenario: TestScenario) -> bool:
        """Check a specific validation rule"""
        # This is a simplified implementation
        # In a real system, you would check actual database state, API responses, etc.

        if rule == 'customer_balance_updated':
            # Check if customer balance was updated correctly
            return True  # Placeholder
        elif rule == 'exchange_rate_applied':
            # Check if exchange rate was applied correctly
            return True  # Placeholder
        elif rule == 'commission_calculated':
            # Check if commission was calculated correctly
            return True  # Placeholder
        elif rule == 'transaction_recorded':
            # Check if transaction was recorded in database
            return True  # Placeholder
        elif rule == 'insufficient_balance_error':
            # Check if insufficient balance error was shown
            errors = await self.form_automation.check_form_validation_errors()
            return any('insufficient' in error.lower() or 'balance' in error.lower() for error in errors)
        elif rule == 'zero_amount_rejected':
            # Check if zero amount was rejected
            errors = await self.form_automation.check_form_validation_errors()
            return any('amount' in error.lower() and ('zero' in error.lower() or 'required' in error.lower()) for error in errors)
        else:
            logger.warning(f"⚠️ Unknown validation rule: {rule}")
            return True  # Default to pass for unknown rules

    def get_execution_summary(self) -> Dict[str, Any]:
        """Get summary of all scenario executions"""
        total_scenarios = len(self.results)
        passed = len([r for r in self.results if r.status == 'PASS'])
        failed = len([r for r in self.results if r.status == 'FAIL'])
        errors = len([r for r in self.results if r.status == 'ERROR'])

        total_time = sum(r.execution_time_ms for r in self.results)
        avg_time = total_time / total_scenarios if total_scenarios > 0 else 0

        return {
            'total_scenarios': total_scenarios,
            'passed': passed,
            'failed': failed,
            'errors': errors,
            'success_rate': (passed / total_scenarios * 100) if total_scenarios > 0 else 0,
            'total_execution_time_ms': total_time,
            'average_execution_time_ms': avg_time,
            'scenario_results': [asdict(r) for r in self.results]
        }
