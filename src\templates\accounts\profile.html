{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Profile" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-person-circle me-2"></i>
                    {% trans "User Profile" %}
                </h1>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="profile-avatar mb-3">
                        <i class="bi bi-person-circle" style="font-size: 5rem; color: #667eea;"></i>
                    </div>
                    <h5 class="card-title" id="user-display-name">Loading...</h5>
                    <p class="card-text text-muted" id="user-role">Loading...</p>
                    <p class="card-text">
                        <small class="text-muted" id="user-location">Loading...</small>
                    </p>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-shield-check me-2"></i>
                        {% trans "Permissions" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div id="user-permissions">
                        <div class="text-center">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-person-lines-fill me-2"></i>
                        {% trans "Profile Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <form id="profile-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">{% trans "First Name" %}</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">{% trans "Last Name" %}</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">{% trans "Username" %}</label>
                                    <input type="text" class="form-control" id="username" name="username" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">{% trans "Email" %}</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone_number" class="form-label">{% trans "Phone Number" %}</label>
                                    <input type="tel" class="form-control" id="phone_number" name="phone_number">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="employee_id" class="form-label">{% trans "Employee ID" %}</label>
                                    <input type="text" class="form-control" id="employee_id" name="employee_id" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="department" class="form-label">{% trans "Department" %}</label>
                            <input type="text" class="form-control" id="department" name="department">
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-primary" onclick="showChangePasswordModal()">
                                <i class="bi bi-key me-2"></i>
                                {% trans "Change Password" %}
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-2"></i>
                                {% trans "Update Profile" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-key me-2"></i>
                    {% trans "Change Password" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="change-password-form">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">{% trans "Current Password" %}</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">{% trans "New Password" %}</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">{% trans "Confirm New Password" %}</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" onclick="changePassword()">
                    {% trans "Change Password" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadUserProfile();
    
    $('#profile-form').on('submit', function(e) {
        e.preventDefault();
        updateProfile();
    });
});

function loadUserProfile() {
    console.log('Loading user profile...');

    // Show loading state
    showLoadingState(true);

    // Get user data from localStorage (set during JWT login)
    const userData = localStorage.getItem('arena_user_data');
    if (userData) {
        try {
            const user = JSON.parse(userData);
            console.log('Loaded user data from localStorage:', user);
            populateUserProfile(user);
        } catch (e) {
            console.error('Error parsing localStorage user data:', e);
        }
    }

    // Also fetch fresh data from API
    console.log('Fetching fresh profile data from API...');
    ArenaDoviz.api.request('GET', 'accounts/users/profile/')
        .then(data => {
            console.log('Profile data received from API:', data);
            populateUserProfile(data);
            // Update localStorage with fresh data
            localStorage.setItem('arena_user_data', JSON.stringify(data));
            showLoadingState(false);
        })
        .catch(error => {
            console.error('Error loading profile:', error);
            showLoadingState(false);

            // Show more detailed error information
            let errorMessage = '{% trans "Failed to load profile data" %}';
            if (error.message) {
                errorMessage += ': ' + error.message;
            }

            showAlert('error', errorMessage);

            // If we have localStorage data, at least show that
            if (!userData) {
                // No fallback data, show error state
                $('#user-display-name').text('{% trans "Error loading profile" %}');
                $('#user-role').text('{% trans "Please refresh the page" %}');
                $('#user-location').text('');
            }
        });
}

function showLoadingState(show) {
    if (show) {
        $('#user-display-name').text('{% trans "Loading..." %}');
        $('#user-role').text('{% trans "Loading..." %}');
        $('#user-location').text('{% trans "Loading..." %}');
        $('#user-permissions').html('<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">{% trans "Loading..." %}</span></div></div>');
    }
}

function populateUserProfile(user) {
    console.log('Populating user profile with data:', user);

    // Update profile card
    const displayName = (user.first_name && user.last_name) ?
        `${user.first_name} ${user.last_name}` :
        (user.username || '{% trans "Unknown User" %}');

    $('#user-display-name').text(displayName);
    $('#user-role').text(user.role_display || user.role || '{% trans "No role assigned" %}');
    $('#user-location').text(user.location_name || (user.location ? user.location.name : '{% trans "No location assigned" %}'));
    
    // Update form fields
    $('#first_name').val(user.first_name || '');
    $('#last_name').val(user.last_name || '');
    $('#username').val(user.username || '');
    $('#email').val(user.email || '');
    $('#phone_number').val(user.phone_number || '');
    $('#employee_id').val(user.employee_id || '');
    $('#department').val(user.department || '');
    
    // Update permissions
    if (user.permissions) {
        updatePermissionsDisplay(user.permissions);
    }
}

function updatePermissionsDisplay(permissions) {
    const permissionsHtml = Object.entries(permissions).map(([key, value]) => {
        const icon = value ? 'bi-check-circle text-success' : 'bi-x-circle text-danger';
        const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        return `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>${label}</span>
                <i class="bi ${icon}"></i>
            </div>
        `;
    }).join('');
    
    $('#user-permissions').html(permissionsHtml);
}

function updateProfile() {
    const formData = {
        first_name: $('#first_name').val(),
        last_name: $('#last_name').val(),
        email: $('#email').val(),
        phone_number: $('#phone_number').val(),
        department: $('#department').val()
    };
    
    ArenaDoviz.api.request('PUT', 'accounts/users/update_profile/', formData)
        .then(data => {
            showAlert('success', '{% trans "Profile updated successfully" %}');
            // Update localStorage
            localStorage.setItem('arena_user_data', JSON.stringify(data));
        })
        .catch(error => {
            console.error('Error updating profile:', error);
            showAlert('error', '{% trans "Failed to update profile" %}');
        });
}

function showChangePasswordModal() {
    $('#changePasswordModal').modal('show');
}

function changePassword() {
    const currentPassword = $('#current_password').val();
    const newPassword = $('#new_password').val();
    const confirmPassword = $('#confirm_password').val();
    
    if (newPassword !== confirmPassword) {
        showAlert('error', '{% trans "New passwords do not match" %}');
        return;
    }
    
    const data = {
        current_password: currentPassword,
        new_password: newPassword
    };
    
    ArenaDoviz.api.request('POST', 'accounts/users/change_password/', data)
        .then(response => {
            $('#changePasswordModal').modal('hide');
            $('#change-password-form')[0].reset();
            showAlert('success', '{% trans "Password changed successfully" %}');
        })
        .catch(error => {
            console.error('Error changing password:', error);
            showAlert('error', '{% trans "Failed to change password" %}');
        });
}

function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Insert alert at the top of the container
    $('.container-fluid').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
