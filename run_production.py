#!/usr/bin/env python
"""
Production server runner for Arena Doviz using Waitress WSGI server.
This script starts the Arena Doviz application in production mode.
"""

import os
import sys
import logging
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Check for encryption key before starting
def check_encryption_key():
    """Check if encryption key is properly configured."""
    encryption_key = os.environ.get('ARENA_ENCRYPTION_KEY')

    if not encryption_key:
        print("ERROR: ARENA_ENCRYPTION_KEY environment variable is not set!")
        print("\nTo fix this issue:")
        print("1. Run the setup script: python deployment/production_setup.py")
        print("2. Or manually set the environment variable:")
        print("   Windows: set ARENA_ENCRYPTION_KEY=your-key-here")
        print("   Linux/macOS: export ARENA_ENCRYPTION_KEY=your-key-here")
        print("\nTo generate a new key, run:")
        print('   python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"')
        return False

    # Validate key format
    try:
        from cryptography.fernet import Fernet
        Fernet(encryption_key.encode())
        print(f"✓ Encryption key validated: {encryption_key[:20]}...")
        return True
    except Exception as e:
        print(f"ERROR: Invalid encryption key format: {e}")
        print("Please generate a new key using the setup script.")
        return False

# Validate encryption key
if not check_encryption_key():
    sys.exit(1)

# Set Django settings module for production
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Create logs directory
logs_dir = Path(__file__).parent / 'logs'
logs_dir.mkdir(exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.FileHandler(logs_dir / 'production.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Start the production server."""
    try:
        # Import Django and configure
        import django
        django.setup()
        
        logger.info("Starting Arena Doviz production server...")
        logger.info("Server will be accessible at: http://0.0.0.0:8000")
        logger.info("Press Ctrl+C to stop the server")
        
        # Import and start Waitress
        from waitress import serve
        from django.core.wsgi import get_wsgi_application
        from django.contrib.staticfiles.handlers import StaticFilesHandler

        # Get the WSGI application with static files handling
        application = StaticFilesHandler(get_wsgi_application())
        
        # Server configuration
        serve(
            application,
            host='0.0.0.0',  # Bind to all interfaces
            port=8000,       # Port 8000
            threads=6,       # Number of threads
            connection_limit=1000,  # Max connections
            cleanup_interval=30,    # Cleanup interval
            channel_timeout=120,    # Channel timeout
            log_untrusted_proxy_headers=True,
            clear_untrusted_proxy_headers=True,
            # Logging
            _quiet=False,
        )
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
