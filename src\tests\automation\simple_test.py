"""
Simple Arena Doviz Production Test
"""
import asyncio
import json
import logging
import requests
import time
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleProductionTest:
    def __init__(self):
        self.production_url = "http://127.0.0.1:8000"  # Using local server since production is not accessible
        self.test_results = {}
        
    async def run_basic_tests(self):
        """Run basic production environment tests"""
        logger.info("🚀 Starting basic production tests...")
        
        # Test 1: Basic connectivity
        logger.info("🔍 Testing basic connectivity...")
        connectivity_result = await self.test_connectivity()
        
        # Test 2: API endpoints
        logger.info("🔍 Testing API endpoints...")
        api_result = await self.test_api_endpoints()
        
        # Test 3: Authentication
        logger.info("🔍 Testing authentication...")
        auth_result = await self.test_authentication()
        
        # Test 4: Key pages
        logger.info("🔍 Testing key pages...")
        pages_result = await self.test_key_pages()
        
        # Compile results
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'production_url': self.production_url,
            'connectivity': connectivity_result,
            'api_endpoints': api_result,
            'authentication': auth_result,
            'key_pages': pages_result
        }
        
        # Generate report
        await self.generate_report()
        
        return self.test_results
    
    async def test_connectivity(self):
        """Test basic connectivity to production server"""
        try:
            start_time = time.time()
            response = requests.get(self.production_url, timeout=30)
            response_time = (time.time() - start_time) * 1000
            
            return {
                'status': 'PASS' if response.status_code == 200 else 'FAIL',
                'status_code': response.status_code,
                'response_time_ms': response_time,
                'accessible': response.status_code == 200,
                'error': None
            }
        except Exception as e:
            return {
                'status': 'ERROR',
                'status_code': 0,
                'response_time_ms': 0,
                'accessible': False,
                'error': str(e)
            }
    
    async def test_api_endpoints(self):
        """Test key API endpoints"""
        endpoints = [
            '/api/v1/customers/customers/',
            '/api/v1/transactions/transactions/',
            '/api/v1/currencies/currencies/',
            '/api/v1/locations/locations/',
            '/api/v1/currencies/rates/current/'
        ]
        
        results = {}
        
        for endpoint in endpoints:
            try:
                url = f"{self.production_url}{endpoint}"
                start_time = time.time()
                response = requests.get(url, timeout=10)
                response_time = (time.time() - start_time) * 1000
                
                results[endpoint] = {
                    'status': 'PASS' if response.status_code in [200, 401, 403] else 'FAIL',
                    'status_code': response.status_code,
                    'response_time_ms': response_time,
                    'accessible': response.status_code in [200, 401, 403],
                    'error': None
                }
            except Exception as e:
                results[endpoint] = {
                    'status': 'ERROR',
                    'status_code': 0,
                    'response_time_ms': 0,
                    'accessible': False,
                    'error': str(e)
                }
        
        return results
    
    async def test_authentication(self):
        """Test authentication endpoint"""
        try:
            auth_url = f"{self.production_url}/api/v1/auth/token/"
            auth_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            start_time = time.time()
            response = requests.post(auth_url, json=auth_data, timeout=10)
            response_time = (time.time() - start_time) * 1000
            
            success = response.status_code == 200
            token = None
            
            if success:
                try:
                    token_data = response.json()
                    token = token_data.get('access', '')
                except:
                    pass
            
            return {
                'status': 'PASS' if success else 'FAIL',
                'status_code': response.status_code,
                'response_time_ms': response_time,
                'token_received': bool(token),
                'error': None if success else response.text
            }
        except Exception as e:
            return {
                'status': 'ERROR',
                'status_code': 0,
                'response_time_ms': 0,
                'token_received': False,
                'error': str(e)
            }
    
    async def test_key_pages(self):
        """Test key application pages"""
        pages = [
            '/accounts/login/',
            '/dashboard/',
            '/transactions/',
            '/customers/',
            '/reports/'
        ]
        
        results = {}
        
        for page in pages:
            try:
                url = f"{self.production_url}{page}"
                start_time = time.time()
                response = requests.get(url, timeout=10)
                response_time = (time.time() - start_time) * 1000
                
                # Check if page loads (200) or redirects to login (302)
                success = response.status_code in [200, 302]
                
                results[page] = {
                    'status': 'PASS' if success else 'FAIL',
                    'status_code': response.status_code,
                    'response_time_ms': response_time,
                    'accessible': success,
                    'error': None
                }
            except Exception as e:
                results[page] = {
                    'status': 'ERROR',
                    'status_code': 0,
                    'response_time_ms': 0,
                    'accessible': False,
                    'error': str(e)
                }
        
        return results
    
    async def generate_report(self):
        """Generate test report"""
        # Calculate overall status
        overall_status = 'PASS'
        
        if self.test_results['connectivity']['status'] != 'PASS':
            overall_status = 'FAIL'
        
        failed_apis = [ep for ep, result in self.test_results['api_endpoints'].items() 
                      if result['status'] != 'PASS']
        if len(failed_apis) > len(self.test_results['api_endpoints']) / 2:
            overall_status = 'FAIL'
        
        if self.test_results['authentication']['status'] != 'PASS':
            overall_status = 'FAIL'
        
        # Create summary
        summary = {
            'overall_status': overall_status,
            'timestamp': self.test_results['timestamp'],
            'production_url': self.production_url,
            'summary': {
                'connectivity': self.test_results['connectivity']['status'],
                'authentication': self.test_results['authentication']['status'],
                'api_endpoints_passed': len([ep for ep, result in self.test_results['api_endpoints'].items() 
                                           if result['status'] == 'PASS']),
                'api_endpoints_total': len(self.test_results['api_endpoints']),
                'pages_passed': len([page for page, result in self.test_results['key_pages'].items() 
                                   if result['status'] == 'PASS']),
                'pages_total': len(self.test_results['key_pages'])
            },
            'detailed_results': self.test_results
        }
        
        # Save report
        report_path = Path('reports') / f"simple_production_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        # Print summary
        print("\n" + "="*60)
        print("🏭 ARENA DOVIZ PRODUCTION TEST RESULTS")
        print("="*60)
        print(f"Overall Status: {overall_status}")
        print(f"Production URL: {self.production_url}")
        print(f"Test Time: {self.test_results['timestamp']}")
        print("\n📊 SUMMARY:")
        print(f"  Connectivity: {self.test_results['connectivity']['status']}")
        print(f"  Authentication: {self.test_results['authentication']['status']}")
        print(f"  API Endpoints: {summary['summary']['api_endpoints_passed']}/{summary['summary']['api_endpoints_total']} PASSED")
        print(f"  Key Pages: {summary['summary']['pages_passed']}/{summary['summary']['pages_total']} PASSED")
        
        if overall_status == 'FAIL':
            print("\n❌ ISSUES FOUND:")
            if self.test_results['connectivity']['status'] != 'PASS':
                print(f"  - Connectivity: {self.test_results['connectivity']['error']}")
            if self.test_results['authentication']['status'] != 'PASS':
                print(f"  - Authentication: {self.test_results['authentication']['error']}")
            
            failed_apis = [ep for ep, result in self.test_results['api_endpoints'].items() 
                          if result['status'] != 'PASS']
            for api in failed_apis:
                result = self.test_results['api_endpoints'][api]
                print(f"  - API {api}: {result['error'] or f'Status {result['status_code']}'}")
        
        print(f"\n📄 Detailed report saved to: {report_path}")
        print("="*60)
        
        return summary

async def main():
    """Main function"""
    test = SimpleProductionTest()
    results = await test.run_basic_tests()
    return results

if __name__ == "__main__":
    asyncio.run(main())
