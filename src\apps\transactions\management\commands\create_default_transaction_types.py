"""
Management command to create default transaction types for Arena Doviz.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.transactions.models import TransactionType


class Command(BaseCommand):
    help = 'Create default transaction types for Arena Doviz'

    def handle(self, *args, **options):
        """Create default transaction types."""
        
        default_types = [
            {
                'code': 'BUY',
                'name': 'Currency Purchase',
                'description': 'Customer buys foreign currency from exchange office',
                'is_exchange': True,
                'requires_approval': False,
                'sort_order': 1
            },
            {
                'code': 'SELL',
                'name': 'Currency Sale',
                'description': 'Customer sells foreign currency to exchange office',
                'is_exchange': True,
                'requires_approval': False,
                'sort_order': 2
            },
            {
                'code': 'EXCHANGE',
                'name': 'Currency Exchange',
                'description': 'Direct currency exchange between two currencies',
                'is_exchange': True,
                'requires_approval': False,
                'sort_order': 3
            },
            {
                'code': 'TRANSFER',
                'name': 'Money Transfer',
                'description': 'International money transfer service',
                'is_exchange': False,
                'requires_approval': True,
                'sort_order': 4
            },
            {
                'code': 'DEPOSIT',
                'name': 'Cash Deposit',
                'description': 'Customer deposits cash to their account',
                'is_exchange': False,
                'requires_approval': False,
                'sort_order': 5
            },
            {
                'code': 'WITHDRAWAL',
                'name': 'Cash Withdrawal',
                'description': 'Customer withdraws cash from their account',
                'is_exchange': False,
                'requires_approval': False,
                'sort_order': 6
            },
            {
                'code': 'REMITTANCE',
                'name': 'Remittance Service',
                'description': 'Money remittance to other countries',
                'is_exchange': False,
                'requires_approval': True,
                'sort_order': 7
            },
            {
                'code': 'ADJUSTMENT',
                'name': 'Balance Adjustment',
                'description': 'Manual balance adjustment for corrections',
                'is_exchange': False,
                'requires_approval': True,
                'sort_order': 8
            }
        ]

        created_count = 0
        updated_count = 0

        with transaction.atomic():
            for type_data in default_types:
                transaction_type, created = TransactionType.objects.get_or_create(
                    code=type_data['code'],
                    defaults=type_data
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Created transaction type: {transaction_type.code} - {transaction_type.name}'
                        )
                    )
                else:
                    # Update existing type with new data
                    for key, value in type_data.items():
                        if key != 'code':  # Don't update the code
                            setattr(transaction_type, key, value)
                    transaction_type.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(
                            f'Updated transaction type: {transaction_type.code} - {transaction_type.name}'
                        )
                    )

        self.stdout.write(
            self.style.SUCCESS(
                f'\nSummary: Created {created_count} new transaction types, '
                f'updated {updated_count} existing types.'
            )
        )
