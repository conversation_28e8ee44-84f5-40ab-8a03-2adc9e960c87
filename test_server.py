#!/usr/bin/env python3
"""
Test if the Arena Doviz server is running
"""
import requests
import time

def test_server():
    """Test if server is running"""
    try:
        print("Testing server connection...")
        response = requests.get('http://127.0.0.1:8000/health/', timeout=5)
        print(f"✅ Server is running! Status: {response.status_code}")
        print(f"Response: {response.text}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error testing server: {str(e)}")
        return False

if __name__ == "__main__":
    test_server()
