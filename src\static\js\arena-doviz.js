/**
 * Arena Doviz Exchange Accounting System - JavaScript Functions
 */

// Global configuration
const ArenaDoviz = {
    config: {
        apiBaseUrl: '/api/v1/',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: 'HH:mm:ss',
        currencyPrecision: 2,
        refreshInterval: 300000 // 5 minutes
    },
    
    // Utility functions
    utils: {
        /**
         * Format currency amount
         */
        formatCurrency: function(amount, currency, precision = 2) {
            if (amount === null || amount === undefined) return '-';
            
            const symbols = {
                'USD': '$',
                'AED': 'د.إ',
                'IRR': '﷼'
            };
            
            const symbol = symbols[currency] || currency;
            const formatted = parseFloat(amount).toLocaleString('en-US', {
                minimumFractionDigits: precision,
                maximumFractionDigits: precision
            });
            
            return `${symbol} ${formatted}`;
        },
        
        /**
         * Format date
         */
        formatDate: function(dateString, includeTime = false) {
            if (!dateString) return '-';
            
            const date = new Date(dateString);
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            };
            
            if (includeTime) {
                options.hour = '2-digit';
                options.minute = '2-digit';
            }
            
            return date.toLocaleDateString('en-US', options);
        },
        
        /**
         * Show loading spinner
         */
        showLoading: function(element) {
            const spinner = '<div class="loading-spinner"></div>';
            $(element).html(spinner);
        },
        
        /**
         * Show error message
         */
        showError: function(message, container = '.alert-container') {
            const alert = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $(container).html(alert);
        },
        
        /**
         * Show success message
         */
        showSuccess: function(message, container = '.alert-container') {
            const alert = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $(container).html(alert);
        },
        
        /**
         * Validate form
         */
        validateForm: function(formSelector) {
            const form = $(formSelector)[0];
            if (!form) return false;
            
            form.classList.add('was-validated');
            return form.checkValidity();
        },
        
        /**
         * Get CSRF token
         */
        getCSRFToken: function() {
            return $('[name=csrfmiddlewaretoken]').val() || 
                   $('meta[name=csrf-token]').attr('content');
        }
    },
    
    // Authentication functions
    auth: {
        /**
         * Get JWT access token from localStorage
         */
        getAccessToken: function() {
            return localStorage.getItem('arena_access_token');
        },

        /**
         * Get JWT refresh token from localStorage
         */
        getRefreshToken: function() {
            return localStorage.getItem('arena_refresh_token');
        },

        /**
         * Set JWT tokens in localStorage
         */
        setTokens: function(accessToken, refreshToken) {
            localStorage.setItem('arena_access_token', accessToken);
            if (refreshToken) {
                localStorage.setItem('arena_refresh_token', refreshToken);
            }
        },

        /**
         * Clear JWT tokens from localStorage
         */
        clearTokens: function() {
            localStorage.removeItem('arena_access_token');
            localStorage.removeItem('arena_refresh_token');
            localStorage.removeItem('arena_user_data');
        },

        /**
         * Check if user is authenticated (JWT or session)
         */
        isAuthenticated: function() {
            return !!this.getAccessToken();
        },

        /**
         * Check if user is authenticated via Django session
         */
        isSessionAuthenticated: function() {
            // This will be set by Django template if user is authenticated
            return window.isSessionAuthenticated || false;
        },

        /**
         * Get authentication headers for API requests
         */
        getAuthHeaders: function() {
            const headers = {
                'Content-Type': 'application/json'
            };

            // Add JWT token if available
            const accessToken = this.getAccessToken();
            if (accessToken) {
                headers['Authorization'] = 'Bearer ' + accessToken;
            }

            // Always add CSRF token for session authentication fallback
            const csrfToken = ArenaDoviz.utils.getCSRFToken();
            if (csrfToken) {
                headers['X-CSRFToken'] = csrfToken;
            }

            return headers;
        },

        /**
         * Login with JWT
         */
        login: function(username, password) {
            return fetch(ArenaDoviz.config.apiBaseUrl + 'accounts/jwt/token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Login failed');
                }
                return response.json();
            })
            .then(data => {
                // Store tokens and user data
                this.setTokens(data.access, data.refresh);
                localStorage.setItem('arena_user_data', JSON.stringify(data.user));
                return data;
            });
        },

        /**
         * Logout with JWT
         */
        logout: function() {
            const refreshToken = this.getRefreshToken();

            // Blacklist refresh token on server
            if (refreshToken) {
                fetch(ArenaDoviz.config.apiBaseUrl + 'accounts/users/jwt_logout/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + this.getAccessToken(),
                        'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
                    },
                    body: JSON.stringify({
                        refresh_token: refreshToken
                    })
                }).catch(error => {
                    console.error('Error blacklisting token:', error);
                });
            }

            // Clear local tokens
            this.clearTokens();

            // Redirect to login page
            window.location.href = '/accounts/login/';
        },

        /**
         * Refresh JWT access token
         */
        refreshToken: function() {
            const refreshToken = this.getRefreshToken();
            if (!refreshToken) {
                // If no refresh token available, try to get JWT tokens for session user
                if (this.isSessionAuthenticated()) {
                    return this.getJWTTokensForSessionUser()
                        .then(data => data.access);
                }
                throw new Error('No refresh token available');
            }

            return fetch(ArenaDoviz.config.apiBaseUrl + 'accounts/jwt/token/refresh/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
                },
                body: JSON.stringify({
                    refresh: refreshToken
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Token refresh failed');
                }
                return response.json();
            })
            .then(data => {
                // Update tokens
                this.setTokens(data.access, data.refresh);
                return data.access;
            });
        },

        /**
         * Get JWT tokens for a user authenticated via Django session
         */
        getJWTTokensForSessionUser: function() {
            return fetch(ArenaDoviz.config.apiBaseUrl + 'accounts/users/get_jwt_tokens/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
                },
                credentials: 'include' // Include session cookies
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to get JWT tokens');
                }
                return response.json();
            })
            .then(data => {
                // Store tokens and user data
                this.setTokens(data.access, data.refresh);
                localStorage.setItem('arena_user_data', JSON.stringify(data.user));
                return data;
            });
        }
    },

    // API functions
    api: {
        /**
         * Make API request with JWT authentication
         */
        request: function(method, endpoint, data = null) {
            const url = ArenaDoviz.config.apiBaseUrl + endpoint;
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
                }
            };

            // Add JWT token if available
            const accessToken = ArenaDoviz.auth.getAccessToken();
            if (accessToken) {
                options.headers['Authorization'] = 'Bearer ' + accessToken;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            return fetch(url, options)
                .then(response => {
                    // Handle authentication errors
                    if (response.status === 401) {
                        // Check if response contains redirect information
                        return response.json().then(errorData => {
                            if (errorData.redirect_url) {
                                // Clear tokens and redirect
                                ArenaDoviz.auth.clearTokens();
                                window.location.href = errorData.redirect_url;
                                return Promise.reject(new Error('Authentication required'));
                            }

                            // Try to refresh token if we have one
                            if (accessToken) {
                                return ArenaDoviz.auth.refreshToken()
                                    .then(newToken => {
                                        // Retry request with new token
                                        options.headers['Authorization'] = 'Bearer ' + newToken;
                                        return fetch(url, options);
                                    })
                                    .catch(error => {
                                        // Refresh failed, redirect to login
                                        ArenaDoviz.auth.clearTokens();
                                        if (!window.location.pathname.includes('/accounts/login/')) {
                                            window.location.href = '/accounts/login/?next=' + encodeURIComponent(window.location.pathname);
                                        }
                                        throw new Error('Authentication failed');
                                    });
                            } else {
                                // No token, redirect to login
                                if (!window.location.pathname.includes('/accounts/login/')) {
                                    window.location.href = '/accounts/login/?next=' + encodeURIComponent(window.location.pathname);
                                }
                                throw new Error('Authentication required');
                            }
                        }).catch(jsonError => {
                            // If response is not JSON, handle as before
                            if (accessToken) {
                                return ArenaDoviz.auth.refreshToken()
                                    .then(newToken => {
                                        options.headers['Authorization'] = 'Bearer ' + newToken;
                                        return fetch(url, options);
                                    })
                                    .catch(error => {
                                        ArenaDoviz.auth.logout();
                                        throw new Error('Authentication failed');
                                    });
                            } else {
                                throw new Error('Authentication required');
                            }
                        });
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('API request failed:', error);
                    throw error;
                });
        },
        
        /**
         * Get customers
         */
        getCustomers: function(params = {}) {
            const queryString = new URLSearchParams(params).toString();
            const endpoint = `customers/${queryString ? '?' + queryString : ''}`;
            return this.request('GET', endpoint);
        },
        
        /**
         * Get transactions
         */
        getTransactions: function(params = {}) {
            const queryString = new URLSearchParams(params).toString();
            const endpoint = `transactions/${queryString ? '?' + queryString : ''}`;
            return this.request('GET', endpoint);
        },
        
        /**
         * Get exchange rates
         */
        getExchangeRates: function(location = null) {
            const endpoint = location ? `currencies/rates/?location=${location}` : 'currencies/rates/';
            return this.request('GET', endpoint);
        },
        
        /**
         * Get dashboard statistics
         */
        getDashboardStats: function() {
            return this.request('GET', 'core/dashboard-stats/');
        }
    },
    
    // Form handling
    forms: {
        /**
         * Initialize form validation
         */
        initValidation: function(formSelector) {
            $(formSelector).on('submit', function(e) {
                if (!ArenaDoviz.utils.validateForm(this)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        },
        
        /**
         * Initialize currency input formatting
         */
        initCurrencyInputs: function() {
            $('.currency-input').on('input', function() {
                let value = $(this).val().replace(/[^\d.]/g, '');
                if (value) {
                    const formatted = parseFloat(value).toLocaleString('en-US');
                    $(this).val(formatted);
                }
            });
        },
        
        /**
         * Initialize date pickers
         */
        initDatePickers: function() {
            $('.date-picker').each(function() {
                // Initialize date picker (would use a library like flatpickr)
                $(this).attr('type', 'date');
            });
        }
    },
    
    // Table handling
    tables: {
        /**
         * Initialize DataTables with Arena Doviz defaults
         */
        initDataTable: function(selector, options = {}) {
            // Check if DataTable already exists and destroy it
            if ($.fn.DataTable && $.fn.DataTable.isDataTable(selector)) {
                $(selector).DataTable().destroy();
                console.log('Existing DataTable destroyed for selector:', selector);
            }

            const defaultOptions = {
                responsive: true,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                order: [[0, 'desc']],
                processing: true,
                serverSide: false,
                stateSave: true,
                stateDuration: 60 * 60 * 24, // 24 hours
                language: {
                    search: 'Search:',
                    lengthMenu: 'Show _MENU_ entries',
                    info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                    infoEmpty: 'Showing 0 to 0 of 0 entries',
                    infoFiltered: '(filtered from _MAX_ total entries)',
                    loadingRecords: 'Loading...',
                    processing: 'Processing...',
                    zeroRecords: 'No matching records found',
                    paginate: {
                        first: 'First',
                        last: 'Last',
                        next: 'Next',
                        previous: 'Previous'
                    }
                },
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                columnDefs: [
                    {
                        targets: '_all',
                        className: 'text-nowrap'
                    }
                ]
            };

            const finalOptions = Object.assign(defaultOptions, options);

            if ($.fn.DataTable) {
                return $(selector).DataTable(finalOptions);
            } else {
                console.warn('DataTables library not loaded');
                return null;
            }
        },

        /**
         * Initialize server-side DataTable for large datasets
         */
        initServerSideTable: function(selector, ajaxUrl, columns, options = {}) {
            // Check if DataTable already exists and destroy it
            if ($.fn.DataTable && $.fn.DataTable.isDataTable(selector)) {
                $(selector).DataTable().destroy();
                console.log('Existing server-side DataTable destroyed for selector:', selector);
            }

            const defaultOptions = {
                responsive: true,
                processing: true,
                serverSide: true,
                ajax: {
                    url: ajaxUrl,
                    type: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
                    },
                    data: function(d) {
                        // Convert DataTables parameters to Django REST framework format
                        return {
                            page: Math.floor(d.start / d.length) + 1,
                            page_size: d.length,
                            search: d.search.value,
                            ordering: d.order.length > 0 ?
                                (d.order[0].dir === 'desc' ? '-' : '') + columns[d.order[0].column].data :
                                undefined
                        };
                    },
                    dataSrc: function(json) {
                        // Convert Django REST framework response to DataTables format
                        json.recordsTotal = json.count;
                        json.recordsFiltered = json.count;
                        return json.results;
                    },
                    error: function(xhr, error, code) {
                        console.error('DataTables AJAX error:', error);
                        ArenaDoviz.ui.showAlert('error', 'Failed to load data');
                    }
                },
                columns: columns,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                language: {
                    search: 'Search:',
                    lengthMenu: 'Show _MENU_ entries',
                    info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                    infoEmpty: 'Showing 0 to 0 of 0 entries',
                    infoFiltered: '(filtered from _MAX_ total entries)',
                    loadingRecords: 'Loading...',
                    processing: 'Processing...',
                    zeroRecords: 'No matching records found',
                    paginate: {
                        first: 'First',
                        last: 'Last',
                        next: 'Next',
                        previous: 'Previous'
                    }
                }
            };

            const finalOptions = Object.assign(defaultOptions, options);

            if ($.fn.DataTable) {
                return $(selector).DataTable(finalOptions);
            } else {
                console.warn('DataTables library not loaded');
                return null;
            }
        },

        /**
         * Refresh DataTable data
         */
        refreshTable: function(table) {
            if (table && table.ajax) {
                table.ajax.reload();
            } else if (table) {
                table.draw();
            }
        },

        /**
         * Export DataTable data
         */
        exportTable: function(table, format = 'excel', filename = 'export') {
            if (!table) return;

            // Get current table data
            const data = table.rows({ search: 'applied' }).data().toArray();
            const headers = table.columns().header().toArray().map(th => $(th).text());

            if (format === 'excel') {
                this.exportToExcel(data, headers, filename);
            } else if (format === 'csv') {
                this.exportToCSV(data, headers, filename);
            }
        },

        /**
         * Export data to Excel format
         */
        exportToExcel: function(data, headers, filename) {
            // This would require a library like SheetJS or server-side export
            console.log('Excel export not implemented yet');
            ArenaDoviz.ui.showAlert('info', 'Excel export feature coming soon');
        },

        /**
         * Export data to CSV format
         */
        exportToCSV: function(data, headers, filename) {
            let csv = headers.join(',') + '\n';

            data.forEach(row => {
                const rowData = Array.isArray(row) ? row : Object.values(row);
                csv += rowData.map(cell => `"${cell}"`).join(',') + '\n';
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `${filename}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    },
    
    // Notification handling
    notifications: {
        /**
         * Show toast notification
         */
        showToast: function(message, type = 'info', duration = 5000) {
            const toastId = 'toast-' + Date.now();
            const iconClass = {
                'success': 'bi-check-circle',
                'error': 'bi-exclamation-triangle',
                'warning': 'bi-exclamation-triangle',
                'info': 'bi-info-circle'
            }[type] || 'bi-info-circle';
            
            const toast = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="bi ${iconClass}"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            
            // Add toast container if it doesn't exist
            if (!$('.toast-container').length) {
                $('body').append('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
            }
            
            $('.toast-container').append(toast);
            
            const toastElement = new bootstrap.Toast(document.getElementById(toastId), {
                delay: duration
            });
            toastElement.show();
            
            // Remove toast element after it's hidden
            $(`#${toastId}`).on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    },
    
    // Real-time updates
    realtime: {
        /**
         * Start real-time updates
         */
        start: function() {
            // This would typically use WebSockets or Server-Sent Events
            // For now, we'll use polling
            setInterval(() => {
                this.updateDashboard();
            }, ArenaDoviz.config.refreshInterval);
        },
        
        /**
         * Update dashboard data
         */
        updateDashboard: function() {
            if (window.location.pathname === '/' || window.location.pathname === '/dashboard/') {
                ArenaDoviz.api.getDashboardStats()
                    .then(data => {
                        // Update dashboard elements
                        $('#total-customers').text(data.total_customers || '-');
                        $('#today-transactions').text(data.today_transactions || '-');
                        $('#active-locations').text(data.active_locations || '-');
                        $('#pending-approvals').text(data.pending_approvals || '-');
                    })
                    .catch(error => {
                        console.error('Failed to update dashboard:', error);
                    });
            }
        }
    }
};

// Initialize when document is ready
$(document).ready(function() {
    try {
        // Initialize form validation for all forms
        if (ArenaDoviz.forms && typeof ArenaDoviz.forms.initValidation === 'function') {
            ArenaDoviz.forms.initValidation('form');
        }

        // Initialize currency inputs
        if (ArenaDoviz.forms && typeof ArenaDoviz.forms.initCurrencyInputs === 'function') {
            ArenaDoviz.forms.initCurrencyInputs();
        }

        // Initialize date pickers
        if (ArenaDoviz.forms && typeof ArenaDoviz.forms.initDatePickers === 'function') {
            ArenaDoviz.forms.initDatePickers();
        }

        // Start real-time updates
        if (ArenaDoviz.realtime && typeof ArenaDoviz.realtime.start === 'function') {
            ArenaDoviz.realtime.start();
        }

        // Initialize tooltips
        if ($.fn.tooltip) {
            try {
                $('[data-bs-toggle="tooltip"]').tooltip();
            } catch (e) {
                console.warn('Failed to initialize tooltips:', e);
            }
        }

        // Initialize popovers
        if ($.fn.popover) {
            try {
                $('[data-bs-toggle="popover"]').popover();
            } catch (e) {
                console.warn('Failed to initialize popovers:', e);
            }
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            try {
                $('.alert:not(.alert-permanent)').fadeOut();
            } catch (e) {
                console.warn('Failed to auto-hide alerts:', e);
            }
        }, 5000);

        console.log('Arena Doviz JavaScript initialized');
    } catch (error) {
        console.error('Error initializing Arena Doviz JavaScript:', error);
    }
});

// Export for global use
window.ArenaDoviz = ArenaDoviz;
