"""
Arena Doviz System Integration Monitoring
"""
import asyncio
import json
import logging
import psutil
import requests
import subprocess
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from threading import Thread
import queue
import re

from config import TestConfig, LOGS_DIR, ERROR_PATTERNS, JS_ERROR_PATTERNS

logger = logging.getLogger("arena_monitor")

@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    network_io_bytes: Dict[str, int]
    process_count: int
    django_process_memory_mb: float = 0
    database_connections: int = 0

@dataclass
class LogEntry:
    """Log entry structure"""
    timestamp: datetime
    level: str
    source: str  # 'django', 'javascript', 'api', 'database'
    message: str
    context: Dict[str, Any]
    error_type: Optional[str] = None
    stack_trace: Optional[str] = None

@dataclass
class APICall:
    """API call monitoring data"""
    timestamp: datetime
    method: str
    url: str
    status_code: int
    response_time_ms: float
    request_size_bytes: int
    response_size_bytes: int
    headers: Dict[str, str]
    request_body: Optional[str] = None
    response_body: Optional[str] = None
    error_message: Optional[str] = None

class SystemMonitor:
    """System monitoring and logging integration"""
    
    def __init__(self):
        self.is_monitoring = False
        self.metrics_history: List[SystemMetrics] = []
        self.log_entries: List[LogEntry] = []
        self.api_calls: List[APICall] = []
        self.error_count = 0
        self.warning_count = 0
        self.monitoring_thread: Optional[Thread] = None
        self.log_queue = queue.Queue()
        
        # Django log file paths
        self.django_log_path = LOGS_DIR / "django.log"
        self.api_log_path = LOGS_DIR / "api.log"
        self.db_log_path = LOGS_DIR / "database.log"
        
        # Create log files if they don't exist
        for log_path in [self.django_log_path, self.api_log_path, self.db_log_path]:
            log_path.parent.mkdir(parents=True, exist_ok=True)
            log_path.touch(exist_ok=True)
    
    def start_monitoring(self) -> None:
        """Start system monitoring"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_thread = Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info("📊 System monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop system monitoring"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        logger.info("📊 System monitoring stopped")
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Collect system metrics
                metrics = self._collect_system_metrics()
                self.metrics_history.append(metrics)
                
                # Keep only last 1000 metrics (about 16 minutes at 1-second intervals)
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-1000:]
                
                # Process log queue
                self._process_log_queue()
                
                # Sleep for 1 second
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
                time.sleep(5)  # Wait longer on error
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """Collect current system metrics"""
        try:
            # Basic system metrics
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            # Process information
            process_count = len(psutil.pids())
            django_memory = 0
            
            # Find Django process memory usage
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'manage.py' in cmdline and 'runserver' in cmdline:
                        django_memory = proc.info['memory_info'].rss / 1024 / 1024  # MB
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                disk_usage_percent=disk.percent,
                network_io_bytes={
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv
                },
                process_count=process_count,
                django_process_memory_mb=django_memory
            )
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=0,
                memory_percent=0,
                memory_used_mb=0,
                disk_usage_percent=0,
                network_io_bytes={'bytes_sent': 0, 'bytes_recv': 0},
                process_count=0
            )
    
    def _process_log_queue(self) -> None:
        """Process queued log entries"""
        while not self.log_queue.empty():
            try:
                log_entry = self.log_queue.get_nowait()
                self.log_entries.append(log_entry)
                
                # Count errors and warnings
                if log_entry.level in ['ERROR', 'CRITICAL']:
                    self.error_count += 1
                elif log_entry.level == 'WARNING':
                    self.warning_count += 1
                
            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"Error processing log entry: {str(e)}")
    
    def log_django_message(self, level: str, message: str, context: Dict[str, Any] = None) -> None:
        """Log a Django message"""
        log_entry = LogEntry(
            timestamp=datetime.now(),
            level=level,
            source='django',
            message=message,
            context=context or {},
            error_type=self._detect_error_type(message)
        )
        self.log_queue.put(log_entry)
    
    def log_api_call(self, method: str, url: str, status_code: int, response_time_ms: float,
                     headers: Dict[str, str] = None, request_body: str = None, 
                     response_body: str = None, error_message: str = None) -> None:
        """Log an API call"""
        api_call = APICall(
            timestamp=datetime.now(),
            method=method,
            url=url,
            status_code=status_code,
            response_time_ms=response_time_ms,
            request_size_bytes=len(request_body.encode()) if request_body else 0,
            response_size_bytes=len(response_body.encode()) if response_body else 0,
            headers=headers or {},
            request_body=request_body,
            response_body=response_body,
            error_message=error_message
        )
        self.api_calls.append(api_call)
        
        # Also create a log entry
        log_level = 'ERROR' if status_code >= 400 else 'INFO'
        log_message = f"{method} {url} - {status_code} ({response_time_ms}ms)"
        
        self.log_django_message(log_level, log_message, {
            'api_call': True,
            'method': method,
            'url': url,
            'status_code': status_code,
            'response_time_ms': response_time_ms
        })
    
    def log_javascript_error(self, error_message: str, source_url: str = None, 
                           line_number: int = None, stack_trace: str = None) -> None:
        """Log a JavaScript error"""
        log_entry = LogEntry(
            timestamp=datetime.now(),
            level='ERROR',
            source='javascript',
            message=error_message,
            context={
                'source_url': source_url,
                'line_number': line_number
            },
            error_type='JavaScript Error',
            stack_trace=stack_trace
        )
        self.log_queue.put(log_entry)
    
    def _detect_error_type(self, message: str) -> Optional[str]:
        """Detect error type from message"""
        for pattern in ERROR_PATTERNS:
            if re.search(pattern, message, re.IGNORECASE):
                return pattern
        return None
    
    def get_performance_summary(self, duration_minutes: int = 10) -> Dict[str, Any]:
        """Get performance summary for the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        if not recent_metrics:
            return {}
        
        # Calculate averages
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        max_cpu = max(m.cpu_percent for m in recent_metrics)
        max_memory = max(m.memory_percent for m in recent_metrics)
        
        # API performance
        recent_api_calls = [a for a in self.api_calls if a.timestamp >= cutoff_time]
        avg_response_time = sum(a.response_time_ms for a in recent_api_calls) / len(recent_api_calls) if recent_api_calls else 0
        api_error_rate = len([a for a in recent_api_calls if a.status_code >= 400]) / len(recent_api_calls) * 100 if recent_api_calls else 0
        
        # Error counts
        recent_errors = len([l for l in self.log_entries if l.timestamp >= cutoff_time and l.level in ['ERROR', 'CRITICAL']])
        recent_warnings = len([l for l in self.log_entries if l.timestamp >= cutoff_time and l.level == 'WARNING'])
        
        return {
            'duration_minutes': duration_minutes,
            'metrics_count': len(recent_metrics),
            'cpu_usage': {
                'average': round(avg_cpu, 2),
                'maximum': round(max_cpu, 2)
            },
            'memory_usage': {
                'average': round(avg_memory, 2),
                'maximum': round(max_memory, 2)
            },
            'api_performance': {
                'total_calls': len(recent_api_calls),
                'average_response_time_ms': round(avg_response_time, 2),
                'error_rate_percent': round(api_error_rate, 2)
            },
            'error_summary': {
                'errors': recent_errors,
                'warnings': recent_warnings,
                'total_issues': recent_errors + recent_warnings
            }
        }
    
    def get_error_analysis(self) -> Dict[str, Any]:
        """Analyze errors and provide insights"""
        error_logs = [l for l in self.log_entries if l.level in ['ERROR', 'CRITICAL']]
        
        # Group errors by type
        error_types = {}
        for log in error_logs:
            error_type = log.error_type or 'Unknown'
            if error_type not in error_types:
                error_types[error_type] = []
            error_types[error_type].append(log)
        
        # API error analysis
        api_errors = [a for a in self.api_calls if a.status_code >= 400]
        api_error_summary = {}
        for api_call in api_errors:
            key = f"{api_call.method} {api_call.url}"
            if key not in api_error_summary:
                api_error_summary[key] = []
            api_error_summary[key].append(api_call)
        
        return {
            'total_errors': len(error_logs),
            'error_types': {k: len(v) for k, v in error_types.items()},
            'api_errors': {
                'total': len(api_errors),
                'by_endpoint': {k: len(v) for k, v in api_error_summary.items()}
            },
            'recent_errors': [asdict(log) for log in error_logs[-10:]]  # Last 10 errors
        }
    
    def export_monitoring_data(self, filepath: Path) -> None:
        """Export all monitoring data to JSON file"""
        data = {
            'export_timestamp': datetime.now().isoformat(),
            'monitoring_duration_minutes': (datetime.now() - self.metrics_history[0].timestamp).total_seconds() / 60 if self.metrics_history else 0,
            'system_metrics': [asdict(m) for m in self.metrics_history],
            'log_entries': [asdict(l) for l in self.log_entries],
            'api_calls': [asdict(a) for a in self.api_calls],
            'performance_summary': self.get_performance_summary(),
            'error_analysis': self.get_error_analysis()
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        logger.info(f"📊 Monitoring data exported to {filepath}")

class DjangoLogMonitor:
    """Monitor Django application logs in real-time"""
    
    def __init__(self, system_monitor: SystemMonitor):
        self.system_monitor = system_monitor
        self.log_files = {
            'django': '/var/log/arena_doviz/django.log',
            'api': '/var/log/arena_doviz/api.log',
            'database': '/var/log/arena_doviz/database.log'
        }
        self.monitoring_processes = {}
    
    def start_log_monitoring(self) -> None:
        """Start monitoring Django log files"""
        for log_type, log_path in self.log_files.items():
            if Path(log_path).exists():
                self._start_tail_process(log_type, log_path)
        
        logger.info("📋 Django log monitoring started")
    
    def stop_log_monitoring(self) -> None:
        """Stop monitoring Django log files"""
        for process in self.monitoring_processes.values():
            process.terminate()
        self.monitoring_processes.clear()
        
        logger.info("📋 Django log monitoring stopped")
    
    def _start_tail_process(self, log_type: str, log_path: str) -> None:
        """Start a tail process for a log file"""
        try:
            process = subprocess.Popen(
                ['tail', '-f', log_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            self.monitoring_processes[log_type] = process
            
            # Start thread to read output
            thread = Thread(
                target=self._read_log_output,
                args=(log_type, process.stdout),
                daemon=True
            )
            thread.start()
            
        except Exception as e:
            logger.error(f"Failed to start monitoring {log_type} log: {str(e)}")
    
    def _read_log_output(self, log_type: str, stdout) -> None:
        """Read log output and process it"""
        for line in stdout:
            try:
                self._process_log_line(log_type, line.strip())
            except Exception as e:
                logger.error(f"Error processing log line: {str(e)}")
    
    def _process_log_line(self, log_type: str, line: str) -> None:
        """Process a single log line"""
        if not line:
            return
        
        # Parse log level
        level = 'INFO'
        for log_level in ['CRITICAL', 'ERROR', 'WARNING', 'INFO', 'DEBUG']:
            if log_level in line:
                level = log_level
                break
        
        # Log to system monitor
        self.system_monitor.log_django_message(
            level=level,
            message=line,
            context={'log_type': log_type}
        )
