{% extends 'base.html' %}
{% load i18n static %}

{% block title %}{{ page_title }} - Arena Doviz{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/transactions.css' %}">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-{{ icon_class|default:'plus-circle' }}"></i>
                {{ page_title }}
            </h1>
            {% if back_url %}
                <a href="{{ back_url }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {% trans "Back to List" %}
                </a>
            {% elif transaction_type_code %}
                <a href="{% url 'transactions_web:type_list' transaction_type_code %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {% trans "Back to List" %}
                </a>
            {% else %}
                <a href="{% url 'transactions_web:navigation' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {% trans "Back to Transactions" %}
                </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-form"></i>
                    {{ form_title|default:page_title }}
                </h5>
            </div>
            <div class="card-body">
                <form id="transaction-form" data-transaction-type="{{ transaction_type_code }}">
                    {% csrf_token %}
                    
                    <!-- Hidden transaction type field -->
                    <input type="hidden" id="transaction_type_code" name="transaction_type_code" value="{{ transaction_type_code }}">
                    
                    <!-- Basic Information Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">{% trans "Basic Information" %}</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="customer" class="form-label">{% trans "Customer" %} <span class="text-danger">*</span></label>
                                        <select class="form-select" id="customer" name="customer" required>
                                            <option value="">{% trans "Select customer..." %}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="location" class="form-label">{% trans "Location" %} <span class="text-danger">*</span></label>
                                        <select class="form-select" id="location" name="location" required>
                                            <option value="">{% trans "Select location..." %}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="reference_number" class="form-label">{% trans "Reference Number" %}</label>
                                        <input type="text" class="form-control" id="reference_number" name="reference_number" placeholder="{% trans 'External reference number...' %}">
                                    </div>
                                </div>
                                {% block additional_basic_fields %}{% endblock %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">{% trans "Description" %}</label>
                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="{% trans 'Transaction description...' %}"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    {% block transaction_specific_fields %}{% endblock %}
                    
                    <!-- Document Upload Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-file-earmark-arrow-up"></i>
                                {% trans "Document Upload" %}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="document_files" class="form-label">{% trans "Upload Documents" %}</label>
                                <input type="file" class="form-control" id="document_files" name="document_files" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx">
                                <div class="form-text">
                                    {% trans "Supported formats: PDF, JPG, PNG, DOC, DOCX, XLS, XLSX. Max 10MB per file." %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="document_type" class="form-label">{% trans "Document Type" %}</label>
                                <select class="form-select" id="document_type" name="document_type">
                                    <option value="receipt">{% trans "Receipt" %}</option>
                                    <option value="invoice">{% trans "Invoice" %}</option>
                                    <option value="bank_slip">{% trans "Bank Slip" %}</option>
                                    <option value="customer_id">{% trans "Customer ID Copy" %}</option>
                                    <option value="contract">{% trans "Contract" %}</option>
                                    <option value="other" selected>{% trans "Other" %}</option>
                                </select>
                            </div>

                            <div id="uploaded-files-preview" class="mt-3" style="display: none;">
                                <h6>{% trans "Selected Files:" %}</h6>
                                <div id="file-list" class="list-group">
                                    <!-- File list will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "Additional Notes" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="{% trans 'Additional notes about this transaction...' %}"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                            <i class="bi bi-x-circle"></i>
                            {% trans "Cancel" %}
                        </button>
                        <div>
                            <button type="submit" class="btn btn-primary me-2" data-action="save">
                                <i class="bi bi-save"></i>
                                {% trans "Save as Draft" %}
                            </button>
                            <button type="submit" class="btn btn-success" data-action="submit">
                                <i class="bi bi-send"></i>
                                {% trans "Submit for Approval" %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Transaction Preview Sidebar -->
    <div class="col-lg-4">
        <div class="card sticky-top">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-eye"></i>
                    {% trans "Transaction Preview" %}
                </h6>
            </div>
            <div class="card-body">
                <div id="transaction-preview">
                    <div class="text-muted text-center py-4">
                        <i class="bi bi-info-circle"></i>
                        {% trans "Fill in the form to see transaction preview" %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Customer Balance Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-wallet2"></i>
                    {% trans "Customer Balance" %}
                </h6>
            </div>
            <div class="card-body">
                <div id="customer-balance">
                    <div class="text-muted text-center py-3">
                        <i class="bi bi-person"></i>
                        {% trans "Select customer to view balance" %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% url 'javascript-catalog' %}"></script>
<script src="/static/js/transactions/common.js"></script>
{% block transaction_specific_js %}{% endblock %}
{% endblock %}
