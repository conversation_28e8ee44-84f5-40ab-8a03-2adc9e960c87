"""
Arena Doviz Complete Production Monitoring and Alerting System
"""
import os
import sys
import time
import json
import logging
import psutil
import smtplib
import requests
import subprocess
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from django.conf import settings
from django.core.management.base import BaseCommand
from django.db import connection
from django.core.cache import cache
from django.contrib.auth import get_user_model
from apps.transactions.models import Transaction, TransactionType
from apps.customers.models import Customer
from apps.currencies.models import Currency, ExchangeRate
from apps.locations.models import Location

logger = logging.getLogger(__name__)
User = get_user_model()

class SystemMonitor:
    """System monitoring and alerting"""
    
    def __init__(self):
        self.alerts = []
        self.metrics = {}
        
    def check_system_health(self):
        """Check overall system health"""
        print("🔍 Checking System Health...")
        
        # Check database connectivity
        self.check_database()
        
        # Check system resources
        self.check_system_resources()
        
        # Check application metrics
        self.check_application_metrics()
        
        # Check security
        self.check_security_metrics()
        
        return self.generate_health_report()
    
    def check_database(self):
        """Check database connectivity and performance"""
        try:
            start_time = time.time()
            
            # Test database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            
            db_response_time = (time.time() - start_time) * 1000
            self.metrics['db_response_time'] = db_response_time
            
            if db_response_time > 1000:  # 1 second
                self.alerts.append({
                    'level': 'WARNING',
                    'message': f'Database response time high: {db_response_time:.2f}ms'
                })
            
            # Check database size
            if hasattr(connection, 'vendor') and connection.vendor == 'postgresql':
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT pg_size_pretty(pg_database_size(current_database()))
                    """)
                    db_size = cursor.fetchone()[0]
                    self.metrics['db_size'] = db_size
            
            print(f"✅ Database: Connected ({db_response_time:.2f}ms)")
            
        except Exception as e:
            self.alerts.append({
                'level': 'CRITICAL',
                'message': f'Database connection failed: {str(e)}'
            })
            print(f"❌ Database: Connection failed - {str(e)}")
    
    def check_system_resources(self):
        """Check system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics['cpu_usage'] = cpu_percent
            
            if cpu_percent > 80:
                self.alerts.append({
                    'level': 'WARNING',
                    'message': f'High CPU usage: {cpu_percent}%'
                })
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.metrics['memory_usage'] = memory_percent
            
            if memory_percent > 85:
                self.alerts.append({
                    'level': 'WARNING',
                    'message': f'High memory usage: {memory_percent}%'
                })
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.metrics['disk_usage'] = disk_percent
            
            if disk_percent > 90:
                self.alerts.append({
                    'level': 'CRITICAL',
                    'message': f'High disk usage: {disk_percent:.1f}%'
                })
            
            print(f"✅ Resources: CPU {cpu_percent}%, Memory {memory_percent}%, Disk {disk_percent:.1f}%")
            
        except Exception as e:
            self.alerts.append({
                'level': 'ERROR',
                'message': f'Failed to check system resources: {str(e)}'
            })
            print(f"❌ Resources: Check failed - {str(e)}")
    
    def check_application_metrics(self):
        """Check application-specific metrics"""
        try:
            # Transaction metrics
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)
            
            today_transactions = Transaction.objects.filter(
                created_at__date=today,
                is_deleted=False
            ).count()
            
            yesterday_transactions = Transaction.objects.filter(
                created_at__date=yesterday,
                is_deleted=False
            ).count()
            
            self.metrics['transactions_today'] = today_transactions
            self.metrics['transactions_yesterday'] = yesterday_transactions
            
            # Check for unusual transaction volume
            if yesterday_transactions > 0:
                change_percent = ((today_transactions - yesterday_transactions) / yesterday_transactions) * 100
                if abs(change_percent) > 50:  # 50% change
                    self.alerts.append({
                        'level': 'INFO',
                        'message': f'Transaction volume changed by {change_percent:.1f}% from yesterday'
                    })
            
            # Check pending transactions
            pending_transactions = Transaction.objects.filter(
                status='pending',
                is_deleted=False
            ).count()
            
            self.metrics['pending_transactions'] = pending_transactions
            
            if pending_transactions > 10:
                self.alerts.append({
                    'level': 'WARNING',
                    'message': f'High number of pending transactions: {pending_transactions}'
                })
            
            # Customer metrics
            active_customers = Customer.objects.filter(is_active=True).count()
            self.metrics['active_customers'] = active_customers
            
            print(f"✅ Application: {today_transactions} transactions today, {pending_transactions} pending")
            
        except Exception as e:
            self.alerts.append({
                'level': 'ERROR',
                'message': f'Failed to check application metrics: {str(e)}'
            })
            print(f"❌ Application: Metrics check failed - {str(e)}")
    
    def check_security_metrics(self):
        """Check security-related metrics"""
        try:
            # Check recent failed login attempts
            yesterday = datetime.now() - timedelta(days=1)
            
            failed_logins = AuditLog.objects.filter(
                action='login_failed',
                timestamp__gte=yesterday
            ).count()
            
            self.metrics['failed_logins_24h'] = failed_logins
            
            if failed_logins > 20:
                self.alerts.append({
                    'level': 'WARNING',
                    'message': f'High number of failed login attempts: {failed_logins} in last 24h'
                })
            
            # Check for suspicious activity patterns
            suspicious_ips = AuditLog.objects.filter(
                action='login_failed',
                timestamp__gte=yesterday
            ).values('ip_address').annotate(
                count=models.Count('id')
            ).filter(count__gte=5)
            
            if suspicious_ips.exists():
                for ip_data in suspicious_ips:
                    self.alerts.append({
                        'level': 'WARNING',
                        'message': f'Suspicious activity from IP {ip_data["ip_address"]}: {ip_data["count"]} failed logins'
                    })
            
            print(f"✅ Security: {failed_logins} failed logins in 24h")
            
        except Exception as e:
            self.alerts.append({
                'level': 'ERROR',
                'message': f'Failed to check security metrics: {str(e)}'
            })
            print(f"❌ Security: Check failed - {str(e)}")
    
    def generate_health_report(self):
        """Generate comprehensive health report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'status': 'HEALTHY',
            'metrics': self.metrics,
            'alerts': self.alerts
        }
        
        # Determine overall status
        critical_alerts = [a for a in self.alerts if a['level'] == 'CRITICAL']
        warning_alerts = [a for a in self.alerts if a['level'] == 'WARNING']
        
        if critical_alerts:
            report['status'] = 'CRITICAL'
        elif warning_alerts:
            report['status'] = 'WARNING'
        
        return report
    
    def send_alert_email(self, report):
        """Send alert email if configured"""
        if not hasattr(settings, 'EMAIL_HOST') or not settings.EMAIL_HOST:
            return
        
        if report['status'] in ['CRITICAL', 'WARNING']:
            try:
                subject = f"Arena Doviz Alert: {report['status']} - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
                
                body = f"""
Arena Doviz System Alert

Status: {report['status']}
Time: {report['timestamp']}

System Metrics:
- CPU Usage: {self.metrics.get('cpu_usage', 'N/A')}%
- Memory Usage: {self.metrics.get('memory_usage', 'N/A')}%
- Disk Usage: {self.metrics.get('disk_usage', 'N/A')}%
- Database Response: {self.metrics.get('db_response_time', 'N/A')}ms

Application Metrics:
- Transactions Today: {self.metrics.get('transactions_today', 'N/A')}
- Pending Transactions: {self.metrics.get('pending_transactions', 'N/A')}
- Active Customers: {self.metrics.get('active_customers', 'N/A')}

Alerts:
"""
                
                for alert in self.alerts:
                    body += f"- [{alert['level']}] {alert['message']}\n"
                
                msg = MIMEText(body)
                msg['Subject'] = subject
                msg['From'] = settings.DEFAULT_FROM_EMAIL
                msg['To'] = settings.ADMINS[0][1] if settings.ADMINS else '<EMAIL>'
                
                with smtplib.SMTP(settings.EMAIL_HOST, settings.EMAIL_PORT) as server:
                    if settings.EMAIL_USE_TLS:
                        server.starttls()
                    if settings.EMAIL_HOST_USER:
                        server.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)
                    server.send_message(msg)
                
                print(f"📧 Alert email sent for {report['status']} status")
                
            except Exception as e:
                print(f"❌ Failed to send alert email: {str(e)}")

class ProductionMonitor:
    """Complete production monitoring system"""

    def __init__(self):
        self.alerts = []
        self.metrics = {}
        self.performance_data = {}
        self.security_events = []

    def run_complete_monitoring(self):
        """Run complete production monitoring"""
        print("🔍 Running Complete Production Monitoring...")

        # System health monitoring
        system_monitor = SystemMonitor()
        system_report = system_monitor.check_system_health()

        # Performance monitoring
        self.check_performance_metrics()

        # Security monitoring
        self.check_security_events()

        # Business metrics monitoring
        self.check_business_metrics()

        # External service monitoring
        self.check_external_services()

        # Generate comprehensive report
        return self.generate_comprehensive_report(system_report)

    def check_performance_metrics(self):
        """Check application performance metrics"""
        try:
            # Database performance
            with connection.cursor() as cursor:
                # Check slow queries
                cursor.execute("""
                    SELECT query, calls, total_exec_time, mean_exec_time
                    FROM pg_stat_statements
                    WHERE mean_exec_time > 1000
                    ORDER BY total_exec_time DESC
                    LIMIT 10
                """)
                slow_queries = cursor.fetchall()

                if slow_queries:
                    self.alerts.append({
                        'level': 'WARNING',
                        'category': 'Performance',
                        'message': f'Found {len(slow_queries)} slow queries'
                    })

                # Check database connections
                cursor.execute("SELECT count(*) FROM pg_stat_activity")
                active_connections = cursor.fetchone()[0]
                self.metrics['active_db_connections'] = active_connections

                if active_connections > 150:  # 75% of max_connections
                    self.alerts.append({
                        'level': 'WARNING',
                        'category': 'Performance',
                        'message': f'High database connections: {active_connections}'
                    })

            # Cache performance
            cache_stats = cache.get_stats() if hasattr(cache, 'get_stats') else {}
            self.metrics['cache_stats'] = cache_stats

            # Response time monitoring
            self.check_response_times()

        except Exception as e:
            self.alerts.append({
                'level': 'ERROR',
                'category': 'Performance',
                'message': f'Performance monitoring failed: {str(e)}'
            })

    def check_response_times(self):
        """Check application response times"""
        endpoints = [
            '/health/',
            '/api/v1/customers/customers/',
            '/api/v1/transactions/transactions/',
            '/api/v1/currencies/rates/current/?location=DXB'
        ]

        base_url = 'http://127.0.0.1:8000'
        response_times = {}

        for endpoint in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
                response_time = (time.time() - start_time) * 1000

                response_times[endpoint] = {
                    'response_time': response_time,
                    'status_code': response.status_code
                }

                if response_time > 2000:  # 2 seconds
                    self.alerts.append({
                        'level': 'WARNING',
                        'category': 'Performance',
                        'message': f'Slow response from {endpoint}: {response_time:.2f}ms'
                    })

            except Exception as e:
                response_times[endpoint] = {
                    'error': str(e),
                    'status_code': 0
                }
                self.alerts.append({
                    'level': 'ERROR',
                    'category': 'Performance',
                    'message': f'Failed to check {endpoint}: {str(e)}'
                })

        self.metrics['response_times'] = response_times

    def check_security_events(self):
        """Check for security events"""
        try:
            # Check for failed login attempts
            yesterday = datetime.now() - timedelta(days=1)

            # Check for suspicious user activity
            recent_users = User.objects.filter(
                last_login__gte=yesterday
            ).count()

            self.metrics['recent_logins'] = recent_users

            # Check for unusual transaction patterns
            recent_transactions = Transaction.objects.filter(
                created_at__gte=yesterday,
                is_deleted=False
            )

            large_transactions = recent_transactions.filter(
                from_amount__gte=100000  # Large transactions
            ).count()

            if large_transactions > 10:
                self.alerts.append({
                    'level': 'INFO',
                    'category': 'Security',
                    'message': f'High number of large transactions: {large_transactions}'
                })

            # Check for rapid transaction creation
            rapid_transactions = recent_transactions.filter(
                created_at__gte=datetime.now() - timedelta(hours=1)
            ).count()

            if rapid_transactions > 50:
                self.alerts.append({
                    'level': 'WARNING',
                    'category': 'Security',
                    'message': f'Rapid transaction creation: {rapid_transactions} in last hour'
                })

        except Exception as e:
            self.alerts.append({
                'level': 'ERROR',
                'category': 'Security',
                'message': f'Security monitoring failed: {str(e)}'
            })

    def check_business_metrics(self):
        """Check business-specific metrics"""
        try:
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)

            # Transaction volume metrics
            today_volume = Transaction.objects.filter(
                created_at__date=today,
                is_deleted=False
            ).count()

            yesterday_volume = Transaction.objects.filter(
                created_at__date=yesterday,
                is_deleted=False
            ).count()

            self.metrics['transaction_volume'] = {
                'today': today_volume,
                'yesterday': yesterday_volume,
                'change_percent': ((today_volume - yesterday_volume) / max(yesterday_volume, 1)) * 100
            }

            # Revenue metrics (commission)
            from django.db.models import Sum
            today_commission = Transaction.objects.filter(
                created_at__date=today,
                is_deleted=False,
                status='completed'
            ).aggregate(total=Sum('commission_amount'))['total'] or 0

            self.metrics['daily_commission'] = float(today_commission)

            # Exchange rate freshness
            stale_rates = ExchangeRate.objects.filter(
                updated_at__lt=datetime.now() - timedelta(hours=24),
                is_active=True
            ).count()

            if stale_rates > 0:
                self.alerts.append({
                    'level': 'WARNING',
                    'category': 'Business',
                    'message': f'Stale exchange rates: {stale_rates} rates older than 24 hours'
                })

            # Customer activity
            active_customers = Customer.objects.filter(
                is_active=True,
                transactions__created_at__gte=datetime.now() - timedelta(days=30)
            ).distinct().count()

            self.metrics['active_customers_30d'] = active_customers

        except Exception as e:
            self.alerts.append({
                'level': 'ERROR',
                'category': 'Business',
                'message': f'Business metrics monitoring failed: {str(e)}'
            })

    def check_external_services(self):
        """Check external service dependencies"""
        services = {
            'PostgreSQL': self.check_postgresql,
            'Redis': self.check_redis,
            'Email': self.check_email_service,
            'Backup': self.check_backup_status
        }

        service_status = {}

        for service_name, check_func in services.items():
            try:
                status = check_func()
                service_status[service_name] = status

                if not status.get('healthy', False):
                    self.alerts.append({
                        'level': 'ERROR',
                        'category': 'External Services',
                        'message': f'{service_name} service unhealthy: {status.get("error", "Unknown error")}'
                    })

            except Exception as e:
                service_status[service_name] = {
                    'healthy': False,
                    'error': str(e)
                }
                self.alerts.append({
                    'level': 'ERROR',
                    'category': 'External Services',
                    'message': f'Failed to check {service_name}: {str(e)}'
                })

        self.metrics['external_services'] = service_status

    def check_postgresql(self):
        """Check PostgreSQL service"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            return {'healthy': True, 'status': 'Connected'}
        except Exception as e:
            return {'healthy': False, 'error': str(e)}

    def check_redis(self):
        """Check Redis service"""
        try:
            from django.core.cache import cache
            cache.set('health_check', 'ok', 10)
            result = cache.get('health_check')
            if result == 'ok':
                return {'healthy': True, 'status': 'Connected'}
            else:
                return {'healthy': False, 'error': 'Cache test failed'}
        except Exception as e:
            return {'healthy': False, 'error': str(e)}

    def check_email_service(self):
        """Check email service"""
        try:
            from django.core.mail import get_connection
            connection = get_connection()
            connection.open()
            connection.close()
            return {'healthy': True, 'status': 'Connected'}
        except Exception as e:
            return {'healthy': False, 'error': str(e)}

    def check_backup_status(self):
        """Check backup status"""
        try:
            backup_dir = os.environ.get('BACKUP_DIR', '/var/backups/arena-doviz')
            status_file = os.path.join(backup_dir, 'backup_status.json')

            if os.path.exists(status_file):
                with open(status_file, 'r') as f:
                    status = json.load(f)

                last_backup = datetime.fromisoformat(status.get('last_backup', '1970-01-01T00:00:00'))
                hours_since_backup = (datetime.now() - last_backup).total_seconds() / 3600

                if hours_since_backup > 25:  # More than 25 hours
                    return {'healthy': False, 'error': f'Last backup was {hours_since_backup:.1f} hours ago'}
                else:
                    return {'healthy': True, 'status': f'Last backup: {hours_since_backup:.1f} hours ago'}
            else:
                return {'healthy': False, 'error': 'Backup status file not found'}

        except Exception as e:
            return {'healthy': False, 'error': str(e)}

    def generate_comprehensive_report(self, system_report):
        """Generate comprehensive monitoring report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_health': system_report,
            'performance_metrics': self.metrics.get('response_times', {}),
            'business_metrics': {
                'transaction_volume': self.metrics.get('transaction_volume', {}),
                'daily_commission': self.metrics.get('daily_commission', 0),
                'active_customers_30d': self.metrics.get('active_customers_30d', 0)
            },
            'external_services': self.metrics.get('external_services', {}),
            'alerts': self.alerts,
            'overall_status': self.determine_overall_status()
        }

        # Save report to cache and file
        cache.set('production_monitoring_report', report, 300)

        # Save to file
        report_dir = '/var/log/arena-doviz'
        os.makedirs(report_dir, exist_ok=True)

        report_file = os.path.join(report_dir, f"monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        return report

    def determine_overall_status(self):
        """Determine overall system status"""
        critical_alerts = [a for a in self.alerts if a['level'] == 'ERROR']
        warning_alerts = [a for a in self.alerts if a['level'] == 'WARNING']

        if critical_alerts:
            return 'CRITICAL'
        elif len(warning_alerts) > 5:
            return 'DEGRADED'
        elif warning_alerts:
            return 'WARNING'
        else:
            return 'HEALTHY'

def run_health_check():
    """Run system health check"""
    monitor = SystemMonitor()
    report = monitor.check_system_health()

    # Send alerts if needed
    monitor.send_alert_email(report)

    # Cache the report for dashboard
    cache.set('system_health_report', report, 300)  # 5 minutes

    return report

def run_production_monitoring():
    """Run complete production monitoring"""
    monitor = ProductionMonitor()
    report = monitor.run_complete_monitoring()

    # Send alerts if critical
    if report['overall_status'] in ['CRITICAL', 'DEGRADED']:
        send_production_alert(report)

    return report

def send_production_alert(report):
    """Send production alert email"""
    if not hasattr(settings, 'EMAIL_HOST') or not settings.EMAIL_HOST:
        return

    try:
        subject = f"Arena Doviz Production Alert: {report['overall_status']} - {datetime.now().strftime('%Y-%m-%d %H:%M')}"

        body = f"""
Arena Doviz Production System Alert

Status: {report['overall_status']}
Time: {report['timestamp']}

Critical Alerts:
"""

        critical_alerts = [a for a in report['alerts'] if a['level'] == 'ERROR']
        for alert in critical_alerts:
            body += f"- [{alert['category']}] {alert['message']}\n"

        body += f"""

Warning Alerts:
"""

        warning_alerts = [a for a in report['alerts'] if a['level'] == 'WARNING']
        for alert in warning_alerts[:10]:  # Limit to 10 warnings
            body += f"- [{alert['category']}] {alert['message']}\n"

        body += f"""

Business Metrics:
- Transaction Volume Today: {report['business_metrics']['transaction_volume'].get('today', 'N/A')}
- Daily Commission: ${report['business_metrics']['daily_commission']:.2f}
- Active Customers (30d): {report['business_metrics']['active_customers_30d']}

External Services:
"""

        for service, status in report['external_services'].items():
            status_text = "✅ Healthy" if status.get('healthy') else f"❌ {status.get('error', 'Unknown error')}"
            body += f"- {service}: {status_text}\n"

        msg = MIMEText(body)
        msg['Subject'] = subject
        msg['From'] = settings.DEFAULT_FROM_EMAIL
        msg['To'] = settings.ADMINS[0][1] if settings.ADMINS else '<EMAIL>'

        with smtplib.SMTP(settings.EMAIL_HOST, settings.EMAIL_PORT) as server:
            if settings.EMAIL_USE_TLS:
                server.starttls()
            if settings.EMAIL_HOST_USER:
                server.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)
            server.send_message(msg)

        print(f"📧 Production alert email sent for {report['overall_status']} status")

    except Exception as e:
        print(f"❌ Failed to send production alert email: {str(e)}")
