"""
Comprehensive tests for Arena Doviz transaction system fixes.
Tests all critical transaction issues found during manual testing.
"""

import json
import os
from decimal import Decimal
from django.test import TestCase, TransactionTestCase, override_settings
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from apps.transactions.models import Transaction, TransactionType, BalanceEntry
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency
from apps.accounts.models import User

User = get_user_model()

# Set encryption key for tests (proper base64 encoded)
import base64
test_key = base64.urlsafe_b64encode(b'test-key-for-testing-only-32-char').decode()
os.environ['ARENA_ENCRYPTION_KEY'] = test_key


class TransactionSystemFixesTestCase(APITestCase):
    """Test case for transaction system fixes."""
    
    def setUp(self):
        """Set up test data."""
        # Create test user with all permissions
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            is_staff=True,
            is_superuser=True,
            role=User.Role.ADMIN
        )
        
        # Create test location
        self.location = Location.objects.create(
            name='Test Location',
            address='Test Address',
            city='Test City',
            country='Test Country',
            is_active=True
        )
        
        # Set user location
        self.user.location = self.location
        self.user.save()
        
        # Create test currencies
        self.usd = Currency.objects.create(
            code='USD',
            name='US Dollar',
            symbol='$',
            is_active=True
        )
        
        self.irr = Currency.objects.create(
            code='IRR',
            name='Iranian Rial',
            symbol='﷼',
            is_active=True
        )
        
        # Create test customer
        self.customer = Customer.objects.create(
            first_name='Amirhossein',
            last_name='Daadbin',
            email='<EMAIL>',
            phone_number='+1234567890',
            preferred_location=self.location,
            status=Customer.Status.ACTIVE
        )

        # Create another test customer with existing balance
        self.customer_with_balance = Customer.objects.create(
            first_name='مریم',
            last_name='کریمی',
            email='<EMAIL>',
            phone_number='+9876543210',
            preferred_location=self.location,
            status=Customer.Status.ACTIVE
        )
        
        # Create transaction types
        self.adjustment_type = TransactionType.objects.create(
            name='Balance Adjustment',
            code='ADJUSTMENT',
            is_exchange=False,
            is_active=True
        )
        
        self.deposit_type = TransactionType.objects.create(
            name='Cash Deposit',
            code='DEPOSIT',
            is_exchange=False,
            is_active=True
        )
        
        self.withdrawal_type = TransactionType.objects.create(
            name='Cash Withdrawal',
            code='WITHDRAWAL',
            is_exchange=False,
            is_active=True
        )
        
        self.exchange_type = TransactionType.objects.create(
            name='Currency Exchange',
            code='EXCHANGE',
            is_exchange=True,
            is_active=True
        )
        
        # Authenticate client
        self.client.force_authenticate(user=self.user)
        
        # Create existing balance entries for customer_with_balance
        self._create_existing_balance()
    
    def _create_existing_balance(self):
        """Create existing balance entries for testing balance calculations."""
        # Create a dummy transaction for the balance entries
        dummy_transaction = Transaction.objects.create(
            transaction_type=self.adjustment_type,
            customer=self.customer_with_balance,
            location=self.location,
            from_currency=self.irr,
            to_currency=self.irr,
            from_amount=Decimal('3975.33'),
            to_amount=Decimal('3975.33'),
            exchange_rate=Decimal('1.0'),
            description='Initial balance setup',
            status=Transaction.Status.COMPLETED
        )

        # Create IRR balance entry
        irr_entry = BalanceEntry.objects.create(
            transaction=dummy_transaction,
            customer=self.customer_with_balance,
            location=self.location,
            currency=self.irr,
            amount=Decimal('-3975.33'),
            entry_type=BalanceEntry.EntryType.DEBIT,
            description='Initial balance'
        )

        # Create another dummy transaction for USD balance
        dummy_transaction_usd = Transaction.objects.create(
            transaction_type=self.adjustment_type,
            customer=self.customer_with_balance,
            location=self.location,
            from_currency=self.usd,
            to_currency=self.usd,
            from_amount=Decimal('0.10'),
            to_amount=Decimal('0.10'),
            exchange_rate=Decimal('1.0'),
            description='Initial USD balance setup',
            status=Transaction.Status.COMPLETED
        )

        # Create USD balance entry
        usd_entry = BalanceEntry.objects.create(
            transaction=dummy_transaction_usd,
            customer=self.customer_with_balance,
            location=self.location,
            currency=self.usd,
            amount=Decimal('0.10'),
            entry_type=BalanceEntry.EntryType.CREDIT,
            description='Initial balance'
        )

    def test_balance_adjustment_creation_and_approval(self):
        """Test Issue 1: Balance adjustment creation and approval workflow."""
        # Create balance adjustment transaction (mimicking frontend)
        adjustment_data = {
            'transaction_type': str(self.adjustment_type.id),
            'customer': str(self.customer.id),
            'location': str(self.location.id),
            'from_currency': str(self.usd.id),
            'to_currency': str(self.usd.id),
            'from_amount': '1000.00',
            'to_amount': '1000.00',
            'exchange_rate': '1.0',
            'description': 'Balance adjustment for testing',
            'status': 'pending',  # Frontend sets this for adjustments
            'delivery_method': 'internal'
        }

        # Create the transaction
        response = self.client.post('/api/v1/transactions/transactions/', adjustment_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        transaction_id = response.data['id']
        transaction = Transaction.objects.get(id=transaction_id)

        # Verify transaction is created with pending status
        self.assertEqual(transaction.status, Transaction.Status.PENDING)
        self.assertTrue(transaction.can_be_approved())

        # Test approval workflow
        approval_response = self.client.post(f'/api/v1/transactions/transactions/{transaction_id}/approve/')

        # This should NOT return HTTP 400
        self.assertNotEqual(approval_response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(approval_response.status_code, status.HTTP_200_OK)

        # Verify transaction is approved
        transaction.refresh_from_db()
        self.assertEqual(transaction.status, Transaction.Status.APPROVED)
        self.assertEqual(transaction.approved_by, self.user)
        self.assertIsNotNone(transaction.approved_at)

    def test_balance_calculation_consistency(self):
        """Test Issue 2: Balance calculation and display consistency."""
        # Get customer balance through API
        balance_response = self.client.get(f'/api/v1/customers/customers/{self.customer_with_balance.id}/balance/')
        self.assertEqual(balance_response.status_code, status.HTTP_200_OK)

        balance_data = balance_response.data

        # Find USD balance
        usd_balance = None
        for currency_code, balance_info in balance_data.items():
            if currency_code.startswith('USD'):
                usd_balance = balance_info['balance']
                break

        self.assertIsNotNone(usd_balance, "USD balance should be found")
        self.assertEqual(usd_balance, Decimal('0.10'), "Balance should match the created entry")

        # Test transfer validation with correct balance
        transfer_data = {
            'transaction_type': str(self.exchange_type.id),
            'customer': str(self.customer_with_balance.id),
            'location': str(self.location.id),
            'from_currency': str(self.usd.id),
            'to_currency': str(self.irr.id),
            'from_amount': '0.05',  # Less than available balance
            'to_amount': '2000.00',
            'exchange_rate': '40000.0',
            'description': 'Test transfer',
            'delivery_method': 'internal'
        }

        # This should succeed since 0.05 < 0.10
        transfer_response = self.client.post('/api/v1/transactions/transactions/', transfer_data)

        # Should not get "exceeds available balance" error
        if transfer_response.status_code != status.HTTP_201_CREATED:
            print(f"Transfer failed with: {transfer_response.data}")

        self.assertEqual(transfer_response.status_code, status.HTTP_201_CREATED)

    def test_cash_deposit_transaction_creation(self):
        """Test Issue 3: Cash deposit transaction failures."""
        # Create cash deposit transaction (mimicking frontend)
        deposit_data = {
            'transaction_type': str(self.deposit_type.id),
            'customer': str(self.customer.id),
            'location': str(self.location.id),
            'from_currency': str(self.usd.id),
            'to_currency': str(self.usd.id),
            'from_amount': '500.00',
            'to_amount': '500.00',
            'exchange_rate': '1.0',
            'description': 'Cash deposit test',
            'delivery_method': 'cash'
        }

        # Create the transaction
        response = self.client.post('/api/v1/transactions/transactions/', deposit_data)

        # Should not get HTTP 400 Bad Request
        if response.status_code != status.HTTP_201_CREATED:
            print(f"Cash deposit failed with: {response.data}")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        transaction_id = response.data['id']
        transaction = Transaction.objects.get(id=transaction_id)

        # Verify transaction details
        self.assertEqual(transaction.transaction_type, self.deposit_type)
        self.assertEqual(transaction.customer, self.customer)
        self.assertEqual(transaction.from_amount, Decimal('500.00'))

    def test_cash_withdrawal_transaction_creation(self):
        """Test Issue 3: Cash withdrawal transaction failures."""
        # First create a balance for the customer
        BalanceEntry.objects.create(
            customer=self.customer,
            location=self.location,
            currency=self.usd,
            amount=Decimal('1000.00'),
            entry_type=BalanceEntry.EntryType.CREDIT,
            description='Initial balance for withdrawal test'
        )

        # Create cash withdrawal transaction (mimicking frontend)
        withdrawal_data = {
            'transaction_type': str(self.withdrawal_type.id),
            'customer': str(self.customer.id),
            'location': str(self.location.id),
            'from_currency': str(self.usd.id),
            'to_currency': str(self.usd.id),
            'from_amount': '200.00',
            'to_amount': '200.00',
            'exchange_rate': '1.0',
            'description': 'Cash withdrawal test',
            'delivery_method': 'cash'
        }

        # Create the transaction
        response = self.client.post('/api/v1/transactions/transactions/', withdrawal_data)

        # Should not get HTTP 400 Bad Request
        if response.status_code != status.HTTP_201_CREATED:
            print(f"Cash withdrawal failed with: {response.data}")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        transaction_id = response.data['id']
        transaction = Transaction.objects.get(id=transaction_id)

        # Verify transaction details
        self.assertEqual(transaction.transaction_type, self.withdrawal_type)
        self.assertEqual(transaction.customer, self.customer)
        self.assertEqual(transaction.from_amount, Decimal('200.00'))

    def test_currency_exchange_transaction_creation_and_list(self):
        """Test Issue 4: Currency exchange transaction list display."""
        # Create currency exchange transaction
        exchange_data = {
            'transaction_type': str(self.exchange_type.id),
            'customer': str(self.customer.id),
            'location': str(self.location.id),
            'from_currency': str(self.usd.id),
            'to_currency': str(self.irr.id),
            'from_amount': '100.00',
            'to_amount': '4000000.00',
            'exchange_rate': '40000.0',
            'description': 'Currency exchange test',
            'delivery_method': 'internal'
        }

        # Create the transaction
        response = self.client.post('/api/v1/transactions/transactions/', exchange_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        transaction_id = response.data['id']

        # Test transaction list retrieval
        list_response = self.client.get('/api/v1/transactions/transactions/')
        self.assertEqual(list_response.status_code, status.HTTP_200_OK)

        # Verify the created transaction appears in the list
        transaction_found = False
        for transaction in list_response.data['results']:
            if transaction['id'] == transaction_id:
                transaction_found = True
                break

        self.assertTrue(transaction_found, "Created transaction should appear in the list")

        # Test filtered list by transaction type
        filtered_response = self.client.get(f'/api/v1/transactions/transactions/?transaction_type={self.exchange_type.id}')
        self.assertEqual(filtered_response.status_code, status.HTTP_200_OK)

        # All transactions in filtered list should be exchange type
        for transaction in filtered_response.data['results']:
            self.assertEqual(transaction['transaction_type']['id'], str(self.exchange_type.id))

    def test_complete_transaction_lifecycle(self):
        """Test Issue 5: Complete transaction lifecycle from creation to completion."""
        # Create a balance adjustment
        adjustment_data = {
            'transaction_type': str(self.adjustment_type.id),
            'customer': str(self.customer.id),
            'location': str(self.location.id),
            'from_currency': str(self.usd.id),
            'to_currency': str(self.usd.id),
            'from_amount': '1000.00',
            'to_amount': '1000.00',
            'exchange_rate': '1.0',
            'description': 'Lifecycle test adjustment',
            'status': 'pending',
            'delivery_method': 'internal'
        }

        # Step 1: Create transaction
        create_response = self.client.post('/api/v1/transactions/transactions/', adjustment_data)
        self.assertEqual(create_response.status_code, status.HTTP_201_CREATED)
        transaction_id = create_response.data['id']

        # Step 2: Approve transaction
        approve_response = self.client.post(f'/api/v1/transactions/transactions/{transaction_id}/approve/')
        self.assertEqual(approve_response.status_code, status.HTTP_200_OK)

        # Step 3: Complete transaction
        complete_response = self.client.post(f'/api/v1/transactions/transactions/{transaction_id}/complete/')
        self.assertEqual(complete_response.status_code, status.HTTP_200_OK)

        # Step 4: Verify balance entries were created
        transaction = Transaction.objects.get(id=transaction_id)
        self.assertEqual(transaction.status, Transaction.Status.COMPLETED)

        # Check that balance entries exist
        balance_entries = BalanceEntry.objects.filter(transaction=transaction)
        self.assertGreater(balance_entries.count(), 0, "Balance entries should be created for completed transaction")

        # Step 5: Verify customer balance was updated
        customer_balance = BalanceEntry.get_current_balance(
            customer=self.customer,
            location=self.location,
            currency=self.usd
        )
        self.assertEqual(customer_balance, Decimal('1000.00'), "Customer balance should reflect the adjustment")

    def test_balance_validation_with_existing_transactions(self):
        """Test balance validation considers existing transactions correctly."""
        # Create initial balance
        BalanceEntry.objects.create(
            customer=self.customer,
            location=self.location,
            currency=self.usd,
            amount=Decimal('100.00'),
            entry_type=BalanceEntry.EntryType.CREDIT,
            description='Initial balance'
        )

        # Create a pending transaction that would reduce balance
        pending_data = {
            'transaction_type': str(self.exchange_type.id),
            'customer': str(self.customer.id),
            'location': str(self.location.id),
            'from_currency': str(self.usd.id),
            'to_currency': str(self.irr.id),
            'from_amount': '50.00',
            'to_amount': '2000000.00',
            'exchange_rate': '40000.0',
            'description': 'Pending transaction',
            'delivery_method': 'internal'
        }

        pending_response = self.client.post('/api/v1/transactions/transactions/', pending_data)
        self.assertEqual(pending_response.status_code, status.HTTP_201_CREATED)

        # Try to create another transaction that would exceed available balance
        # Available should be 100 - 50 = 50 (considering pending transaction)
        exceed_data = {
            'transaction_type': str(self.exchange_type.id),
            'customer': str(self.customer.id),
            'location': str(self.location.id),
            'from_currency': str(self.usd.id),
            'to_currency': str(self.irr.id),
            'from_amount': '75.00',  # This should exceed available balance
            'to_amount': '3000000.00',
            'exchange_rate': '40000.0',
            'description': 'Should fail transaction',
            'delivery_method': 'internal'
        }

        exceed_response = self.client.post('/api/v1/transactions/transactions/', exceed_data)

        # This should either succeed (if validation is not implemented) or fail with proper error
        # The test documents the current behavior
        if exceed_response.status_code != status.HTTP_201_CREATED:
            self.assertIn('balance', str(exceed_response.data).lower(),
                         "Error should mention balance if validation fails")
