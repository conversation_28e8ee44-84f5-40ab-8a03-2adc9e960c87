# Exchange Rate Management in Arena Doviz

## Overview

Arena Doviz Exchange Office Management System implements a flexible exchange rate management system that supports both manual rate entry and automated rate fetching from external sources. This document explains the complete exchange rate flow and implementation.

## Exchange Rate Sources

### 1. Manual Rate Entry (Primary Method)
- **Default Approach**: Exchange rates are manually entered by authorized staff
- **Real-time Updates**: Rates can be updated throughout the day based on market conditions
- **Location-specific**: Each location can have different rates based on local market conditions
- **Currency Pair Management**: Supports all major currency pairs (USD, EUR, AED, IRR, etc.)

### 2. External API Integration (Optional)
- **Backup Source**: Can fetch rates from external providers when manual rates are not available
- **Supported Providers**: 
  - Central Bank APIs
  - Financial data providers (Alpha Vantage, Fixer.io, etc.)
  - Custom exchange rate services

## Exchange Rate Models

### ExchangeRate Model
```python
class ExchangeRate(BaseModel):
    from_currency = ForeignKey(Currency)
    to_currency = ForeignKey(Currency)
    rate = DecimalField(max_digits=15, decimal_places=6)
    location = ForeignKey(Location, null=True, blank=True)
    effective_from = DateTimeField()
    effective_to = DateTimeField(null=True, blank=True)
    rate_type = CharField(choices=['buy', 'sell', 'mid'])
    source = CharField(choices=['manual', 'api', 'calculated'])
    is_active = BooleanField(default=True)
```

### Key Features
- **Bidirectional Rates**: Supports both buy and sell rates
- **Time-based**: Rates have effective periods
- **Location-specific**: Different rates per location
- **Source Tracking**: Tracks whether rate is manual or from API

## Exchange Rate Flow

### 1. Rate Retrieval Process

```mermaid
graph TD
    A[Transaction Request] --> B[Get Exchange Rate]
    B --> C{Manual Rate Available?}
    C -->|Yes| D[Use Manual Rate]
    C -->|No| E{API Rate Available?}
    E -->|Yes| F[Use API Rate]
    E -->|No| G[Calculate Cross Rate]
    G --> H[Use Calculated Rate]
    D --> I[Apply Rate to Transaction]
    F --> I
    H --> I
    I --> J[Complete Transaction]
```

### 2. Rate Priority Order
1. **Manual Rates** (Highest Priority)
   - Location-specific manual rates
   - Global manual rates
2. **API Rates** (Medium Priority)
   - Real-time API rates
   - Cached API rates (if recent)
3. **Calculated Rates** (Lowest Priority)
   - Cross-currency calculations via base currency
   - Historical rate extrapolation

### 3. Rate Calculation Methods

#### Direct Rate
```python
def get_direct_rate(from_currency, to_currency, location=None):
    """Get direct exchange rate between two currencies."""
    rate = ExchangeRate.objects.filter(
        from_currency=from_currency,
        to_currency=to_currency,
        location=location,
        is_active=True,
        effective_from__lte=timezone.now()
    ).filter(
        Q(effective_to__isnull=True) | Q(effective_to__gte=timezone.now())
    ).order_by('-effective_from').first()
    
    return rate.rate if rate else None
```

#### Cross Rate Calculation
```python
def calculate_cross_rate(from_currency, to_currency, base_currency='USD'):
    """Calculate cross rate via base currency."""
    from_to_base = get_direct_rate(from_currency, base_currency)
    base_to_target = get_direct_rate(base_currency, to_currency)
    
    if from_to_base and base_to_target:
        return from_to_base * base_to_target
    return None
```

## API Endpoints

### 1. Get Current Rates
```http
GET /api/v1/currencies/exchange-rates/current/
```

**Parameters:**
- `from_currency`: Source currency code
- `to_currency`: Target currency code
- `location`: Location ID (optional)
- `rate_type`: 'buy', 'sell', or 'mid' (default: 'mid')

**Response:**
```json
{
    "from_currency": "USD",
    "to_currency": "AED",
    "rate": 3.6725,
    "rate_type": "mid",
    "location": "dubai",
    "source": "manual",
    "effective_from": "2025-01-18T10:00:00Z",
    "last_updated": "2025-01-18T10:00:00Z"
}
```

### 2. Update Exchange Rate
```http
POST /api/v1/currencies/exchange-rates/
```

**Request Body:**
```json
{
    "from_currency": "USD",
    "to_currency": "AED",
    "rate": 3.6725,
    "rate_type": "mid",
    "location": "dubai",
    "effective_from": "2025-01-18T10:00:00Z"
}
```

### 3. Get Rate History
```http
GET /api/v1/currencies/exchange-rates/history/
```

**Parameters:**
- `from_currency`: Source currency code
- `to_currency`: Target currency code
- `date_from`: Start date
- `date_to`: End date
- `location`: Location ID (optional)

## Frontend Integration

### 1. Real-time Rate Display
```javascript
// Get current exchange rate
function getCurrentRate(fromCurrency, toCurrency, location) {
    return ArenaDoviz.api.request('GET', 
        `/currencies/exchange-rates/current/?from_currency=${fromCurrency}&to_currency=${toCurrency}&location=${location}`
    );
}

// Update rate display
function updateRateDisplay(fromCurrency, toCurrency) {
    getCurrentRate(fromCurrency, toCurrency, currentLocation)
        .then(response => {
            $('#exchange_rate').val(response.rate);
            $('#rate_source').text(`Source: ${response.source}`);
            $('#rate_updated').text(`Updated: ${response.last_updated}`);
        });
}
```

### 2. Transaction Form Integration
```javascript
// Auto-calculate amounts when rate changes
$('#exchange_rate').on('input', function() {
    const rate = parseFloat($(this).val());
    const fromAmount = parseFloat($('#from_amount').val());
    
    if (rate && fromAmount) {
        const toAmount = fromAmount * rate;
        $('#to_amount').val(toAmount.toFixed(2));
    }
});
```

## Configuration

### 1. Rate Update Settings
```python
# settings.py
EXCHANGE_RATE_SETTINGS = {
    'AUTO_UPDATE_ENABLED': True,
    'UPDATE_INTERVAL': 300,  # 5 minutes
    'API_PROVIDER': 'fixer',
    'FALLBACK_TO_MANUAL': True,
    'CACHE_DURATION': 60,  # 1 minute
    'MAX_RATE_AGE': 3600,  # 1 hour
}
```

### 2. API Provider Configuration
```python
EXCHANGE_RATE_PROVIDERS = {
    'fixer': {
        'api_key': 'your-fixer-api-key',
        'base_url': 'http://data.fixer.io/api/',
        'supported_currencies': ['USD', 'EUR', 'AED', 'IRR']
    },
    'central_bank': {
        'uae_url': 'https://www.centralbank.ae/rates',
        'iran_url': 'https://www.cbi.ir/rates'
    }
}
```

## Rate Management Best Practices

### 1. Manual Rate Entry
- Update rates at market opening
- Monitor rate changes throughout the day
- Set appropriate buy/sell spreads
- Document rate change reasons

### 2. Automated Monitoring
- Set up rate change alerts
- Monitor API availability
- Implement fallback mechanisms
- Log all rate changes

### 3. Compliance and Auditing
- Maintain rate history
- Track rate sources
- Document manual overrides
- Generate rate reports

## Troubleshooting

### Common Issues
1. **Missing Rates**: Check if manual rates are configured
2. **Stale Rates**: Verify API connectivity and update intervals
3. **Calculation Errors**: Ensure base currency rates are available
4. **Permission Issues**: Check user permissions for rate updates

### Rate Validation
- Rates must be positive
- Effective dates must be logical
- Cross-rate consistency checks
- Maximum rate change thresholds
