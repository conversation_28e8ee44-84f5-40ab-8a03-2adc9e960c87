{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Dashboard" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h2 mb-4">
            <i class="bi bi-speedometer2"></i>
            {% trans "Dashboard" %}
        </h1>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card" style="background-color: white; border-left: 4px solid var(--arena-primary);">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title" style="color: var(--arena-primary);">{% trans "Total Customers" %}</h5>
                        <h2 class="mb-0 dashboard-stat-number" id="total-customers" style="color: var(--arena-primary);">-</h2>
                        <small class="text-muted" id="customers-change">-</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1" style="color: var(--arena-primary);"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card dashboard-card success" style="background-color: white; border-left: 4px solid var(--arena-tertiary);">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title" style="color: var(--arena-primary);">{% trans "Today's Transactions" %}</h5>
                        <h2 class="mb-0 dashboard-stat-number" id="today-transactions" style="color: var(--arena-primary);">-</h2>
                        <small class="text-muted" id="transactions-change">-</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-left-right fs-1" style="color: var(--arena-tertiary);"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card dashboard-card" style="background-color: white; border-left: 4px solid var(--arena-secondary);">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title" style="color: var(--arena-primary);">{% trans "Today's Commission" %}</h5>
                        <h2 class="mb-0 dashboard-stat-number" id="today-commission" style="color: var(--arena-primary);">-</h2>
                        <small class="text-muted" id="commission-change">-</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cash-coin fs-1" style="color: var(--arena-secondary);"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card dashboard-card" style="background-color: white; border-left: 4px solid var(--arena-primary);">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title" style="color: var(--arena-primary);">{% trans "Pending Approvals" %}</h5>
                        <h2 class="mb-0 dashboard-stat-number" id="pending-approvals" style="color: var(--arena-primary);">-</h2>
                        <small class="text-muted" id="approvals-change">-</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock fs-1" style="color: var(--arena-primary);"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Balance Summary Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card balance-card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-currency-dollar"></i>
                    {% trans "USD Balance" %}
                </h6>
            </div>
            <div class="card-body">
                <h4 class="mb-1" id="usd-balance">$0.00</h4>
                <small class="text-muted" id="usd-balance-change">-</small>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card balance-card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-currency-exchange"></i>
                    {% trans "AED Balance" %}
                </h6>
            </div>
            <div class="card-body">
                <h4 class="mb-1" id="aed-balance">د.إ 0.00</h4>
                <small class="text-muted" id="aed-balance-change">-</small>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card balance-card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-cash"></i>
                    {% trans "IRR Balance" %}
                </h6>
            </div>
            <div class="card-body">
                <h4 class="mb-1" id="irr-balance">﷼ 0</h4>
                <small class="text-muted" id="irr-balance-change">-</small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    {% trans "Daily Transaction Volume" %}
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="loadChartData(7)">7D</button>
                    <button type="button" class="btn btn-outline-primary active" onclick="loadChartData(30)">30D</button>
                    <button type="button" class="btn btn-outline-primary" onclick="loadChartData(90)">90D</button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="transactionChart" height="100"></canvas>
                <div id="transactionChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    {% trans "Currency Distribution" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="currencyChart" height="200"></canvas>
                <div id="currencyChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Charts Row -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    {% trans "Profit Analysis" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="profitChart" height="150"></canvas>
                <div id="profitChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart-fill"></i>
                    {% trans "Transaction Status" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="150"></canvas>
                <div id="statusChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if user.can_manage_users %}
<!-- Admin Only Charts -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-building"></i>
                    {% trans "Location Performance" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="locationChart" height="80"></canvas>
                <div id="locationChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Exchange Rate Alerts -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle"></i>
                    {% trans "Exchange Rate Alerts" %}
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshExchangeRates()">
                    <i class="bi bi-arrow-clockwise"></i>
                    {% trans "Refresh" %}
                </button>
            </div>
            <div class="card-body">
                <div id="exchange-rate-alerts">
                    <div class="text-center py-3">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2">{% trans "Loading exchange rates..." %}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    {% trans "Recent Transactions" %}
                </h5>
                <a href="{% url 'transactions_web:list' %}" class="btn btn-sm btn-outline-primary">
                    {% trans "View All" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="recent-transactions-table">
                        <thead>
                            <tr>
                                <th>{% trans "Transaction #" %}</th>
                                <th>{% trans "Customer" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center text-muted">
                                    {% trans "Loading..." %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Exchange Rates -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-currency-exchange"></i>
                    {% trans "Current Exchange Rates" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="exchange-rates">
                    <div class="col-12 text-center text-muted">
                        {% trans "Loading exchange rates..." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Utility functions
function getAuthToken() {
    // Get JWT access token from localStorage
    return localStorage.getItem('arena_access_token') || '';
}

function getAuthHeaders() {
    const token = getAuthToken();
    const headers = {
        'Content-Type': 'application/json'
    };

    // Add JWT token if available
    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    // Add CSRF token for session authentication
    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

function getStatusClass(status) {
    const statusClasses = {
        'draft': 'secondary',
        'pending': 'warning',
        'approved': 'info',
        'completed': 'success',
        'cancelled': 'danger',
        'rejected': 'danger'
    };
    return statusClasses[status] || 'secondary';
}

// Global chart instances
let charts = {};

$(document).ready(function() {
    // Initialize dashboard - don't redirect if no JWT token
    // The user might be authenticated via Django session
    loadDashboardData();
    loadChartData(30); // Load 30 days by default

    // Refresh data every 5 minutes
    setInterval(function() {
        loadDashboardData();
        loadChartData(30);
    }, 300000);
});

function loadDashboardData() {
    // Load summary statistics
    loadSummaryStats();
    
    // Load recent transactions
    loadRecentTransactions();
    
    // Load exchange rates
    loadExchangeRates();

    // Load exchange rate alerts
    loadExchangeRateAlerts();
}

function loadSummaryStats() {
    // Load comprehensive dashboard statistics
    // Try with JWT token first, fallback to session auth
    $.ajax({
        url: '/api/v1/core/dashboard/stats/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            // Update main metrics
            $('#total-customers').text(data.total_customers || 0);
            $('#customers-change').text(data.customers_change || '-');

            $('#today-transactions').text(data.today_transactions || 0);
            $('#transactions-change').text(data.transactions_change || '-');

            $('#pending-approvals').text(data.pending_approvals || 0);
            $('#approvals-change').text(data.approvals_change || '-');

            $('#today-commission').text(data.today_commission || '$0.00');
            $('#commission-change').text(data.commission_change || '-');

            // Update balance cards
            if (data.balances) {
                if (data.balances.usd) {
                    $('#usd-balance').text(data.balances.usd.balance || '$0.00');
                    $('#usd-balance-change').text(data.balances.usd.change || '-');
                }

                if (data.balances.aed) {
                    $('#aed-balance').text(data.balances.aed.balance || 'د.إ 0.00');
                    $('#aed-balance-change').text(data.balances.aed.change || '-');
                }

                if (data.balances.irr) {
                    $('#irr-balance').text(data.balances.irr.balance || '﷼ 0');
                    $('#irr-balance-change').text(data.balances.irr.change || '-');
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading dashboard stats:', error);
            // Don't redirect on 401 - just show error states
            // The user is authenticated via Django session for the web page
            $('#total-customers').text('Error');
            $('#today-transactions').text('Error');
            $('#pending-approvals').text('Error');
            $('#today-commission').text('Error');
            $('#usd-balance').text('Error');
            $('#aed-balance').text('Error');
            $('#irr-balance').text('Error');
        }
    });
}

function loadRecentTransactions() {
    $.ajax({
        url: '/api/v1/transactions/transactions/',
        method: 'GET',
        data: {
            ordering: '-created_at',
            limit: 10
        },
        headers: getAuthHeaders(),
        success: function(data) {
            const tbody = $('#recent-transactions-table tbody');
            tbody.empty();

            if (data.results && data.results.length > 0) {
                data.results.forEach(function(tx) {
                    const statusClass = getStatusClass(tx.status);
                    const formattedDate = new Date(tx.created_at).toLocaleString();

                    const row = `
                        <tr>
                            <td><code>${tx.transaction_number}</code></td>
                            <td>${tx.customer_name}</td>
                            <td>${tx.display_amount}</td>
                            <td><span class="badge bg-${statusClass}">${tx.status_display}</span></td>
                            <td>${formattedDate}</td>
                            <td>
                                <a href="/transactions/${tx.id}/" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-eye"></i>
                                </a>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            } else {
                tbody.append(`
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            {% trans "No recent transactions found" %}
                        </td>
                    </tr>
                `);
            }
        },
        error: function() {
            const tbody = $('#recent-transactions-table tbody');
            tbody.empty();
            tbody.append(`
                <tr>
                    <td colspan="6" class="text-center text-danger">
                        {% trans "Error loading transactions" %}
                    </td>
                </tr>
            `);
        }
    });
}

function loadExchangeRates() {
    $.ajax({
        url: '/api/v1/currencies/rates/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const container = $('#exchange-rates');
            container.empty();

            if (data.results && data.results.length > 0) {
                data.results.forEach(function(rate) {
                    const card = `
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h6 class="card-title">${rate.from_currency_code} → ${rate.to_currency_code}</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">Buy</small>
                                            <div class="fw-bold text-success">${rate.buy_rate || 'N/A'}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Sell</small>
                                            <div class="fw-bold text-danger">${rate.sell_rate || 'N/A'}</div>
                                        </div>
                                    </div>
                                    <small class="text-muted">${rate.location_name}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    container.append(card);
                });
            } else {
                container.append(`
                    <div class="col-12 text-center text-muted">
                        {% trans "No exchange rates available" %}
                    </div>
                `);
            }
        },
        error: function() {
            const container = $('#exchange-rates');
            container.empty();
            container.append(`
                <div class="col-12 text-center text-danger">
                    {% trans "Error loading exchange rates" %}
                </div>
            `);
        }
    });
}

function loadChartData(days = 30) {
    // Update active button
    $('.btn-group button').removeClass('active');
    $(`.btn-group button:contains('${days}D')`).addClass('active');

    // Show loading indicators
    showChartLoading(true);

    // Make API request for chart data
    ArenaDoviz.api.request('GET', `core/dashboard/chart_data/?days=${days}`)
        .then(data => {
            initializeCharts(data);
            showChartLoading(false);
        })
        .catch(error => {
            console.error('Error loading chart data:', error);
            showChartLoading(false);
            showAlert('error', '{% trans "Failed to load chart data" %}');
        });
}

function showChartLoading(show) {
    const loadingElements = [
        '#transactionChartLoading',
        '#currencyChartLoading',
        '#profitChartLoading',
        '#statusChartLoading',
        '#locationChartLoading'
    ];

    const chartElements = [
        '#transactionChart',
        '#currencyChart',
        '#profitChart',
        '#statusChart',
        '#locationChart'
    ];

    if (show) {
        loadingElements.forEach(el => $(el).show());
        chartElements.forEach(el => $(el).hide());
    } else {
        loadingElements.forEach(el => $(el).hide());
        chartElements.forEach(el => $(el).show());
    }
}

function initializeCharts(data) {
    // Destroy existing charts first to prevent growing
    ArenaDovizCharts.destroyAll();

    // Small delay to ensure canvas elements are ready after destruction
    setTimeout(function() {
        // Use the enhanced chart utilities
        ArenaDovizCharts.init(data);
    }, 100);
}

function loadExchangeRateAlerts() {
    $.ajax({
        url: '/api/v1/currencies/rates/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            // Extract results array from API response
            const rates = data.results || data || [];
            displayExchangeRateAlerts(rates);
        },
        error: function(xhr, status, error) {
            console.error('Error loading exchange rate alerts:', error);
            $('#exchange-rate-alerts').html(
                '<div class="alert alert-warning">' +
                '<i class="bi bi-exclamation-triangle"></i> ' +
                '{% trans "Failed to load exchange rates" %}' +
                '</div>'
            );
        }
    });
}

function displayExchangeRateAlerts(rates) {
    let alertsHtml = '';

    // Ensure rates is an array
    if (!Array.isArray(rates) || rates.length === 0) {
        alertsHtml = '<div class="alert alert-info">' +
                    '<i class="bi bi-info-circle"></i> ' +
                    '{% trans "No exchange rate alerts at this time" %}' +
                    '</div>';
    } else {
        alertsHtml = '<div class="row">';

        rates.forEach(function(rate, index) {
            if (index < 6) { // Show max 6 rates
                const changeClass = rate.change_percentage >= 0 ? 'text-success' : 'text-danger';
                const changeIcon = rate.change_percentage >= 0 ? 'bi-arrow-up' : 'bi-arrow-down';

                alertsHtml += `
                    <div class="col-md-4 mb-3">
                        <div class="card border-left-primary">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="mb-1">${rate.from_currency_code}/${rate.to_currency_code}</h6>
                                        <div class="small text-muted">${rate.location_name}</div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold">${rate.sell_rate}</div>
                                        <div class="small ${changeClass}">
                                            <i class="bi ${changeIcon}"></i>
                                            ${Math.abs(rate.change_percentage || 0).toFixed(2)}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        });

        alertsHtml += '</div>';
    }

    $('#exchange-rate-alerts').html(alertsHtml);
}

function refreshExchangeRates() {
    $('#exchange-rate-alerts').html(
        '<div class="text-center py-3">' +
        '<div class="spinner-border spinner-border-sm" role="status">' +
        '<span class="visually-hidden">Loading...</span>' +
        '</div>' +
        '<span class="ms-2">{% trans "Refreshing exchange rates..." %}</span>' +
        '</div>'
    );

    loadExchangeRateAlerts();
}
</script>
{% endblock %}
