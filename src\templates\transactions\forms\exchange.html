{% extends 'transactions/base_form.html' %}
{% load i18n %}

{% block transaction_specific_fields %}
<!-- Currency Exchange Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-arrow-left-right"></i>
            {% trans "Currency Exchange Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "From Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.01" required placeholder="0.00">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="to_currency" class="form-label">{% trans "To Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="to_currency" name="to_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="to_amount" class="form-label">{% trans "Receive Amount" %}</label>
                    <input type="number" class="form-control" id="to_amount" name="to_amount" step="0.01" placeholder="0.00" readonly>
                    <div class="form-text">{% trans "Calculated automatically based on current rate" %}</div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="exchange_rate" class="form-label">{% trans "Exchange Rate" %}</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" step="0.000001" placeholder="0.000000" readonly>
                        <button class="btn btn-outline-primary" type="button" id="get-current-rate">
                            <i class="bi bi-arrow-clockwise"></i> {% trans "Get Rate" %}
                        </button>
                    </div>
                    <div class="form-text">{% trans "Current market rate will be applied automatically" %}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="commission_amount" class="form-label">{% trans "Service Fee" %}</label>
                    <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.01" placeholder="0.00">
                    <div class="form-text">{% trans "Optional service fee" %}</div>
                </div>
            </div>
        </div>

        <!-- Exchange Rate Information -->
        <div class="alert alert-info" id="rate-info" style="display: none;">
            <i class="bi bi-info-circle"></i>
            <span id="rate-info-text"></span>
        </div>
    </div>
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="/static/js/transactions/exchange.js"></script>
{% endblock %}
