{"timestamp": "2025-08-20T03:20:13.326184", "production_components": {".env.production": {"exists": false, "description": "Environment configuration", "status": "MISSING"}, "nginx.conf.production": {"exists": false, "description": "Nginx configuration", "status": "MISSING"}, "arena-doviz.service": {"exists": false, "description": "Systemd service file", "status": "MISSING"}, "arena-doviz-backup.service": {"exists": false, "description": "Backup service file", "status": "MISSING"}, "arena-doviz-backup.timer": {"exists": false, "description": "Backup timer file", "status": "MISSING"}, "src/scripts/setup_production.py": {"exists": false, "description": "Production setup script", "status": "MISSING"}, "src/scripts/setup_postgresql.py": {"exists": false, "description": "PostgreSQL setup script", "status": "MISSING"}, "src/scripts/setup_ssl.sh": {"exists": false, "description": "SSL setup script", "status": "MISSING"}, "src/scripts/deploy_production.py": {"exists": false, "description": "Complete deployment script", "status": "MISSING"}, "src/scripts/backup_database.sh": {"exists": false, "description": "Database backup script", "status": "MISSING"}, "src/scripts/security_audit.py": {"exists": false, "description": "Security audit script", "status": "MISSING"}, "src/tests/load_testing/load_test.py": {"exists": false, "description": "Load testing script", "status": "MISSING"}, "src/apps/core/monitoring.py": {"exists": false, "description": "Monitoring system", "status": "MISSING"}, "src/templates/monitoring/dashboard.html": {"exists": false, "description": "Monitoring dashboard", "status": "MISSING"}}, "transfer_forms": {"Internal Transfer": {"url": "/transactions/transfer/internal/add/", "status_code": 200, "accessible": true, "has_form": true}, "External Transfer": {"url": "/transactions/transfer/external/add/", "status_code": 200, "accessible": true, "has_form": true}, "International Transfer": {"url": "/transactions/transfer/international/add/", "status_code": 200, "accessible": true, "has_form": true}}, "monitoring_system": {"health_check": {"status_code": 404, "working": false}, "dashboard": {"status_code": 404, "working": false}, "monitoring.py": {"exists": false, "executable": false}, "health_check.py": {"exists": false, "executable": false}}, "security_features": {"security_audit": {"exists": false, "executable": false}, "production_settings": {"debug_disabled": true, "allowed_hosts_configured": true, "encryption_key_configured": true, "security_headers_configured": true}}, "performance_metrics": {"load_testing": {"exists": false}, "database": {"response_time_ms": 0.16, "status": "GOOD"}}, "overall_status": "NOT_READY", "deployment_scripts": {"src/scripts/deploy_production.py": {"exists": false, "description": "Complete deployment automation", "status": "MISSING"}, "src/scripts/setup_production.py": {"exists": false, "description": "Production environment setup", "status": "MISSING"}, "src/scripts/setup_postgresql.py": {"exists": false, "description": "PostgreSQL setup", "status": "MISSING"}, "src/scripts/setup_ssl.sh": {"exists": false, "description": "SSL certificate setup", "status": "MISSING"}, "src/scripts/backup_database.sh": {"exists": false, "description": "Database backup automation", "status": "MISSING"}}}