<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arena Doviz Production Monitoring Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: #000d28;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.5rem;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-healthy { background: #28a745; color: white; }
        .status-warning { background: #ffc107; color: #000; }
        .status-critical { background: #dc3545; color: white; }
        .status-degraded { background: #fd7e14; color: white; }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #000d28;
        }
        
        .card h3 {
            margin-bottom: 1rem;
            color: #000d28;
            font-size: 1.1rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: 500;
        }
        
        .metric-value {
            font-weight: bold;
            color: #000d28;
        }
        
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 1rem;
            margin: 0.5rem 0;
        }
        
        .alert.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .alert.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .alert.info {
            background: #d1ecf1;
            border-color: #bee5eb;
        }
        
        .chart-container {
            height: 300px;
            margin: 1rem 0;
        }
        
        .service-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .status-up { background: #28a745; }
        .status-down { background: #dc3545; }
        .status-unknown { background: #6c757d; }
        
        .refresh-info {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            margin-top: 2rem;
        }
        
        .auto-refresh {
            background: #000d28;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .auto-refresh:hover {
            background: #001a3d;
        }
        
        .full-width {
            grid-column: 1 / -1;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            text-align: left;
            padding: 0.75rem;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        
        .progress-fill.warning {
            background: linear-gradient(90deg, #ffc107, #fd7e14);
        }
        
        .progress-fill.danger {
            background: linear-gradient(90deg, #dc3545, #e74c3c);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 Arena Doviz Production Monitoring</h1>
        <div>
            <span class="status-badge" id="overall-status">LOADING...</span>
            <button class="auto-refresh" onclick="toggleAutoRefresh()" id="refresh-btn">Auto Refresh: OFF</button>
        </div>
    </div>

    <div class="container">
        <!-- System Overview -->
        <div class="grid">
            <div class="card">
                <h3>🖥️ System Resources</h3>
                <div class="metric">
                    <span class="metric-label">CPU Usage</span>
                    <span class="metric-value" id="cpu-usage">-</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="cpu-progress"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory Usage</span>
                    <span class="metric-value" id="memory-usage">-</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="memory-progress"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">Disk Usage</span>
                    <span class="metric-value" id="disk-usage">-</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="disk-progress"></div>
                </div>
            </div>

            <div class="card">
                <h3>🗄️ Database Performance</h3>
                <div class="metric">
                    <span class="metric-label">Response Time</span>
                    <span class="metric-value" id="db-response">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Active Connections</span>
                    <span class="metric-value" id="db-connections">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Database Size</span>
                    <span class="metric-value" id="db-size">-</span>
                </div>
            </div>

            <div class="card">
                <h3>💼 Business Metrics</h3>
                <div class="metric">
                    <span class="metric-label">Transactions Today</span>
                    <span class="metric-value" id="transactions-today">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Daily Commission</span>
                    <span class="metric-value" id="daily-commission">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Active Customers (30d)</span>
                    <span class="metric-value" id="active-customers">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Pending Transactions</span>
                    <span class="metric-value" id="pending-transactions">-</span>
                </div>
            </div>

            <div class="card">
                <h3>🔗 External Services</h3>
                <div class="metric">
                    <span class="metric-label">PostgreSQL</span>
                    <div class="service-status">
                        <div class="status-indicator" id="postgres-status"></div>
                        <span class="metric-value" id="postgres-text">-</span>
                    </div>
                </div>
                <div class="metric">
                    <span class="metric-label">Redis</span>
                    <div class="service-status">
                        <div class="status-indicator" id="redis-status"></div>
                        <span class="metric-value" id="redis-text">-</span>
                    </div>
                </div>
                <div class="metric">
                    <span class="metric-label">Email Service</span>
                    <div class="service-status">
                        <div class="status-indicator" id="email-status"></div>
                        <span class="metric-value" id="email-text">-</span>
                    </div>
                </div>
                <div class="metric">
                    <span class="metric-label">Backup System</span>
                    <div class="service-status">
                        <div class="status-indicator" id="backup-status"></div>
                        <span class="metric-value" id="backup-text">-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts Section -->
        <div class="card full-width">
            <h3>🚨 System Alerts</h3>
            <div id="alerts-container">
                <p>Loading alerts...</p>
            </div>
        </div>

        <!-- Response Times Chart -->
        <div class="card full-width">
            <h3>📊 API Response Times</h3>
            <div id="response-times-table">
                <table>
                    <thead>
                        <tr>
                            <th>Endpoint</th>
                            <th>Response Time</th>
                            <th>Status</th>
                            <th>Last Check</th>
                        </tr>
                    </thead>
                    <tbody id="response-times-body">
                        <tr><td colspan="4">Loading...</td></tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="refresh-info">
            <p>Last updated: <span id="last-updated">-</span></p>
            <p>Next refresh in: <span id="next-refresh">-</span></p>
        </div>
    </div>

    <script>
        let autoRefresh = false;
        let refreshInterval;
        let countdownInterval;
        let nextRefreshTime = 0;

        function updateDashboard() {
            fetch('/api/v1/monitoring/dashboard/')
                .then(response => response.json())
                .then(data => {
                    updateSystemMetrics(data);
                    updateBusinessMetrics(data);
                    updateExternalServices(data);
                    updateAlerts(data);
                    updateResponseTimes(data);
                    updateOverallStatus(data);
                    
                    document.getElementById('last-updated').textContent = new Date().toLocaleString();
                    nextRefreshTime = Date.now() + 30000; // 30 seconds
                })
                .catch(error => {
                    console.error('Error updating dashboard:', error);
                    document.getElementById('overall-status').textContent = 'ERROR';
                    document.getElementById('overall-status').className = 'status-badge status-critical';
                });
        }

        function updateSystemMetrics(data) {
            const metrics = data.system_health?.metrics || {};
            
            // CPU
            const cpu = metrics.cpu_usage || 0;
            document.getElementById('cpu-usage').textContent = cpu + '%';
            updateProgressBar('cpu-progress', cpu);
            
            // Memory
            const memory = metrics.memory_usage || 0;
            document.getElementById('memory-usage').textContent = memory + '%';
            updateProgressBar('memory-progress', memory);
            
            // Disk
            const disk = metrics.disk_usage || 0;
            document.getElementById('disk-usage').textContent = disk.toFixed(1) + '%';
            updateProgressBar('disk-progress', disk);
            
            // Database
            document.getElementById('db-response').textContent = (metrics.db_response_time || 0).toFixed(2) + 'ms';
            document.getElementById('db-connections').textContent = metrics.active_db_connections || '-';
            document.getElementById('db-size').textContent = metrics.db_size || '-';
        }

        function updateBusinessMetrics(data) {
            const business = data.business_metrics || {};
            
            document.getElementById('transactions-today').textContent = business.transaction_volume?.today || 0;
            document.getElementById('daily-commission').textContent = '$' + (business.daily_commission || 0).toFixed(2);
            document.getElementById('active-customers').textContent = business.active_customers_30d || 0;
            document.getElementById('pending-transactions').textContent = data.system_health?.metrics?.pending_transactions || 0;
        }

        function updateExternalServices(data) {
            const services = data.external_services || {};
            
            updateServiceStatus('postgres', services.PostgreSQL);
            updateServiceStatus('redis', services.Redis);
            updateServiceStatus('email', services['Email Service']);
            updateServiceStatus('backup', services['Backup System']);
        }

        function updateServiceStatus(service, status) {
            const indicator = document.getElementById(service + '-status');
            const text = document.getElementById(service + '-text');
            
            if (status?.healthy) {
                indicator.className = 'status-indicator status-up';
                text.textContent = 'Healthy';
            } else {
                indicator.className = 'status-indicator status-down';
                text.textContent = status?.error || 'Unknown';
            }
        }

        function updateAlerts(data) {
            const alerts = data.alerts || [];
            const container = document.getElementById('alerts-container');
            
            if (alerts.length === 0) {
                container.innerHTML = '<p style="color: #28a745;">✅ No active alerts</p>';
                return;
            }
            
            container.innerHTML = alerts.map(alert => `
                <div class="alert ${alert.level.toLowerCase()}">
                    <strong>[${alert.level}] ${alert.category}:</strong> ${alert.message}
                </div>
            `).join('');
        }

        function updateResponseTimes(data) {
            const responseTimes = data.performance_metrics || {};
            const tbody = document.getElementById('response-times-body');
            
            if (Object.keys(responseTimes).length === 0) {
                tbody.innerHTML = '<tr><td colspan="4">No response time data available</td></tr>';
                return;
            }
            
            tbody.innerHTML = Object.entries(responseTimes).map(([endpoint, data]) => `
                <tr>
                    <td>${endpoint}</td>
                    <td>${data.response_time ? data.response_time.toFixed(2) + 'ms' : 'N/A'}</td>
                    <td>${data.status_code || 'N/A'}</td>
                    <td>${new Date().toLocaleTimeString()}</td>
                </tr>
            `).join('');
        }

        function updateOverallStatus(data) {
            const status = data.overall_status || 'UNKNOWN';
            const badge = document.getElementById('overall-status');
            
            badge.textContent = status;
            badge.className = 'status-badge status-' + status.toLowerCase();
        }

        function updateProgressBar(id, value) {
            const bar = document.getElementById(id);
            bar.style.width = Math.min(value, 100) + '%';
            
            if (value > 80) {
                bar.className = 'progress-fill danger';
            } else if (value > 60) {
                bar.className = 'progress-fill warning';
            } else {
                bar.className = 'progress-fill';
            }
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const btn = document.getElementById('refresh-btn');
            
            if (autoRefresh) {
                btn.textContent = 'Auto Refresh: ON';
                refreshInterval = setInterval(updateDashboard, 30000);
                countdownInterval = setInterval(updateCountdown, 1000);
            } else {
                btn.textContent = 'Auto Refresh: OFF';
                clearInterval(refreshInterval);
                clearInterval(countdownInterval);
                document.getElementById('next-refresh').textContent = '-';
            }
        }

        function updateCountdown() {
            if (!autoRefresh) return;
            
            const remaining = Math.max(0, Math.ceil((nextRefreshTime - Date.now()) / 1000));
            document.getElementById('next-refresh').textContent = remaining + 's';
        }

        // Initial load
        updateDashboard();
        
        // Manual refresh button
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                updateDashboard();
            }
        });
    </script>
</body>
</html>
