#!/usr/bin/env python
"""
Manual test script to verify transaction system fixes.
Run this to test the specific issues found during manual testing.
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# Set encryption key for testing
from cryptography.fernet import Fernet
test_key = Fernet.generate_key().decode()
os.environ['ARENA_ENCRYPTION_KEY'] = test_key

django.setup()

from django.contrib.auth import get_user_model
from apps.transactions.models import Transaction, TransactionType, BalanceEntry
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency
from apps.accounts.models import User

User = get_user_model()

def test_balance_adjustment_approval():
    """Test the balance adjustment creation and approval workflow."""
    print("🧪 Testing Balance Adjustment Creation and Approval...")
    
    # Get or create test data
    try:
        user = User.objects.filter(role=User.Role.ADMIN).first()
        if not user:
            user = User.objects.create_user(
                username='testadmin',
                email='<EMAIL>',
                password='testpass123',
                first_name='Test',
                last_name='Admin',
                role=User.Role.ADMIN
            )
            print(f"✅ Created test admin user: {user.username}")
        
        location = Location.objects.filter(is_active=True).first()
        if not location:
            print("❌ No active location found")
            return False
            
        usd = Currency.objects.filter(code='USD').first()
        if not usd:
            print("❌ USD currency not found")
            return False
            
        customer = Customer.objects.filter(status=Customer.Status.ACTIVE).first()
        if not customer:
            customer = Customer.objects.create(
                first_name='Test',
                last_name='Customer',
                email='<EMAIL>',
                phone_number='+1234567890',
                status=Customer.Status.ACTIVE
            )
            print(f"✅ Created test customer: {customer.get_display_name()}")
            
        adjustment_type = TransactionType.objects.filter(code='ADJUSTMENT').first()
        if not adjustment_type:
            print("❌ Balance Adjustment transaction type not found")
            return False
            
        # Test 1: Create balance adjustment transaction
        print("\n📝 Step 1: Creating balance adjustment transaction...")
        transaction = Transaction.objects.create(
            transaction_type=adjustment_type,
            customer=customer,
            location=location,
            from_currency=usd,
            to_currency=usd,
            from_amount=Decimal('1000.00'),
            to_amount=Decimal('1000.00'),
            exchange_rate=Decimal('1.0'),
            description='Test balance adjustment',
            status=Transaction.Status.PENDING,  # This should work now
            delivery_method='internal'
        )
        
        print(f"✅ Transaction created: {transaction.transaction_number}")
        print(f"   Status: {transaction.status}")
        print(f"   Can be approved: {transaction.can_be_approved()}")
        
        # Test 2: Approve the transaction
        print("\n✅ Step 2: Testing approval workflow...")
        if transaction.can_be_approved():
            transaction.status = Transaction.Status.APPROVED
            transaction.approved_by = user
            from django.utils import timezone
            transaction.approved_at = timezone.now()
            transaction.save()
            
            print(f"✅ Transaction approved successfully")
            print(f"   New status: {transaction.status}")
            print(f"   Approved by: {transaction.approved_by}")
            print(f"   Approved at: {transaction.approved_at}")
            
            # Test 3: Complete the transaction
            print("\n🔄 Step 3: Testing completion workflow...")
            transaction.status = Transaction.Status.COMPLETED
            transaction.completed_at = timezone.now()
            transaction.save()
            
            print(f"✅ Transaction completed successfully")
            print(f"   Final status: {transaction.status}")
            
            # Check balance entries
            balance_entries = BalanceEntry.objects.filter(transaction=transaction)
            print(f"   Balance entries created: {balance_entries.count()}")
            
            return True
        else:
            print(f"❌ Transaction cannot be approved. Status: {transaction.status}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_cash_transaction_creation():
    """Test cash deposit/withdrawal transaction creation."""
    print("\n🧪 Testing Cash Transaction Creation...")
    
    try:
        location = Location.objects.filter(is_active=True).first()
        usd = Currency.objects.filter(code='USD').first()
        customer = Customer.objects.filter(status=Customer.Status.ACTIVE).first()
        deposit_type = TransactionType.objects.filter(code='DEPOSIT').first()
        
        if not all([location, usd, customer, deposit_type]):
            print("❌ Missing required data for cash transaction test")
            return False
            
        # Test cash deposit creation
        print("📝 Creating cash deposit transaction...")
        deposit = Transaction.objects.create(
            transaction_type=deposit_type,
            customer=customer,
            location=location,
            from_currency=usd,
            to_currency=usd,
            from_amount=Decimal('500.00'),
            to_amount=Decimal('500.00'),
            exchange_rate=Decimal('1.0'),
            description='Test cash deposit',
            delivery_method='cash'
        )
        
        print(f"✅ Cash deposit created: {deposit.transaction_number}")
        print(f"   Status: {deposit.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during cash transaction test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Arena Doviz Transaction System Fix Tests")
    print("=" * 60)
    
    results = []
    
    # Test 1: Balance Adjustment Approval
    results.append(test_balance_adjustment_approval())
    
    # Test 2: Cash Transaction Creation
    results.append(test_cash_transaction_creation())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    if passed == total:
        print("🎉 All tests passed! Transaction system fixes are working.")
    else:
        print("⚠️  Some tests failed. Please check the output above.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
