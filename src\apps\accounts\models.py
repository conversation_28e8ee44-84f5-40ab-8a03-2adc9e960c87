"""
User and authentication models for Arena Doviz Exchange Accounting System.
"""

from django.contrib.auth.models import AbstractUser, Group, UserManager
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from apps.core.models import TimeStampedModel, UUIDModel, BaseModel
import logging

logger = logging.getLogger(__name__)


class CustomUserManager(UserManager):
    """Custom user manager for Arena Doviz system."""

    def get_queryset(self):
        """Return queryset excluding soft-deleted users."""
        return super().get_queryset().filter(is_deleted=False)

    def create_superuser(self, username, email=None, password=None, **extra_fields):
        """Create and save a superuser with ADMIN role."""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('role', 'admin')  # Set admin role for superusers

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(username, email, password, **extra_fields)


class User(AbstractUser, BaseModel):
    """
    Custom user model for Arena Doviz system.
    Extends Django's AbstractUser with additional fields for exchange office management.
    """

    # Use custom manager
    objects = CustomUserManager()
    all_objects = UserManager()  # Manager that includes soft-deleted users

    # User roles based on business requirements
    class Role(models.TextChoices):
        ADMIN = 'admin', _('Admin')
        ACCOUNTANT = 'accountant', _('Accountant')
        BRANCH_EMPLOYEE = 'branch_employee', _('Branch Employee')
        VIEWER = 'viewer', _('Viewer')
        COURIER = 'courier', _('Courier')
    
    # Additional user information
    phone_number = models.CharField(
        _('Phone number'),
        max_length=20,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_('Phone number must be entered in the format: "+*********". Up to 15 digits allowed.')
            )
        ],
        blank=True,
        help_text=_('Contact phone number')
    )
    
    role = models.CharField(
        _('Role'),
        max_length=20,
        choices=Role.choices,
        default=Role.VIEWER,
        help_text=_('User role determining access permissions')
    )
    
    location = models.ForeignKey(
        'locations.Location',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='users',
        verbose_name=_('Location'),
        help_text=_('Primary location where this user works')
    )
    
    is_active_session = models.BooleanField(
        _('Has active session'),
        default=False,
        help_text=_('Whether the user currently has an active session')
    )
    
    last_login_ip = models.GenericIPAddressField(
        _('Last login IP'),
        null=True,
        blank=True,
        help_text=_('IP address of the last login')
    )
    
    failed_login_attempts = models.PositiveIntegerField(
        _('Failed login attempts'),
        default=0,
        help_text=_('Number of consecutive failed login attempts')
    )
    
    account_locked_until = models.DateTimeField(
        _('Account locked until'),
        null=True,
        blank=True,
        help_text=_('Account is locked until this date/time')
    )
    
    # Profile information
    employee_id = models.CharField(
        _('Employee ID'),
        max_length=20,
        unique=True,
        null=True,
        blank=True,
        help_text=_('Unique employee identifier')
    )
    
    department = models.CharField(
        _('Department'),
        max_length=100,
        blank=True,
        help_text=_('Department or division')
    )
    
    notes = models.TextField(
        _('Notes'),
        blank=True,
        help_text=_('Internal notes about this user')
    )
    
    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        ordering = ['last_name', 'first_name']
        indexes = [
            models.Index(fields=['role']),
            models.Index(fields=['location']),
            models.Index(fields=['is_active']),
            models.Index(fields=['employee_id']),
        ]
    
    def __str__(self):
        return f"{self.get_full_name()} ({self.username})"
    
    def get_full_name(self):
        """
        Return the first_name plus the last_name, with a space in between.
        """
        full_name = f"{self.first_name} {self.last_name}".strip()
        return full_name or self.username
    
    def get_display_name(self):
        """
        Return a display name for the user.
        """
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.username
    
    def is_admin(self):
        """Check if user has admin role."""
        return self.role == self.Role.ADMIN
    
    def is_accountant(self):
        """Check if user has accountant role."""
        return self.role == self.Role.ACCOUNTANT
    
    def is_branch_employee(self):
        """Check if user has branch employee role."""
        return self.role == self.Role.BRANCH_EMPLOYEE
    
    def is_viewer(self):
        """Check if user has viewer role."""
        return self.role == self.Role.VIEWER
    
    def is_courier(self):
        """Check if user has courier role."""
        return self.role == self.Role.COURIER
    
    def can_manage_transactions(self):
        """Check if user can manage transactions."""
        return self.role in [self.Role.ADMIN, self.Role.ACCOUNTANT, self.Role.BRANCH_EMPLOYEE]
    
    def can_view_reports(self):
        """Check if user can view reports."""
        return self.role in [self.Role.ADMIN, self.Role.ACCOUNTANT, self.Role.VIEWER]
    
    def can_manage_users(self):
        """Check if user can manage other users."""
        return self.role == self.Role.ADMIN
    
    def can_manage_system_settings(self):
        """Check if user can manage system settings."""
        return self.role == self.Role.ADMIN

    def can_view_all_locations(self):
        """Check if user can view all locations."""
        return self.role == self.Role.ADMIN

    def can_approve_transactions(self):
        """Check if user can approve transactions."""
        return self.role in [self.Role.ADMIN, self.Role.ACCOUNTANT]

    def can_complete_transactions(self):
        """Check if user can complete transactions."""
        return self.role in [self.Role.ADMIN, self.Role.ACCOUNTANT, self.Role.BRANCH_EMPLOYEE]
    
    def reset_failed_login_attempts(self):
        """Reset failed login attempts counter."""
        if self.failed_login_attempts > 0:
            self.failed_login_attempts = 0
            self.account_locked_until = None
            self.save(update_fields=['failed_login_attempts', 'account_locked_until'])
            logger.info(f"Reset failed login attempts for user {self.username}")
    
    def increment_failed_login_attempts(self):
        """Increment failed login attempts and lock account if necessary."""
        from django.utils import timezone
        from datetime import timedelta
        
        self.failed_login_attempts += 1
        
        # Lock account after 5 failed attempts for 30 minutes
        if self.failed_login_attempts >= 5:
            self.account_locked_until = timezone.now() + timedelta(minutes=30)
            logger.warning(f"Account locked for user {self.username} due to failed login attempts")
        
        self.save(update_fields=['failed_login_attempts', 'account_locked_until'])
    
    def is_account_locked(self):
        """Check if account is currently locked."""
        from django.utils import timezone
        
        if self.account_locked_until:
            if timezone.now() < self.account_locked_until:
                return True
            else:
                # Lock period has expired, reset
                self.account_locked_until = None
                self.failed_login_attempts = 0
                self.save(update_fields=['account_locked_until', 'failed_login_attempts'])
        
        return False
    
    def save(self, *args, **kwargs):
        """
        Override save to handle role-based group assignment.
        """
        is_new = self.pk is None
        
        super().save(*args, **kwargs)
        
        # Assign user to appropriate group based on role
        if is_new or 'role' in kwargs.get('update_fields', []):
            self._assign_role_group()
        
        if is_new:
            logger.info(f"New user created: {self.username} with role {self.role}")
        else:
            logger.debug(f"User updated: {self.username}")
    
    def _assign_role_group(self):
        """Assign user to the appropriate group based on their role."""
        try:
            # Remove user from all role-based groups
            role_groups = Group.objects.filter(
                name__in=[choice[1] for choice in self.Role.choices]
            )
            self.groups.remove(*role_groups)
            
            # Add user to the appropriate group
            role_group_name = self.get_role_display()
            group, created = Group.objects.get_or_create(name=role_group_name)
            self.groups.add(group)
            
            logger.debug(f"User {self.username} assigned to group {role_group_name}")
            
        except Exception as e:
            logger.error(f"Failed to assign role group for user {self.username}: {e}")


class UserSession(TimeStampedModel):
    """
    Model to track user sessions for security and audit purposes.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sessions',
        verbose_name=_('User')
    )
    
    session_key = models.CharField(
        _('Session key'),
        max_length=40,
        unique=True,
        help_text=_('Django session key')
    )
    
    ip_address = models.GenericIPAddressField(
        _('IP address'),
        help_text=_('IP address of the session')
    )
    
    user_agent = models.TextField(
        _('User agent'),
        blank=True,
        help_text=_('Browser user agent string')
    )
    
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this session is currently active')
    )
    
    ended_at = models.DateTimeField(
        _('Ended at'),
        null=True,
        blank=True,
        help_text=_('When the session ended')
    )

    expires_at = models.DateTimeField(
        _('Expires at'),
        null=True,
        blank=True,
        help_text=_('When the session expires')
    )
    
    class Meta:
        verbose_name = _('User Session')
        verbose_name_plural = _('User Sessions')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_key']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.ip_address} ({self.created_at})"
    
    def end_session(self):
        """Mark the session as ended."""
        from django.utils import timezone

        self.is_active = False
        self.ended_at = timezone.now()
        self.save(update_fields=['is_active', 'ended_at'])

        logger.info(f"Session ended for user {self.user.username} from {self.ip_address}")

    def is_expired(self):
        """Check if session is expired."""
        if not self.expires_at:
            return False

        from django.utils import timezone
        return timezone.now() > self.expires_at


class AuditLog(TimeStampedModel):
    """
    Model for comprehensive audit logging of user actions.
    """
    
    class Action(models.TextChoices):
        LOGIN = 'login', _('Login')
        LOGOUT = 'logout', _('Logout')
        CREATE = 'create', _('Create')
        UPDATE = 'update', _('Update')
        DELETE = 'delete', _('Delete')
        VIEW = 'view', _('View')
        EXPORT = 'export', _('Export')
        IMPORT = 'import', _('Import')
        APPROVE = 'approve', _('Approve')
        REJECT = 'reject', _('Reject')
    
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_logs',
        verbose_name=_('User')
    )
    
    action = models.CharField(
        _('Action'),
        max_length=20,
        choices=Action.choices,
        help_text=_('Type of action performed')
    )
    
    model_name = models.CharField(
        _('Model name'),
        max_length=100,
        blank=True,
        help_text=_('Name of the model that was affected')
    )
    
    object_id = models.CharField(
        _('Object ID'),
        max_length=100,
        blank=True,
        help_text=_('ID of the object that was affected')
    )
    
    object_repr = models.CharField(
        _('Object representation'),
        max_length=200,
        blank=True,
        help_text=_('String representation of the affected object')
    )
    
    changes = models.JSONField(
        _('Changes'),
        default=dict,
        blank=True,
        help_text=_('JSON representation of changes made')
    )
    
    ip_address = models.GenericIPAddressField(
        _('IP address'),
        null=True,
        blank=True,
        help_text=_('IP address from which the action was performed')
    )
    
    user_agent = models.TextField(
        _('User agent'),
        blank=True,
        help_text=_('Browser user agent string')
    )
    
    additional_data = models.JSONField(
        _('Additional data'),
        default=dict,
        blank=True,
        help_text=_('Additional context data')
    )
    
    class Meta:
        verbose_name = _('Audit Log')
        verbose_name_plural = _('Audit Logs')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'action']),
            models.Index(fields=['model_name', 'object_id']),
            models.Index(fields=['created_at']),
            models.Index(fields=['action']),
        ]
    
    def __str__(self):
        user_str = self.user.username if self.user else 'Anonymous'
        return f"{user_str} - {self.action} - {self.model_name} ({self.created_at})"
    
    @classmethod
    def log_action(cls, user, action, model_name='', object_id='', object_repr='', 
                   changes=None, ip_address=None, user_agent='', additional_data=None):
        """
        Create an audit log entry.
        """
        try:
            audit_log = cls.objects.create(
                user=user,
                action=action,
                model_name=model_name,
                object_id=str(object_id) if object_id else '',
                object_repr=object_repr,
                changes=changes or {},
                ip_address=ip_address,
                user_agent=user_agent,
                additional_data=additional_data or {}
            )
            
            logger.debug(f"Audit log created: {audit_log}")
            return audit_log
            
        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")
            return None
