{% extends 'transactions/base_form.html' %}
{% load i18n %}

{% block transaction_specific_fields %}
<!-- Balance Adjustment Details Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-calculator"></i>
            {% trans "Balance Adjustment Details" %}
        </h6>
    </div>
    <div class="card-body">
        <!-- Hidden fields for transaction processing -->
        <input type="hidden" id="to_amount" name="to_amount" value="0">

        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="adjustment_type" class="form-label">{% trans "Adjustment Type" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="adjustment_type" name="adjustment_type" required>
                        <option value="">{% trans "Select type..." %}</option>
                        <option value="increase">{% trans "Increase Balance" %}</option>
                        <option value="decrease">{% trans "Decrease Balance" %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Adjustment Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.000001" required placeholder="0.00">
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="current_balance" class="form-label">{% trans "Current Balance" %}</label>
                    <input type="text" class="form-control" id="current_balance" name="current_balance" readonly placeholder="{% trans 'Loading...' %}">
                    <div class="form-text">{% trans "Customer's current balance in selected currency" %}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="new_balance" class="form-label">{% trans "New Balance" %}</label>
                    <input type="text" class="form-control" id="new_balance" name="new_balance" readonly placeholder="{% trans 'Calculated automatically...' %}">
                    <div class="form-text">{% trans "Balance after adjustment" %}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Adjustment Reason Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-clipboard-check"></i>
            {% trans "Adjustment Reason" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <label for="adjustment_reason" class="form-label">{% trans "Reason Category" %} <span class="text-danger">*</span></label>
            <select class="form-select" id="adjustment_reason" name="adjustment_reason" required>
                <option value="">{% trans "Select reason..." %}</option>
                <option value="correction">{% trans "Data Correction" %}</option>
                <option value="refund">{% trans "Refund" %}</option>
                <option value="bonus">{% trans "Bonus/Promotion" %}</option>
                <option value="penalty">{% trans "Penalty/Fee" %}</option>
                <option value="system_error">{% trans "System Error Correction" %}</option>
                <option value="manual_entry">{% trans "Manual Entry Error" %}</option>
                <option value="other">{% trans "Other" %}</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="adjustment_details" class="form-label">{% trans "Detailed Explanation" %} <span class="text-danger">*</span></label>
            <textarea class="form-control" id="adjustment_details" name="adjustment_details" rows="4" required placeholder="{% trans 'Please provide a detailed explanation for this adjustment...' %}"></textarea>
            <div class="form-text">{% trans "This explanation will be recorded in the audit log" %}</div>
        </div>
        
        <div class="mb-3">
            <label for="related_transaction" class="form-label">{% trans "Related Transaction" %}</label>
            <input type="text" class="form-control" id="related_transaction" name="related_transaction" placeholder="{% trans 'Transaction number if applicable...' %}">
            <div class="form-text">{% trans "Reference to original transaction if this adjustment is related to a specific transaction" %}</div>
        </div>
    </div>
</div>

<!-- Authorization Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-shield-exclamation"></i>
            {% trans "Authorization Required" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>{% trans "Important:" %}</strong> {% trans "Balance adjustments require supervisor approval and will be logged in the audit trail." %}
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="requested_by" class="form-label">{% trans "Requested By" %}</label>
                    <input type="text" class="form-control" id="requested_by" name="requested_by" readonly placeholder="{% trans 'Current user...' %}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="supervisor_approval" class="form-label">{% trans "Supervisor Approval Required" %}</label>
                    <input type="text" class="form-control" value="{% trans 'Yes - Pending' %}" readonly>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="confirm_adjustment" name="confirm_adjustment" required>
                <label class="form-check-label" for="confirm_adjustment">
                    {% trans "I confirm that this adjustment is necessary and accurate" %} <span class="text-danger">*</span>
                </label>
            </div>
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="customer_notified" name="customer_notified">
                <label class="form-check-label" for="customer_notified">
                    {% trans "Customer has been notified of this adjustment" %}
                </label>
            </div>
        </div>
    </div>
</div>

<!-- Impact Warning -->
<div class="alert alert-danger" id="negative-balance-warning" style="display: none;">
    <i class="bi bi-exclamation-triangle"></i>
    <strong>{% trans "Warning:" %}</strong> {% trans "This adjustment will result in a negative balance. Please ensure this is intentional." %}
</div>

<!-- Audit Information -->
<div class="alert alert-info">
    <i class="bi bi-info-circle"></i>
    <strong>{% trans "Audit Trail:" %}</strong> {% trans "This adjustment will be recorded with timestamp, user information, and all provided details for compliance and audit purposes." %}
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="/static/js/transactions/adjustment.js"></script>
{% endblock %}
