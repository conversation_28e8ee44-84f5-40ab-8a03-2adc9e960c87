{% extends 'transactions/base_form.html' %}
{% load i18n %}

{% block transaction_specific_fields %}
<!-- Cash Deposit Details Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-cash-coin"></i>
            {% trans "Cash Deposit Details" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_currency" class="form-label">{% trans "Currency" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="from_currency" name="from_currency" required>
                        <option value="">{% trans "Select currency..." %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="from_amount" class="form-label">{% trans "Deposit Amount" %} <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.000001" required placeholder="0.00">
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="commission_amount" class="form-label">
                        {% trans "Service Fee" %}
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="calculateDepositCommission()">
                            <i class="bi bi-calculator"></i> {% trans "Auto Calculate" %}
                        </button>
                    </label>
                    <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.000001" placeholder="0.00">
                    <div class="form-text" id="commission_info"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="deposit_source" class="form-label">{% trans "Deposit Source" %}</label>
                    <select class="form-select" id="deposit_source" name="deposit_source">
                        <option value="cash">{% trans "Cash" %}</option>
                        <option value="bank_transfer">{% trans "Bank Transfer" %}</option>
                        <option value="check">{% trans "Check" %}</option>
                        <option value="other">{% trans "Other" %}</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="delivery_method" class="form-label">{% trans "Delivery Method" %}</label>
                    <select class="form-select" id="delivery_method" name="delivery_method">
                        <option value="in_person">{% trans "In Person" %}</option>
                        <option value="courier">{% trans "Courier" %}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3" id="courier-field" style="display: none;">
                    <label for="courier" class="form-label">{% trans "Courier" %} <span class="text-danger">*</span></label>
                    <select class="form-select" id="courier" name="courier">
                        <option value="">{% trans "Select courier..." %}</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="mb-3" id="bank-transfer-details" style="display: none;">
            <label for="bank_reference" class="form-label">{% trans "Bank Reference Number" %}</label>
            <input type="text" class="form-control" id="bank_reference" name="bank_reference" placeholder="{% trans 'Bank transaction reference...' %}">
        </div>
        
        <div class="mb-3" id="check-details" style="display: none;">
            <label for="check_number" class="form-label">{% trans "Check Number" %}</label>
            <input type="text" class="form-control" id="check_number" name="check_number" placeholder="{% trans 'Check number...' %}">
        </div>
    </div>
</div>

<!-- Verification Section -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="bi bi-shield-check"></i>
            {% trans "Verification & Receipt" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="received_by" class="form-label">{% trans "Received By" %}</label>
                    <input type="text" class="form-control" id="received_by" name="received_by" placeholder="{% trans 'Staff member name...' %}" readonly>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="receipt_number" class="form-label">{% trans "Receipt Number" %}</label>
                    <input type="text" class="form-control" id="receipt_number" name="receipt_number" placeholder="{% trans 'Auto-generated...' %}" readonly>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="cash_verified" name="cash_verified" required>
                <label class="form-check-label" for="cash_verified">
                    {% trans "I confirm that the cash amount has been verified and counted" %} <span class="text-danger">*</span>
                </label>
            </div>
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="customer_id_verified" name="customer_id_verified">
                <label class="form-check-label" for="customer_id_verified">
                    {% trans "Customer identification has been verified" %}
                </label>
            </div>
        </div>
    </div>
</div>

<!-- Balance Update Information -->
<div class="alert alert-info">
    <i class="bi bi-info-circle"></i>
    <strong>{% trans "Note:" %}</strong> {% trans "This deposit will be added to the customer's account balance immediately upon approval." %}
</div>
{% endblock %}

{% block transaction_specific_js %}
<script src="/static/js/transactions/deposit.js"></script>
{% endblock %}
