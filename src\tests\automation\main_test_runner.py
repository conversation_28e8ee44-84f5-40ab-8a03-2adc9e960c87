"""
Arena Doviz Main Test Runner - Comprehensive Automated Testing System
"""
import asyncio
import json
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.progress import Progress, TaskID
from rich.table import Table
from rich.panel import Panel

try:
    from browser_automation import B<PERSON>er<PERSON>uto<PERSON>
    from system_monitor import SystemMonitor
    from test_scenarios import TestScenarioGenerator, TestScenarioExecutor
    from financial_verification import FinancialVerificationSystem
    from test_data_manager import TestDataManager
    from issue_detector import IssueDetector
    from production_tester import ProductionEnvironmentTester
    from config import TestConfig, LOGS_DIR, REPORTS_DIR
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are in the same directory")
    import sys
    sys.exit(1)

# Setup rich console and logging
console = Console()
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(console=console, rich_tracebacks=True)]
)
logger = logging.getLogger("arena_main_runner")

class ArenaTestRunner:
    """Main test runner for Arena Doviz automated testing system"""
    
    def __init__(self, test_type: str = 'full'):
        self.test_type = test_type
        self.start_time = datetime.now()
        self.test_results = {}
        self.components = {}
        
    async def run_tests(self) -> Dict[str, Any]:
        """Run the complete test suite"""
        console.print(Panel.fit(
            "[bold blue]Arena Doviz Automated Testing System[/bold blue]\n"
            f"Test Type: {self.test_type.upper()}\n"
            f"Environment: {TestConfig.ENVIRONMENT}\n"
            f"Base URL: {TestConfig.BASE_URL}",
            title="🚀 Starting Tests"
        ))
        
        try:
            if self.test_type == 'production':
                await self._run_production_tests()
            elif self.test_type == 'scenarios':
                await self._run_scenario_tests()
            elif self.test_type == 'financial':
                await self._run_financial_tests()
            elif self.test_type == 'full':
                await self._run_full_test_suite()
            else:
                raise ValueError(f"Unknown test type: {self.test_type}")
            
            # Generate comprehensive report
            await self._generate_final_report()
            
        except Exception as e:
            logger.error(f"💥 Test execution failed: {str(e)}")
            self.test_results['status'] = 'ERROR'
            self.test_results['error'] = str(e)
        
        return self.test_results
    
    async def _run_production_tests(self) -> None:
        """Run production environment tests"""
        logger.info("🏭 Running production environment tests...")
        
        production_tester = ProductionEnvironmentTester()
        result = await production_tester.run_production_tests()
        
        self.test_results['production'] = {
            'status': result.status,
            'duration_ms': result.duration_ms,
            'environment_info': result.environment_info,
            'service_health': result.service_health,
            'scenario_results': result.scenario_results,
            'performance_metrics': result.performance_metrics,
            'issues_detected': result.issues_detected,
            'financial_verification': result.financial_verification
        }
        
        # Export production report
        report_path = REPORTS_DIR / f"production_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        production_tester.export_production_report(report_path)
    
    async def _run_scenario_tests(self) -> None:
        """Run scenario-based tests"""
        logger.info("🧪 Running scenario tests...")
        
        # Initialize components
        browser_automation = BrowserAutomation()
        await browser_automation.setup()
        
        system_monitor = SystemMonitor()
        system_monitor.start_monitoring()
        
        test_data_manager = TestDataManager()
        scenario_executor = TestScenarioExecutor(browser_automation, system_monitor)
        
        try:
            # Seed test data
            await test_data_manager.seed_test_data()
            
            # Login
            login_success = await browser_automation.login()
            if not login_success:
                raise Exception("Failed to login")
            
            # Generate and execute scenarios
            scenario_generator = TestScenarioGenerator()
            all_scenarios = scenario_generator.get_all_scenarios()
            
            scenario_results = []
            with Progress() as progress:
                task = progress.add_task("Executing scenarios...", total=len(all_scenarios))
                
                for scenario in all_scenarios:
                    result = await scenario_executor.execute_scenario(scenario)
                    scenario_results.append(result)
                    progress.advance(task)
            
            # Get execution summary
            execution_summary = scenario_executor.get_execution_summary()
            
            self.test_results['scenarios'] = {
                'total_scenarios': len(all_scenarios),
                'execution_summary': execution_summary,
                'detailed_results': [asdict(r) for r in scenario_results]
            }
            
            # Cleanup
            await test_data_manager.cleanup_test_data()
            
        finally:
            await browser_automation.cleanup()
            system_monitor.stop_monitoring()
    
    async def _run_financial_tests(self) -> None:
        """Run financial verification tests"""
        logger.info("💰 Running financial verification tests...")
        
        financial_verifier = FinancialVerificationSystem()
        
        # Get recent transactions for verification
        # This would typically get transaction IDs from the database or API
        transaction_ids = []  # Placeholder - would be populated from actual data
        
        verification_results = []
        for transaction_id in transaction_ids:
            result = await financial_verifier.verify_transaction(transaction_id)
            verification_results.append(result)
        
        # Generate financial report
        financial_report = financial_verifier.generate_financial_report()
        
        self.test_results['financial'] = {
            'verification_results': [asdict(r) for r in verification_results],
            'financial_report': financial_report
        }
    
    async def _run_full_test_suite(self) -> None:
        """Run the complete test suite"""
        logger.info("🎯 Running full test suite...")
        
        # Run all test types
        await self._run_production_tests()
        await self._run_scenario_tests()
        await self._run_financial_tests()
        
        # Additional comprehensive analysis
        await self._run_issue_detection()
        await self._generate_qa_documentation()
    
    async def _run_issue_detection(self) -> None:
        """Run issue detection analysis"""
        logger.info("🔍 Running issue detection...")
        
        issue_detector = IssueDetector()
        
        # Collect logs from all components
        all_logs = []
        
        # Add logs from system monitor if available
        if 'scenarios' in self.test_results:
            # This would collect logs from the scenario execution
            pass
        
        # Analyze logs for issues
        detected_issues = await issue_detector.analyze_logs(all_logs)
        
        # Generate Q&A documentation
        qa_entries = await issue_detector.generate_qa_documentation()
        
        self.test_results['issue_detection'] = {
            'detected_issues': [asdict(issue) for issue in detected_issues],
            'qa_documentation': [asdict(qa) for qa in qa_entries]
        }
        
        # Export issues report
        issues_report_path = REPORTS_DIR / f"issues_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        issue_detector.export_issues_report(issues_report_path)
    
    async def _generate_qa_documentation(self) -> None:
        """Generate comprehensive Q&A documentation"""
        logger.info("📝 Generating Q&A documentation...")
        
        qa_document = {
            'generated_at': datetime.now().isoformat(),
            'test_execution_summary': self._get_test_execution_summary(),
            'common_issues_and_solutions': self._get_common_issues_and_solutions(),
            'api_testing_guide': self._get_api_testing_guide(),
            'troubleshooting_guide': self._get_troubleshooting_guide(),
            'performance_optimization_tips': self._get_performance_tips(),
            'security_considerations': self._get_security_considerations()
        }
        
        # Export Q&A documentation
        qa_path = REPORTS_DIR / f"qa_documentation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(qa_path, 'w') as f:
            json.dump(qa_document, f, indent=2, default=str)
        
        self.test_results['qa_documentation'] = qa_document
    
    def _get_test_execution_summary(self) -> Dict[str, Any]:
        """Get test execution summary"""
        return {
            'test_type': self.test_type,
            'start_time': self.start_time.isoformat(),
            'end_time': datetime.now().isoformat(),
            'duration_minutes': (datetime.now() - self.start_time).total_seconds() / 60,
            'environment': TestConfig.ENVIRONMENT,
            'base_url': TestConfig.BASE_URL,
            'components_tested': list(self.test_results.keys())
        }
    
    def _get_common_issues_and_solutions(self) -> List[Dict[str, str]]:
        """Get common issues and their solutions"""
        return [
            {
                'issue': 'Commission amount validation error',
                'description': 'Form submission fails with commission_amount validation error',
                'solution': 'Ensure commission_amount field is properly validated as decimal or null',
                'code_fix': 'Add parseFloat() validation and null handling in form submission'
            },
            {
                'issue': 'Customer dropdown not loading',
                'description': 'Customer dropdown shows empty or fails to load options',
                'solution': 'Check customer API endpoint and authentication headers',
                'code_fix': 'Implement proper error handling and loading states for dropdowns'
            },
            {
                'issue': 'Exchange rate API 500 error',
                'description': 'Exchange rate API returns 500 Internal Server Error',
                'solution': 'Fix database query compatibility issues (PostgreSQL vs SQLite)',
                'code_fix': 'Replace distinct() with subquery approach for SQLite compatibility'
            },
            {
                'issue': 'Balance calculation incorrect',
                'description': 'Customer balance shows incorrect values after transactions',
                'solution': 'Verify field names in API responses and JavaScript code',
                'code_fix': 'Use currency_code instead of currency_id, balance instead of amount'
            },
            {
                'issue': 'JavaScript errors in console',
                'description': 'Browser console shows JavaScript errors during form interactions',
                'solution': 'Add proper null checks and error handling in JavaScript code',
                'code_fix': 'Implement try-catch blocks and validate data before processing'
            }
        ]
    
    def _get_api_testing_guide(self) -> Dict[str, Any]:
        """Get API testing guide"""
        return {
            'authentication': {
                'method': 'JWT Bearer Token',
                'endpoint': '/api/v1/auth/token/',
                'headers': {'Content-Type': 'application/json'},
                'example': {
                    'request': {'username': 'admin', 'password': 'password'},
                    'response': {'access': 'jwt_token_here', 'refresh': 'refresh_token_here'}
                }
            },
            'key_endpoints': [
                {
                    'name': 'Customer List',
                    'url': '/api/v1/customers/customers/',
                    'method': 'GET',
                    'description': 'Get list of customers with search support'
                },
                {
                    'name': 'Customer Balance',
                    'url': '/api/v1/customers/customers/{id}/balance/',
                    'method': 'GET',
                    'description': 'Get customer balance for all currencies'
                },
                {
                    'name': 'Exchange Rates',
                    'url': '/api/v1/currencies/rates/current/',
                    'method': 'GET',
                    'description': 'Get current exchange rates for location'
                },
                {
                    'name': 'Create Transaction',
                    'url': '/api/v1/transactions/transactions/',
                    'method': 'POST',
                    'description': 'Create new transaction'
                }
            ],
            'testing_tips': [
                'Always include Authorization header with valid JWT token',
                'Use proper Content-Type: application/json for POST requests',
                'Check response status codes: 200 (success), 400 (validation error), 401 (unauthorized)',
                'Validate response data structure matches expected format',
                'Test with various input combinations including edge cases'
            ]
        }
    
    def _get_troubleshooting_guide(self) -> Dict[str, List[str]]:
        """Get troubleshooting guide"""
        return {
            'form_submission_issues': [
                'Check browser console for JavaScript errors',
                'Verify all required fields are filled',
                'Ensure CSRF token is included in form data',
                'Check network tab for API call failures',
                'Validate form data format and types'
            ],
            'dropdown_loading_issues': [
                'Check API endpoint accessibility',
                'Verify authentication token validity',
                'Check for CORS issues in browser console',
                'Ensure proper error handling for failed requests',
                'Validate dropdown initialization code'
            ],
            'balance_calculation_issues': [
                'Verify customer balance API response format',
                'Check for proper decimal precision handling',
                'Ensure transaction amounts are validated correctly',
                'Verify double-entry bookkeeping implementation',
                'Check for race conditions in balance updates'
            ],
            'performance_issues': [
                'Monitor database query execution times',
                'Check for N+1 query problems',
                'Optimize API response sizes',
                'Implement proper caching strategies',
                'Monitor memory usage and garbage collection'
            ]
        }
    
    def _get_performance_tips(self) -> List[str]:
        """Get performance optimization tips"""
        return [
            'Use database indexes on frequently queried fields',
            'Implement API response caching for static data',
            'Optimize database queries to avoid N+1 problems',
            'Use pagination for large data sets',
            'Implement lazy loading for dropdown options',
            'Minimize JavaScript bundle sizes',
            'Use CDN for static assets',
            'Implement proper error handling to avoid retries',
            'Monitor and optimize database connection pooling',
            'Use asynchronous processing for heavy operations'
        ]
    
    def _get_security_considerations(self) -> List[str]:
        """Get security considerations"""
        return [
            'Always validate and sanitize user input',
            'Use HTTPS for all communications',
            'Implement proper JWT token expiration and refresh',
            'Validate user permissions for each operation',
            'Use CSRF protection for form submissions',
            'Implement rate limiting for API endpoints',
            'Log security events for audit purposes',
            'Use parameterized queries to prevent SQL injection',
            'Implement proper session management',
            'Regular security updates and dependency scanning'
        ]
    
    async def _generate_final_report(self) -> None:
        """Generate comprehensive final report"""
        logger.info("📊 Generating final comprehensive report...")
        
        # Calculate overall statistics
        end_time = datetime.now()
        total_duration = end_time - self.start_time
        
        # Determine overall status
        overall_status = self._determine_overall_status()
        
        final_report = {
            'report_metadata': {
                'generated_at': end_time.isoformat(),
                'test_type': self.test_type,
                'environment': TestConfig.ENVIRONMENT,
                'base_url': TestConfig.BASE_URL,
                'total_duration_minutes': total_duration.total_seconds() / 60,
                'overall_status': overall_status
            },
            'executive_summary': self._generate_executive_summary(),
            'detailed_results': self.test_results,
            'recommendations': self._generate_recommendations(),
            'next_steps': self._generate_next_steps()
        }
        
        # Export final report
        final_report_path = REPORTS_DIR / f"arena_doviz_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(final_report_path, 'w') as f:
            json.dump(final_report, f, indent=2, default=str)
        
        # Display summary table
        self._display_summary_table(final_report)
        
        console.print(Panel.fit(
            f"[bold green]Testing completed successfully![/bold green]\n"
            f"Overall Status: {overall_status}\n"
            f"Duration: {total_duration.total_seconds() / 60:.1f} minutes\n"
            f"Report saved to: {final_report_path}",
            title="✅ Test Execution Complete"
        ))
    
    def _determine_overall_status(self) -> str:
        """Determine overall test status"""
        if 'production' in self.test_results:
            prod_status = self.test_results['production'].get('status', 'UNKNOWN')
            if prod_status == 'FAIL':
                return 'FAIL'
        
        if 'scenarios' in self.test_results:
            scenario_summary = self.test_results['scenarios'].get('execution_summary', {})
            success_rate = scenario_summary.get('success_rate', 0)
            if success_rate < 80:  # Less than 80% success rate
                return 'FAIL'
        
        return 'PASS'
    
    def _generate_executive_summary(self) -> Dict[str, Any]:
        """Generate executive summary"""
        summary = {
            'test_scope': f'{self.test_type.title()} testing of Arena Doviz transaction system',
            'environment_tested': TestConfig.ENVIRONMENT,
            'key_findings': [],
            'critical_issues': 0,
            'recommendations_count': 0
        }
        
        # Analyze results for key findings
        if 'production' in self.test_results:
            prod_result = self.test_results['production']
            if prod_result.get('status') == 'PASS':
                summary['key_findings'].append('Production environment is stable and accessible')
            else:
                summary['key_findings'].append('Production environment has critical issues')
                summary['critical_issues'] += 1
        
        if 'scenarios' in self.test_results:
            scenario_summary = self.test_results['scenarios'].get('execution_summary', {})
            success_rate = scenario_summary.get('success_rate', 0)
            summary['key_findings'].append(f'Transaction scenarios have {success_rate:.1f}% success rate')
        
        if 'issue_detection' in self.test_results:
            issues = self.test_results['issue_detection'].get('detected_issues', [])
            critical_issues = [i for i in issues if i.get('severity') == 'CRITICAL']
            summary['critical_issues'] += len(critical_issues)
        
        return summary
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Add recommendations based on test results
        if 'production' in self.test_results:
            prod_result = self.test_results['production']
            if prod_result.get('status') == 'FAIL':
                recommendations.append('Address production environment issues before deployment')
        
        if 'scenarios' in self.test_results:
            scenario_summary = self.test_results['scenarios'].get('execution_summary', {})
            if scenario_summary.get('success_rate', 100) < 95:
                recommendations.append('Improve transaction form validation and error handling')
        
        if 'issue_detection' in self.test_results:
            issues = self.test_results['issue_detection'].get('detected_issues', [])
            if issues:
                recommendations.append(f'Address {len(issues)} detected issues in order of severity')
        
        # Add general recommendations
        recommendations.extend([
            'Implement comprehensive monitoring and alerting',
            'Set up automated testing in CI/CD pipeline',
            'Regular security audits and penetration testing',
            'Performance optimization and load testing',
            'User acceptance testing with real scenarios'
        ])
        
        return recommendations
    
    def _generate_next_steps(self) -> List[str]:
        """Generate next steps"""
        return [
            'Review and prioritize identified issues',
            'Implement recommended fixes and improvements',
            'Set up continuous monitoring and alerting',
            'Schedule regular automated testing runs',
            'Plan user acceptance testing sessions',
            'Document operational procedures and runbooks',
            'Train support team on common issues and solutions',
            'Establish incident response procedures'
        ]
    
    def _display_summary_table(self, report: Dict[str, Any]) -> None:
        """Display summary table"""
        table = Table(title="Arena Doviz Test Results Summary")
        
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="magenta")
        table.add_column("Details", style="green")
        
        # Add rows based on test results
        if 'production' in self.test_results:
            prod_status = self.test_results['production'].get('status', 'UNKNOWN')
            table.add_row("Production Environment", prod_status, "Environment accessibility and health")
        
        if 'scenarios' in self.test_results:
            scenario_summary = self.test_results['scenarios'].get('execution_summary', {})
            success_rate = scenario_summary.get('success_rate', 0)
            status = "PASS" if success_rate >= 80 else "FAIL"
            table.add_row("Transaction Scenarios", status, f"{success_rate:.1f}% success rate")
        
        if 'financial' in self.test_results:
            table.add_row("Financial Verification", "COMPLETED", "Transaction accuracy verification")
        
        if 'issue_detection' in self.test_results:
            issues = self.test_results['issue_detection'].get('detected_issues', [])
            status = "CLEAN" if len(issues) == 0 else f"{len(issues)} ISSUES"
            table.add_row("Issue Detection", status, "Automated issue analysis")
        
        console.print(table)

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Arena Doviz Automated Testing System')
    parser.add_argument('--test-type', choices=['full', 'production', 'scenarios', 'financial'], 
                       default='full', help='Type of tests to run')
    parser.add_argument('--environment', choices=['production', 'local'], 
                       default='production', help='Environment to test')
    parser.add_argument('--headless', action='store_true', help='Run browser tests in headless mode')
    
    args = parser.parse_args()
    
    # Configure test environment
    TestConfig.ENVIRONMENT = args.environment
    TestConfig.HEADLESS = args.headless
    
    # Run tests
    test_runner = ArenaTestRunner(test_type=args.test_type)
    results = await test_runner.run_tests()
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
