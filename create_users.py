#!/usr/bin/env python3
"""
Create test users for Arena Doviz
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from apps.locations.models import Location

User = get_user_model()

def create_users():
    print('🔧 Creating Arena Doviz Test User Accounts...')
    print('=' * 60)
    
    # Get locations
    dubai = Location.objects.filter(code='DXB').first()
    istanbul = Location.objects.filter(code='IST').first()
    
    # Create admin user
    admin_user, created = User.objects.get_or_create(
        username='admin_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'User',
            'role': 'admin',
            'is_staff': True,
            'is_superuser': True
        }
    )
    admin_user.set_password('Admin123!@#')
    admin_user.save()
    
    try:
        admin_group = Group.objects.get(name='Admin')
        admin_user.groups.clear()
        admin_user.groups.add(admin_group)
    except Group.DoesNotExist:
        pass
    
    print('✅ Created admin_test user')
    
    # Create accountant user
    accountant_user, created = User.objects.get_or_create(
        username='accountant_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Sarah',
            'last_name': 'Johnson',
            'role': 'accountant',
            'location': dubai,
            'is_staff': True,
            'is_superuser': False
        }
    )
    accountant_user.set_password('Account123!@#')
    accountant_user.save()
    
    try:
        accountant_group = Group.objects.get(name='Accountant')
        accountant_user.groups.clear()
        accountant_user.groups.add(accountant_group)
    except Group.DoesNotExist:
        pass
    
    print('✅ Created accountant_test user')
    
    # Create branch employee user
    employee_user, created = User.objects.get_or_create(
        username='branch_employee_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Ahmed',
            'last_name': 'Hassan',
            'role': 'branch_employee',
            'location': dubai,
            'is_staff': False,
            'is_superuser': False
        }
    )
    employee_user.set_password('Branch123!@#')
    employee_user.save()
    
    try:
        employee_group = Group.objects.get(name='Branch Employee')
        employee_user.groups.clear()
        employee_user.groups.add(employee_group)
    except Group.DoesNotExist:
        pass
    
    print('✅ Created branch_employee_test user')
    
    # Create viewer user
    viewer_user, created = User.objects.get_or_create(
        username='viewer_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Maria',
            'last_name': 'Garcia',
            'role': 'viewer',
            'location': istanbul,
            'is_staff': False,
            'is_superuser': False
        }
    )
    viewer_user.set_password('Viewer123!@#')
    viewer_user.save()
    
    try:
        viewer_group = Group.objects.get(name='Viewer')
        viewer_user.groups.clear()
        viewer_user.groups.add(viewer_group)
    except Group.DoesNotExist:
        pass
    
    print('✅ Created viewer_test user')
    
    # Create courier user
    courier_user, created = User.objects.get_or_create(
        username='courier_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Smith',
            'role': 'courier',
            'location': dubai,
            'is_staff': False,
            'is_superuser': False
        }
    )
    courier_user.set_password('Courier123!@#')
    courier_user.save()
    
    try:
        courier_group = Group.objects.get(name='Courier')
        courier_user.groups.clear()
        courier_user.groups.add(courier_group)
    except Group.DoesNotExist:
        pass
    
    print('✅ Created courier_test user')
    
    print()
    print('=' * 60)
    print('🔑 ARENA DOVIZ TEST USER CREDENTIALS')
    print('=' * 60)
    
    users = [
        ('admin_test', 'Admin123!@#', 'admin', 'System Administrator - Full access'),
        ('accountant_test', 'Account123!@#', 'accountant', 'Senior Accountant - Financial operations'),
        ('branch_employee_test', 'Branch123!@#', 'branch_employee', 'Branch Employee - Daily transactions'),
        ('viewer_test', 'Viewer123!@#', 'viewer', 'Viewer - Read-only access'),
        ('courier_test', 'Courier123!@#', 'courier', 'Courier - Cash pickup and delivery')
    ]
    
    for username, password, role, description in users:
        print(f'')
        print(f'👤 {role.upper()} USER:')
        print(f'   Username: {username}')
        print(f'   Password: {password}')
        print(f'   Role: {role}')
        print(f'   Description: {description}')
    
    print(f'')
    print(f'📊 Summary: 5 test users created/updated successfully')
    print()
    print('🔗 Login URL: http://127.0.0.1:8000/accounts/login/')
    print('🏠 Home URL: http://127.0.0.1:8000/')
    print('📊 Dashboard URL: http://127.0.0.1:8000/')
    print('🔍 Monitoring URL: http://127.0.0.1:8000/monitoring/')

if __name__ == '__main__':
    create_users()
