{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "New Transaction" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-plus-circle"></i>
                {% trans "New Transaction" %}
            </h1>
            <a href="{% url 'transactions_web:list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {% trans "Back to List" %}
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-form"></i>
                    {% trans "Transaction Details" %}
                </h5>
            </div>
            <div class="card-body">
                <form id="transaction-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="transaction_type" class="form-label">{% trans "Transaction Type" %} <span class="text-danger">*</span></label>
                                <select class="form-select" id="transaction_type" name="transaction_type" required>
                                    <option value="">{% trans "Select transaction type..." %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer" class="form-label">{% trans "Customer" %} <span class="text-danger">*</span></label>
                                <select class="form-select" id="customer" name="customer" required>
                                    <option value="">{% trans "Select customer..." %}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">{% trans "Location" %} <span class="text-danger">*</span></label>
                                <select class="form-select" id="location" name="location" required>
                                    <option value="">{% trans "Select location..." %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reference_number" class="form-label">{% trans "Reference Number" %}</label>
                                <input type="text" class="form-control" id="reference_number" name="reference_number" placeholder="{% trans 'External reference number...' %}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">{% trans "Description" %} <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="3" required placeholder="{% trans 'Transaction description...' %}"></textarea>
                    </div>
                    
                    <!-- Currency Exchange Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">{% trans "Currency Exchange" %}</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="from_currency" class="form-label">{% trans "From Currency" %} <span class="text-danger">*</span></label>
                                        <select class="form-select" id="from_currency" name="from_currency" required>
                                            <option value="">{% trans "Select currency..." %}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="from_amount" class="form-label">{% trans "From Amount" %} <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="from_amount" name="from_amount" step="0.000001" required placeholder="0.00">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="exchange_rate" class="form-label">{% trans "Exchange Rate" %} <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" step="0.000001" required placeholder="0.000000">
                                            <button class="btn btn-outline-secondary" type="button" id="get-current-rate">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="to_currency" class="form-label">{% trans "To Currency" %} <span class="text-danger">*</span></label>
                                        <select class="form-select" id="to_currency" name="to_currency" required>
                                            <option value="">{% trans "Select currency..." %}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="to_amount" class="form-label">{% trans "To Amount" %} <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="to_amount" name="to_amount" step="0.000001" required placeholder="0.00" readonly>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="commission_amount" class="form-label">
                                            {% trans "Commission Amount" %}
                                            <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="calculateCommission()">
                                                <i class="bi bi-calculator"></i> {% trans "Auto Calculate" %}
                                            </button>
                                        </label>
                                        <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.000001" placeholder="0.00">
                                        <div class="form-text" id="commission_info"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delivery Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">{% trans "Delivery Information" %}</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="delivery_method" class="form-label">{% trans "Delivery Method" %}</label>
                                        <select class="form-select" id="delivery_method" name="delivery_method">
                                            <option value="">{% trans "Select delivery method..." %}</option>
                                            <option value="in_person">{% trans "In Person" %}</option>
                                            <option value="courier">{% trans "Courier" %}</option>
                                            <option value="bank_transfer">{% trans "Bank Transfer" %}</option>
                                            <option value="swift">{% trans "SWIFT Transfer" %}</option>
                                            <option value="cash">{% trans "Cash" %}</option>
                                            <option value="internal">{% trans "Internal Transfer" %}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="courier" class="form-label">{% trans "Courier" %}</label>
                                        <div class="input-group">
                                            <select class="form-select" id="courier_select" disabled>
                                                <option value="">{% trans "Select existing courier..." %}</option>
                                            </select>
                                            <input type="text" class="form-control" id="courier_manual" placeholder="{% trans 'Or enter courier name...' %}" disabled style="display: none;">
                                            <input type="hidden" id="courier_hidden" name="courier" value="">
                                            <button class="btn btn-outline-secondary" type="button" id="toggle_courier_input" disabled>
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">{% trans "Select from existing couriers or enter a new name" %}</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="delivery_address" class="form-label">{% trans "Delivery Address" %}</label>
                                <textarea class="form-control" id="delivery_address" name="delivery_address" rows="2" placeholder="{% trans 'Delivery address...' %}"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="delivery_notes" class="form-label">{% trans "Delivery Notes" %}</label>
                                <textarea class="form-control" id="delivery_notes" name="delivery_notes" rows="2" placeholder="{% trans 'Special delivery instructions...' %}"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Multi-step Transaction -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_multi_step" name="is_multi_step">
                                <label class="form-check-label" for="is_multi_step">
                                    {% trans "Multi-step Transaction" %}
                                </label>
                            </div>
                        </div>
                        <div class="card-body" id="multi-step-section" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="step_number" class="form-label">{% trans "Step Number" %}</label>
                                        <input type="number" class="form-control" id="step_number" name="step_number" min="1" value="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="total_steps" class="form-label">{% trans "Total Steps" %}</label>
                                        <input type="number" class="form-control" id="total_steps" name="total_steps" min="1" value="1">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Document Upload Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-file-earmark-arrow-up"></i>
                                {% trans "Document Upload" %}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="document_files" class="form-label">{% trans "Upload Documents" %}</label>
                                <input type="file" class="form-control" id="document_files" name="document_files" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx">
                                <div class="form-text">
                                    {% trans "Supported formats: PDF, JPG, PNG, DOC, DOCX, XLS, XLSX. Max 10MB per file." %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="document_type" class="form-label">{% trans "Document Type" %}</label>
                                <select class="form-select" id="document_type" name="document_type">
                                    <option value="receipt">{% trans "Receipt" %}</option>
                                    <option value="invoice">{% trans "Invoice" %}</option>
                                    <option value="bank_slip">{% trans "Bank Slip" %}</option>
                                    <option value="customer_id">{% trans "Customer ID Copy" %}</option>
                                    <option value="contract">{% trans "Contract" %}</option>
                                    <option value="other" selected>{% trans "Other" %}</option>
                                </select>
                            </div>

                            <div id="uploaded-files-preview" class="mt-3" style="display: none;">
                                <h6>{% trans "Selected Files:" %}</h6>
                                <div id="file-list" class="list-group">
                                    <!-- File list will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "Additional Notes" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="{% trans 'Additional notes about this transaction...' %}"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                            <i class="bi bi-x-circle"></i>
                            {% trans "Cancel" %}
                        </button>
                        <div>
                            <button type="submit" class="btn btn-primary me-2" data-action="save">
                                <i class="bi bi-save"></i>
                                {% trans "Save as Draft" %}
                            </button>
                            <button type="submit" class="btn btn-success" data-action="submit">
                                <i class="bi bi-send"></i>
                                {% trans "Submit for Approval" %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Customer Balance Card -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-wallet2"></i>
                    {% trans "Customer Balance" %}
                </h6>
            </div>
            <div class="card-body" id="customer-balance">
                <p class="text-muted">{% trans "Select a customer to view balance" %}</p>
            </div>
        </div>
        
        <!-- Exchange Rate Info -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-currency-exchange"></i>
                    {% trans "Current Exchange Rates" %}
                </h6>
            </div>
            <div class="card-body" id="current-rates">
                <p class="text-muted">{% trans "Select currencies to view rates" %}</p>
            </div>
        </div>
        
        <!-- Transaction Preview -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-eye"></i>
                    {% trans "Transaction Preview" %}
                </h6>
            </div>
            <div class="card-body" id="transaction-preview">
                <p class="text-muted">{% trans "Fill in the form to see preview" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Authentication utility functions
function getAuthToken() {
    // Get JWT access token from localStorage
    return localStorage.getItem('arena_access_token') || '';
}

function getAuthHeaders() {
    const token = getAuthToken();
    const headers = {
        'Content-Type': 'application/json'
    };

    // Add JWT token if available
    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    // Add CSRF token for session authentication
    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

$(document).ready(function() {
    // Load form data
    loadTransactionTypes();
    loadCustomers();
    loadLocations();
    loadCurrencies();
    
    // Event handlers
    $('#customer').on('change', function() {
        loadCustomerBalance();
    });
    
    $('#from_currency, #to_currency, #location').on('change', function() {
        loadCurrentRates();
    });
    
    $('#from_amount, #exchange_rate').on('input', function() {
        calculateToAmount();
        updateTransactionPreview();
    });
    
    $('#delivery_method').on('change', function() {
        toggleCourierField();
    });

    $('#toggle_courier_input').on('click', function() {
        toggleCourierInputMode();
    });

    $('#courier_select').on('change', function() {
        const selectedValue = $(this).val();
        const selectedText = $(this).find('option:selected').text();

        if (selectedValue) {
            $('#courier_manual').val(selectedText);
            $('#courier_hidden').val(selectedValue);  // Set the UUID for backend
        } else {
            $('#courier_manual').val('');
            $('#courier_hidden').val('');
        }
    });
    
    $('#is_multi_step').on('change', function() {
        toggleMultiStepSection();
    });
    
    $('#get-current-rate').on('click', function() {
        getCurrentExchangeRate();
    });
    
    $('#transaction-form').on('submit', function(e) {
        e.preventDefault();

        // Get the action from the submitter button or default to 'save'
        let action = 'save';
        if (e.submitter && e.submitter.dataset && e.submitter.dataset.action) {
            action = e.submitter.dataset.action;
        } else if (e.originalEvent && e.originalEvent.submitter && e.originalEvent.submitter.dataset) {
            action = e.originalEvent.submitter.dataset.action;
        }

        submitTransaction(action);
    });

    // File upload handling
    $('#document_files').on('change', function() {
        handleFileSelection();
    });
    
    // Form validation and preview updates
    $('#transaction-form input, #transaction-form select, #transaction-form textarea').on('change input', function() {
        updateTransactionPreview();
    });
});

function loadTransactionTypes() {
    $.ajax({
        url: '/api/v1/transactions/types/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#transaction_type');
            if (data.results && data.results.length > 0) {
                data.results.forEach(function(type) {
                    select.append(`<option value="${type.id}">${type.name}</option>`);
                });
            } else {
                select.append('<option value="" disabled>{% trans "No transaction types available" %}</option>');
                console.warn('No transaction types found');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading transaction types:', error);
            const select = $('#transaction_type');
            select.append('<option value="" disabled>{% trans "Error loading transaction types" %}</option>');
        }
    });
}

function loadCustomers() {
    $.ajax({
        url: '/api/v1/customers/customers/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#customer');
            if (data.results && data.results.length > 0) {
                data.results.forEach(function(customer) {
                    const displayName = customer.display_name ||
                                      (customer.first_name && customer.last_name ?
                                       `${customer.first_name} ${customer.last_name}` :
                                       customer.company_name || customer.customer_code);
                    select.append(`<option value="${customer.id}">${displayName}</option>`);
                });
            } else {
                select.append('<option value="" disabled>{% trans "No customers available" %}</option>');
                console.warn('No customers found');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading customers:', error);
            const select = $('#customer');
            select.append('<option value="" disabled>{% trans "Error loading customers" %}</option>');
        }
    });
}

function loadLocations() {
    $.ajax({
        url: '/api/v1/locations/locations/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#location');
            if (data.results) {
                data.results.forEach(function(location) {
                    select.append(`<option value="${location.id}" data-code="${location.code}">${location.name}</option>`);
                });
            }
        }
    });
}

function loadCurrencies() {
    $.ajax({
        url: '/api/v1/currencies/currencies/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const fromSelect = $('#from_currency');
            const toSelect = $('#to_currency');
            
            if (data.results) {
                data.results.forEach(function(currency) {
                    const option = `<option value="${currency.id}">${currency.code} - ${currency.name}</option>`;
                    fromSelect.append(option);
                    toSelect.append(option);
                });
            }
        }
    });
}

function loadCustomerBalance() {
    const customerId = $('#customer').val();
    if (!customerId) {
        $('#customer-balance').html('<p class="text-muted">{% trans "Select a customer to view balance" %}</p>');
        return;
    }
    
    $.ajax({
        url: `/api/v1/transactions/balance-entries/balances/?customer=${customerId}`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            let html = '';
            if (data.length > 0) {
                data.forEach(function(balance) {
                    const balanceClass = balance.balance >= 0 ? 'text-success' : 'text-danger';
                    html += `
                        <div class="d-flex justify-content-between mb-2">
                            <span>${balance.currency} (${balance.location})</span>
                            <span class="${balanceClass}">${balance.formatted_balance}</span>
                        </div>
                    `;
                });
            } else {
                html = '<p class="text-muted">{% trans "No balance information available" %}</p>';
            }
            $('#customer-balance').html(html);
        }
    });
}

function calculateToAmount() {
    const fromAmount = parseFloat($('#from_amount').val()) || 0;
    const exchangeRate = parseFloat($('#exchange_rate').val()) || 0;
    
    if (fromAmount > 0 && exchangeRate > 0) {
        const toAmount = fromAmount * exchangeRate;
        $('#to_amount').val(toAmount.toFixed(6));
    } else {
        $('#to_amount').val('');
    }
}

function loadCouriers() {
    const courierSelect = $('#courier_select');

    // Clear existing options except the first one
    courierSelect.find('option:not(:first)').remove();

    // Add loading indicator
    courierSelect.append('<option value="">{% trans "Loading..." %}</option>');

    $.ajax({
        url: '/api/v1/accounts/users/',
        method: 'GET',
        headers: getAuthHeaders(),
        data: {
            role: 'courier',
            is_active: true
        },
        success: function(data) {
            // Clear loading option
            courierSelect.find('option:not(:first)').remove();

            if (data.results && data.results.length > 0) {
                data.results.forEach(function(courier) {
                    const displayName = courier.first_name && courier.last_name
                        ? `${courier.first_name} ${courier.last_name}`
                        : courier.username;
                    courierSelect.append(`<option value="${courier.id}">${displayName}</option>`);
                });
            } else {
                courierSelect.append('<option value="">{% trans "No couriers available" %}</option>');
            }
        },
        error: function(xhr) {
            console.error('Error loading couriers:', xhr);
            courierSelect.find('option:not(:first)').remove();
            courierSelect.append('<option value="">{% trans "Error loading couriers" %}</option>');
        }
    });
}

function toggleCourierField() {
    const deliveryMethod = $('#delivery_method').val();
    const courierSelect = $('#courier_select');
    const courierManual = $('#courier_manual');
    const courierHidden = $('#courier_hidden');
    const toggleButton = $('#toggle_courier_input');

    if (deliveryMethod === 'courier') {
        courierSelect.prop('disabled', false);
        courierManual.prop('disabled', false);
        toggleButton.prop('disabled', false);
        loadCouriers();
    } else {
        courierSelect.prop('disabled', true);
        courierManual.prop('disabled', true);
        toggleButton.prop('disabled', true);
        courierSelect.val('');
        courierManual.val('');
        courierHidden.val('');  // Clear hidden field as well
    }
}

function toggleCourierInputMode() {
    const courierSelect = $('#courier_select');
    const courierManual = $('#courier_manual');
    const toggleButton = $('#toggle_courier_input');

    if (courierSelect.is(':visible')) {
        // Switch to manual input
        courierSelect.hide();
        courierManual.show().focus();
        toggleButton.html('<i class="fas fa-list"></i>');
        toggleButton.attr('title', '{% trans "Switch to dropdown" %}');
    } else {
        // Switch to dropdown
        courierManual.hide();
        courierSelect.show();
        toggleButton.html('<i class="fas fa-edit"></i>');
        toggleButton.attr('title', '{% trans "Switch to manual input" %}');
    }
}

function toggleMultiStepSection() {
    const isMultiStep = $('#is_multi_step').is(':checked');
    const section = $('#multi-step-section');

    if (isMultiStep) {
        section.show();
    } else {
        section.hide();
        $('#step_number').val(1);
        $('#total_steps').val(1);
    }
}

function loadCurrentRates() {
    const fromCurrency = $('#from_currency').val();
    const toCurrency = $('#to_currency').val();
    const location = $('#location').val();

    if (!fromCurrency || !toCurrency || fromCurrency === toCurrency) {
        $('#current-rates').html('<p class="text-muted">{% trans "Select different currencies to view rates" %}</p>');
        return;
    }

    $('#current-rates').html('<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> {% trans "Loading rates..." %}</div>');

    // Get the codes instead of IDs for the API call
    const fromCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
    const toCurrencyCode = $('#to_currency option:selected').text().split(' - ')[0];
    const locationCode = $('#location option:selected').data('code');

    // Don't make API call if location is not selected
    if (!locationCode || locationCode === '' || $('#location').val() === '') {
        $('#current-rates').html('<p class="text-muted">{% trans "Please select a location to view rates" %}</p>');
        return;
    }

    $.ajax({
        url: '/api/v1/currencies/rates/',
        method: 'GET',
        headers: getAuthHeaders(),
        data: {
            from_currency: fromCurrencyCode,
            to_currency: toCurrencyCode,
            location: locationCode,
            current_only: 'true'
        },
        success: function(data) {
            let ratesHtml = '<div class="row">';

            if (data.results && data.results.length > 0) {
                data.results.forEach(function(rate) {
                    ratesHtml += `
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                                <span class="fw-bold">${rate.from_currency_code} → ${rate.to_currency_code}</span>
                                <div>
                                    <span class="badge bg-success me-1">Buy: ${rate.buy_rate}</span>
                                    <span class="badge bg-primary">Sell: ${rate.sell_rate}</span>
                                </div>
                            </div>
                        </div>
                    `;
                });
            } else {
                ratesHtml += '<div class="col-12"><p class="text-muted mb-0">{% trans "No current rates available" %}</p></div>';
            }

            ratesHtml += '</div>';
            $('#current-rates').html(ratesHtml);
        },
        error: function(xhr) {
            console.error('Error loading rates:', xhr);
            $('#current-rates').html('<p class="text-danger">{% trans "Failed to load current rates" %}</p>');
        }
    });
}

function getCurrentExchangeRate() {
    const fromCurrency = $('#from_currency').val();
    const toCurrency = $('#to_currency').val();
    const location = $('#location').val();

    if (!fromCurrency || !toCurrency || fromCurrency === toCurrency) {
        showAlert('warning', '{% trans "Please select different currencies" %}');
        return;
    }

    const button = $('#get-current-rate');
    const originalHtml = button.html();
    button.html('<i class="spinner-border spinner-border-sm"></i>').prop('disabled', true);

    // Get the codes instead of IDs for the API call
    const fromCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
    const toCurrencyCode = $('#to_currency option:selected').text().split(' - ')[0];
    const locationCode = $('#location option:selected').data('code');

    // Don't make API call if location is not selected
    if (!locationCode || locationCode === '' || $('#location').val() === '') {
        showAlert('warning', '{% trans "Please select a location first" %}');
        button.html(originalHtml).prop('disabled', false);
        return;
    }

    $.ajax({
        url: '/api/v1/currencies/rates/',
        method: 'GET',
        headers: getAuthHeaders(),
        data: {
            from_currency: fromCurrencyCode,
            to_currency: toCurrencyCode,
            location: locationCode,
            current_only: 'true'
        },
        success: function(data) {
            if (data.results && data.results.length > 0) {
                const rate = data.results[0].sell_rate; // Use sell_rate as default
                $('#exchange_rate').val(rate);
                calculateToAmount();
                updateTransactionPreview();
                showAlert('success', '{% trans "Exchange rate updated successfully" %}');
            } else {
                showAlert('warning', '{% trans "No exchange rate found for selected currencies" %}');
            }
        },
        error: function(xhr) {
            console.error('Error getting exchange rate:', xhr);
            showAlert('danger', '{% trans "Failed to get current exchange rate" %}');
        },
        complete: function() {
            button.html(originalHtml).prop('disabled', false);
        }
    });
}

function updateTransactionPreview() {
    const formData = new FormData($('#transaction-form')[0]);
    let preview = '<div class="small">';
    
    const customer = $('#customer option:selected').text();
    const fromCurrency = $('#from_currency option:selected').text();
    const toCurrency = $('#to_currency option:selected').text();
    const fromAmount = $('#from_amount').val();
    const toAmount = $('#to_amount').val();
    
    if (customer && customer !== '{% trans "Select customer..." %}') {
        preview += `<strong>{% trans "Customer" %}:</strong> ${customer}<br>`;
    }
    
    if (fromAmount && toAmount && fromCurrency && toCurrency) {
        preview += `<strong>{% trans "Exchange" %}:</strong> ${fromAmount} ${fromCurrency.split(' - ')[0]} → ${toAmount} ${toCurrency.split(' - ')[0]}<br>`;
    }
    
    const deliveryMethod = $('#delivery_method option:selected').text();
    if (deliveryMethod && deliveryMethod !== '{% trans "Select delivery method..." %}') {
        preview += `<strong>{% trans "Delivery" %}:</strong> ${deliveryMethod}<br>`;
    }
    
    preview += '</div>';
    
    if (preview === '<div class="small"></div>') {
        preview = '<p class="text-muted">{% trans "Fill in the form to see preview" %}</p>';
    }
    
    $('#transaction-preview').html(preview);
}

function handleFileSelection() {
    const files = $('#document_files')[0].files;
    const fileList = $('#file-list');
    const preview = $('#uploaded-files-preview');

    fileList.empty();

    if (files.length > 0) {
        preview.show();

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const fileSize = (file.size / 1024 / 1024).toFixed(2);

            const fileItem = `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-file-earmark"></i>
                        <strong>${file.name}</strong>
                        <small class="text-muted">(${fileSize} MB)</small>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${i})">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;
            fileList.append(fileItem);
        }
    } else {
        preview.hide();
    }
}

function removeFile(index) {
    const input = $('#document_files')[0];
    const dt = new DataTransfer();

    for (let i = 0; i < input.files.length; i++) {
        if (i !== index) {
            dt.items.add(input.files[i]);
        }
    }

    input.files = dt.files;
    handleFileSelection();
}

function submitTransaction(action) {
    const formData = new FormData($('#transaction-form')[0]);
    const data = {};

    // Extract non-file data
    for (let [key, value] of formData.entries()) {
        if (key !== 'document_files' && value) {
            data[key] = value;
        }
    }

    // Handle courier field - ensure empty values are not sent
    if (!data.courier || data.courier.trim() === '') {
        delete data.courier;
    }

    // Set status based on action
    if (action === 'submit') {
        data.status = 'pending';
    } else {
        data.status = 'draft';
    }

    $.ajax({
        url: '/api/v1/transactions/transactions/',
        method: 'POST',
        headers: getAuthHeaders(),
        data: JSON.stringify(data),
        success: function(response) {
            const transactionId = response.id;

            // Upload documents if any
            const files = $('#document_files')[0].files;
            if (files.length > 0) {
                uploadDocuments(transactionId, files);
            } else {
                showAlert('success', '{% trans "Transaction created successfully" %}');
                setTimeout(function() {
                    window.location.href = '/transactions/';
                }, 2000);
            }
        },
        error: function(xhr) {
            const errors = xhr.responseJSON;
            let errorMessage = '{% trans "Error creating transaction" %}';

            if (errors) {
                errorMessage += ':<br>';
                for (let field in errors) {
                    errorMessage += `${field}: ${errors[field]}<br>`;
                }
            }

            showAlert('danger', errorMessage);
        }
    });
}

function uploadDocuments(transactionId, files) {
    const documentType = $('#document_type').val();
    const uploadData = new FormData();

    uploadData.append('transaction_id', transactionId);
    uploadData.append('document_type', documentType);

    // Append files with proper naming for DRF ListField
    for (let i = 0; i < files.length; i++) {
        uploadData.append('files', files[i]);
    }

    const headers = {};
    const token = getAuthToken();
    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    // Add CSRF token for file uploads
    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    $.ajax({
        url: '/api/v1/transactions/documents/bulk_upload/',
        method: 'POST',
        headers: headers,
        data: uploadData,
        processData: false,
        contentType: false,
        success: function(response) {
            showAlert('success', '{% trans "Transaction and documents uploaded successfully" %}');
            setTimeout(function() {
                window.location.href = '/transactions/';
            }, 2000);
        },
        error: function(xhr) {
            console.error('Document upload error:', xhr.responseJSON);

            let errorMessage = '{% trans "Transaction created but document upload failed" %}';

            if (xhr.responseJSON) {
                if (xhr.responseJSON.error) {
                    errorMessage += ': ' + xhr.responseJSON.error;
                }

                if (xhr.responseJSON.details) {
                    errorMessage += '<br><br>{% trans "Details:" %}<br>';
                    for (let field in xhr.responseJSON.details) {
                        const fieldErrors = xhr.responseJSON.details[field];
                        if (Array.isArray(fieldErrors)) {
                            errorMessage += `<strong>${field}:</strong> ${fieldErrors.join(', ')}<br>`;
                        } else {
                            errorMessage += `<strong>${field}:</strong> ${fieldErrors}<br>`;
                        }
                    }
                }
            }

            showAlert('warning', errorMessage);
            setTimeout(function() {
                window.location.href = '/transactions/';
            }, 5000); // Give more time to read the error
        }
    });
}

function calculateCommission() {
    // Get form values
    const location = $('#location').val();
    const transactionType = $('#transaction_type').val();
    const fromCurrency = $('#from_currency').val();
    const toCurrency = $('#to_currency').val();
    const fromAmount = parseFloat($('#from_amount').val());
    const deliveryMethod = $('#delivery_method').val();

    // Validate required fields
    if (!location || !fromCurrency || !toCurrency || !fromAmount || fromAmount <= 0) {
        showAlert('warning', '{% trans "Please fill in all required fields before calculating commission" %}');
        return;
    }

    // Show loading state
    const button = $('button[onclick="calculateCommission()"]');
    const originalText = button.html();
    button.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> {% trans "Calculating..." %}');

    // Prepare data for commission calculation
    const data = {
        location: location,
        transaction_type: transactionType,
        from_currency: fromCurrency,
        to_currency: toCurrency,
        from_amount: fromAmount,
        delivery_method: deliveryMethod
    };

    // Make API request
    $.ajax({
        url: '/api/v1/transactions/commission-rules/calculate_commission/',
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json',
            'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
        },
        data: JSON.stringify(data),
        success: function(response) {
            const commission = response.commission;

            // Update commission amount field
            $('#commission_amount').val(commission.amount);

            // Show commission info
            let infoText = `{% trans "Calculated" %}: ${commission.formatted_amount}`;
            if (commission.rule_name) {
                infoText += ` (${commission.rule_name} - ${commission.rule_type})`;
            }
            $('#commission_info').text(infoText).removeClass('text-danger').addClass('text-success');

            showAlert('success', '{% trans "Commission calculated successfully" %}');
        },
        error: function(xhr) {
            console.error('Commission calculation error:', xhr);
            const error = xhr.responseJSON?.error || '{% trans "Failed to calculate commission" %}';
            $('#commission_info').text('{% trans "Calculation failed" %}').removeClass('text-success').addClass('text-danger');
            showAlert('error', error);
        },
        complete: function() {
            // Restore button state
            button.prop('disabled', false).html(originalText);
        }
    });
}

// Auto-calculate commission when key fields change
$('#location, #transaction_type, #from_currency, #to_currency, #from_amount, #delivery_method').on('change', function() {
    // Clear previous commission info
    $('#commission_info').text('');

    // Auto-calculate if all required fields are filled
    const location = $('#location').val();
    const fromCurrency = $('#from_currency').val();
    const toCurrency = $('#to_currency').val();
    const fromAmount = parseFloat($('#from_amount').val());

    if (location && fromCurrency && toCurrency && fromAmount > 0) {
        // Debounce the calculation to avoid too many API calls
        clearTimeout(window.commissionCalculationTimeout);
        window.commissionCalculationTimeout = setTimeout(function() {
            calculateCommission();
        }, 1000);
    }
});
</script>
{% endblock %}
