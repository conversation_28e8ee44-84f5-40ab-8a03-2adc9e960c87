"""
Management command to create default users for each role in Arena Doviz.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from apps.locations.models import Location

User = get_user_model()


class Command(BaseCommand):
    help = 'Create default users for each role in Arena Doviz system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing default users (will delete and recreate)',
        )
        parser.add_argument(
            '--password',
            type=str,
            default='Arena2024!',
            help='Default password for all users (default: Arena2024!)',
        )

    def handle(self, *args, **options):
        """Create default users for testing and initial setup."""
        
        password = options['password']
        reset = options['reset']
        
        # Get or create a default location
        default_location, created = Location.objects.get_or_create(
            code='MAIN',
            defaults={
                'name': 'Main Office',
                'address': 'Main Office Address',
                'phone': '+1234567890',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created default location: {default_location.name}')
            )

        default_users = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'first_name': 'System',
                'last_name': 'Administrator',
                'role': User.Role.ADMIN,
                'is_staff': True,
                'is_superuser': True,
                'employee_id': 'EMP001',
                'department': 'Administration'
            },
            {
                'username': 'accountant',
                'email': '<EMAIL>',
                'first_name': 'Financial',
                'last_name': 'Accountant',
                'role': User.Role.ACCOUNTANT,
                'is_staff': False,
                'is_superuser': False,
                'employee_id': 'EMP002',
                'department': 'Finance'
            },
            {
                'username': 'employee',
                'email': '<EMAIL>',
                'first_name': 'Branch',
                'last_name': 'Employee',
                'role': User.Role.BRANCH_EMPLOYEE,
                'is_staff': False,
                'is_superuser': False,
                'employee_id': 'EMP003',
                'department': 'Operations',
                'location': default_location
            },
            {
                'username': 'viewer',
                'email': '<EMAIL>',
                'first_name': 'Report',
                'last_name': 'Viewer',
                'role': User.Role.VIEWER,
                'is_staff': False,
                'is_superuser': False,
                'employee_id': 'EMP004',
                'department': 'Management'
            },
            {
                'username': 'courier',
                'email': '<EMAIL>',
                'first_name': 'Delivery',
                'last_name': 'Courier',
                'role': User.Role.COURIER,
                'is_staff': False,
                'is_superuser': False,
                'employee_id': 'EMP005',
                'department': 'Logistics'
            }
        ]

        created_count = 0
        updated_count = 0

        with transaction.atomic():
            for user_data in default_users:
                username = user_data['username']
                
                # Check if user exists
                existing_user = User.objects.filter(username=username).first()
                
                if existing_user:
                    if reset:
                        existing_user.delete()
                        self.stdout.write(
                            self.style.WARNING(f'Deleted existing user: {username}')
                        )
                        existing_user = None
                    else:
                        # Update existing user
                        for key, value in user_data.items():
                            if key != 'username':  # Don't update username
                                setattr(existing_user, key, value)
                        existing_user.set_password(password)
                        existing_user.save()
                        updated_count += 1
                        self.stdout.write(
                            self.style.WARNING(f'Updated existing user: {username}')
                        )
                        continue

                # Create new user
                user = User.objects.create_user(
                    username=user_data['username'],
                    email=user_data['email'],
                    password=password,
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                    role=user_data['role'],
                    is_staff=user_data.get('is_staff', False),
                    is_superuser=user_data.get('is_superuser', False),
                    employee_id=user_data.get('employee_id'),
                    department=user_data.get('department'),
                    location=user_data.get('location'),
                    phone_number='+1234567890',
                    is_active=True
                )
                
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Created user: {username} ({user.get_role_display()}) - {user.email}'
                    )
                )

        # Display summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\n=== Summary ==='
                f'\nCreated: {created_count} users'
                f'\nUpdated: {updated_count} users'
                f'\nDefault password: {password}'
                f'\n\nDefault Users:'
            )
        )
        
        for user_data in default_users:
            role_display = dict(User.Role.choices)[user_data['role']]
            self.stdout.write(
                f'  • {user_data["username"]} ({role_display}) - {user_data["email"]}'
            )
        
        self.stdout.write(
            self.style.WARNING(
                f'\n⚠️  IMPORTANT: Change default passwords in production!'
                f'\n⚠️  These users are for testing and initial setup only.'
            )
        )
        
        # Display role permissions summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\n=== Role Permissions Summary ==='
                f'\n• Admin: Full system access, user management, all operations'
                f'\n• Accountant: Transaction approval, reporting, multi-location access'
                f'\n• Branch Employee: Daily operations, customer service, single location'
                f'\n• Viewer: Read-only access to reports and data'
                f'\n• Courier: Delivery tracking and receipt management'
                f'\n\nFor detailed permissions, see: docs/user_roles_and_permissions.md'
            )
        )
