"""
Arena Doviz Automated Testing Setup Script
"""
import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False
    
    return True

def install_playwright():
    """Install Playwright browsers"""
    print("🎭 Installing Playwright browsers...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "playwright", "install"
        ])
        print("✅ Playwright browsers installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Playwright browsers: {e}")
        return False
    
    return True

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    base_dir = Path(__file__).parent
    directories = [
        base_dir / "screenshots",
        base_dir / "logs",
        base_dir / "reports",
        base_dir / "test_data"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def setup_environment():
    """Setup environment variables"""
    print("🔧 Setting up environment...")
    
    env_file = Path(__file__).parent / ".env"
    
    if not env_file.exists():
        env_content = """# Arena Doviz Testing Environment Configuration
TEST_ENVIRONMENT=production
BROWSER_TYPE=chromium
HEADLESS=false
SLOW_MO=500
TEST_USERNAME=admin
TEST_PASSWORD=admin123
LOG_LEVEL=INFO
"""
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("✅ Created .env file with default configuration")
        print("📝 Please update .env file with your actual credentials")
    else:
        print("✅ .env file already exists")
    
    return True

def main():
    """Main setup function"""
    print("🚀 Arena Doviz Automated Testing Setup")
    print("=" * 50)
    
    steps = [
        ("Installing requirements", install_requirements),
        ("Installing Playwright browsers", install_playwright),
        ("Creating directories", create_directories),
        ("Setting up environment", setup_environment)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"❌ Setup failed at step: {step_name}")
            return False
    
    print("\n" + "=" * 50)
    print("✅ Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Update .env file with your actual credentials")
    print("2. Run tests with: python main_test_runner.py")
    print("3. Check reports in the 'reports' directory")
    
    return True

if __name__ == "__main__":
    main()
