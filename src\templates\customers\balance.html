{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Customer Balance" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-wallet2"></i>
                {% trans "Customer Balance" %}
            </h1>
            <div>
                <a href="javascript:history.back()" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {% trans "Back" %}
                </a>
                <button class="btn btn-primary" onclick="refreshBalance()">
                    <i class="bi bi-arrow-clockwise"></i>
                    {% trans "Refresh" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Customer Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5 id="customer-name">{% trans "Loading..." %}</h5>
                        <p class="text-muted mb-0" id="customer-code">-</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">{% trans "Last Updated" %}: <span id="last-updated">-</span></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Balance Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-currency-exchange"></i>
                    {% trans "Balance Summary" %}
                </h5>
            </div>
            <div class="card-body">
                <div id="balance-summary">
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">{% trans "Loading balance information..." %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    {% trans "Recent Transactions" %}
                </h5>
                <a href="/transactions/?customer={{ customer_id }}" class="btn btn-sm btn-outline-primary">
                    {% trans "View All" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="recent-transactions-table">
                        <thead>
                            <tr>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Transaction #" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Balance After" %}</th>
                                <th>{% trans "Status" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center text-muted">
                                    {% trans "Loading..." %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let customerId = null;

// Authentication utility functions
function getAuthToken() {
    return localStorage.getItem('arena_access_token') || '';
}

function getAuthHeaders() {
    const token = getAuthToken();
    const headers = {
        'Content-Type': 'application/json'
    };

    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

$(document).ready(function() {
    // Get customer ID from URL
    const pathParts = window.location.pathname.split('/');
    customerId = pathParts[pathParts.length - 3]; // Get UUID from URL
    
    if (customerId) {
        loadCustomerInfo();
        loadCustomerBalance();
        loadRecentTransactions();
    }
});

function loadCustomerInfo() {
    $.ajax({
        url: `/api/v1/customers/customers/${customerId}/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            $('#customer-name').text(data.first_name + ' ' + data.last_name || data.company_name);
            $('#customer-code').text(data.customer_code);
        },
        error: function(xhr) {
            console.error('Error loading customer info:', xhr);
            $('#customer-name').text('{% trans "Error loading customer" %}');
        }
    });
}

function loadCustomerBalance() {
    $.ajax({
        url: `/api/v1/customers/customers/${customerId}/balance/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayBalanceSummary(data);
            $('#last-updated').text(new Date().toLocaleString());
        },
        error: function(xhr) {
            console.error('Error loading balance:', xhr);
            $('#balance-summary').html(
                '<div class="alert alert-danger">' +
                '<i class="bi bi-exclamation-triangle"></i> ' +
                '{% trans "Failed to load balance information" %}' +
                '</div>'
            );
        }
    });
}

function displayBalanceSummary(balances) {
    let html = '';
    
    if (!balances || balances.length === 0) {
        html = '<div class="alert alert-info">' +
               '<i class="bi bi-info-circle"></i> ' +
               '{% trans "No balance information available" %}' +
               '</div>';
    } else {
        html = '<div class="row">';
        
        balances.forEach(function(balance) {
            const balanceClass = balance.balance >= 0 ? 'text-success' : 'text-danger';
            const balanceIcon = balance.balance >= 0 ? 'bi-arrow-up' : 'bi-arrow-down';
            
            html += `
                <div class="col-md-4 mb-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h6 class="card-title">${balance.currency_code}</h6>
                            <h4 class="mb-1 ${balanceClass}">
                                <i class="bi ${balanceIcon}"></i>
                                ${balance.formatted_balance}
                            </h4>
                            <small class="text-muted">{% trans "Current Balance" %}</small>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
    }
    
    $('#balance-summary').html(html);
}

function loadRecentTransactions() {
    $.ajax({
        url: `/api/v1/transactions/transactions/?customer=${customerId}&limit=10&ordering=-created_at`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayRecentTransactions(data.results || []);
        },
        error: function(xhr) {
            console.error('Error loading transactions:', xhr);
            $('#recent-transactions-table tbody').html(
                '<tr><td colspan="6" class="text-center text-danger">' +
                '{% trans "Failed to load transactions" %}' +
                '</td></tr>'
            );
        }
    });
}

function displayRecentTransactions(transactions) {
    const tbody = $('#recent-transactions-table tbody');
    tbody.empty();
    
    if (transactions.length === 0) {
        tbody.append(
            '<tr><td colspan="6" class="text-center text-muted">' +
            '{% trans "No recent transactions found" %}' +
            '</td></tr>'
        );
        return;
    }
    
    transactions.forEach(function(tx) {
        const statusClass = getStatusClass(tx.status);
        const formattedDate = new Date(tx.created_at).toLocaleDateString();
        
        const row = `
            <tr>
                <td>${formattedDate}</td>
                <td><code>${tx.transaction_number}</code></td>
                <td>${tx.transaction_type_name || tx.type}</td>
                <td>${tx.display_amount || tx.amount}</td>
                <td>-</td>
                <td><span class="badge bg-${statusClass}">${tx.status_display || tx.status}</span></td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getStatusClass(status) {
    const statusClasses = {
        'draft': 'secondary',
        'pending': 'warning',
        'approved': 'info',
        'completed': 'success',
        'cancelled': 'danger',
        'rejected': 'danger'
    };
    return statusClasses[status] || 'secondary';
}

function refreshBalance() {
    loadCustomerBalance();
    loadRecentTransactions();
}
</script>
{% endblock %}
