"""
Simple Transfer Form Test
"""
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
import django
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import AccessToken

User = get_user_model()

def test_transfer_forms():
    """Simple test of transfer forms"""
    print("🔧 Testing Transfer Forms...")

    # Create test client
    client = Client()

    # Create test user
    user, created = User.objects.get_or_create(
        username='simple_test_user',
        defaults={'email': '<EMAIL>', 'is_staff': True}
    )

    # Login user with Django session (required for @login_required views)
    client.force_login(user)

    # Get JWT token for API calls
    token = AccessToken.for_user(user)
    api_headers = {'HTTP_AUTHORIZATION': f'Bearer {str(token)}'}

    # Test form URLs
    form_urls = [
        '/transactions/transfer/internal/add/',
        '/transactions/transfer/external/add/',
        '/transactions/transfer/international/add/'
    ]

    results = {}

    for url in form_urls:
        try:
            response = client.get(url)  # No headers needed for session auth
            results[url] = {
                'status_code': response.status_code,
                'accessible': response.status_code == 200
            }

            if response.status_code == 200:
                print(f"✅ {url}: Working")

                # Check for required form elements
                content = response.content.decode('utf-8')
                if 'id="transaction-form"' in content:
                    print(f"   ✓ Form element found")
                else:
                    print(f"   ⚠️ Form element missing")

            else:
                print(f"❌ {url}: HTTP {response.status_code}")

        except Exception as e:
            results[url] = {
                'status_code': 0,
                'accessible': False,
                'error': str(e)
            }
            print(f"❌ {url}: Error - {str(e)}")
    
    # Test API endpoints
    api_endpoints = [
        '/api/v1/customers/customers/',
        '/api/v1/locations/locations/',
        '/api/v1/currencies/currencies/',
        '/api/v1/transactions/types/'
    ]
    
    print("\n🔌 Testing API Endpoints...")
    
    for endpoint in api_endpoints:
        try:
            response = client.get(endpoint, **api_headers)  # Use JWT for API calls

            if response.status_code == 200:
                data = response.json()
                count = len(data.get('results', data)) if isinstance(data, dict) else len(data)
                print(f"✅ {endpoint}: Working ({count} items)")
            else:
                print(f"❌ {endpoint}: HTTP {response.status_code}")

        except Exception as e:
            print(f"❌ {endpoint}: Error - {str(e)}")
    
    # Check JavaScript files
    print("\n🖥️ Checking JavaScript Files...")
    
    js_files = [
        'static/js/arena-doviz.js',
        'static/js/transactions/common.js',
        'static/js/transactions/internal_transfer.js',
        'static/js/transactions/external_transfer.js',
        'static/js/transactions/international_transfer.js'
    ]

    for js_file in js_files:
        if os.path.exists(js_file):
            print(f"✅ {os.path.basename(js_file)}: Found")
        else:
            print(f"❌ {os.path.basename(js_file)}: Missing")
    
    print("\n📊 Test Summary:")
    working_forms = sum(1 for result in results.values() if result['accessible'])
    total_forms = len(results)
    print(f"   Working Forms: {working_forms}/{total_forms}")
    
    if working_forms == total_forms:
        print("🎉 All transfer forms are accessible!")
    else:
        print("⚠️ Some transfer forms have issues")
    
    return results

if __name__ == "__main__":
    test_transfer_forms()
