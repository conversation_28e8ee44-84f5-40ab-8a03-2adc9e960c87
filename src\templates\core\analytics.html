{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Analytics" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<style>
/* Analytics page following Arena Doviz design system */
.analytics-header {
    background-color: var(--arena-primary);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 !important;
}

.chart-container {
    position: relative;
    height: 400px;
    margin-bottom: 2rem;
    background-color: var(--arena-white);
    border: 1px solid var(--arena-border);
    border-radius: 0 !important;
    padding: 1rem;
}

.chart-controls {
    background-color: var(--arena-light-gray);
    padding: 1rem;
    border-radius: 0 !important;
    margin-bottom: 1rem;
    border: 1px solid var(--arena-border);
}

.metric-card {
    background-color: var(--arena-primary);
    color: white;
    border-radius: 0 !important;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
    border: 1px solid var(--arena-primary);
}

.metric-card.success {
    background-color: var(--arena-tertiary);
    border-color: var(--arena-tertiary);
}

.metric-card.danger {
    background-color: var(--arena-secondary);
    border-color: var(--arena-secondary);
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: white;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
    color: white;
}

.chart-export-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    background-color: var(--arena-primary) !important;
    border-color: var(--arena-primary) !important;
    color: white !important;
    border-radius: 0 !important;
}

.chart-export-btn:hover {
    background-color: #001a3d !important;
    border-color: #001a3d !important;
}

/* Ensure all form elements follow design system */
.form-select,
.form-control {
    border-radius: 0 !important;
    border-color: var(--arena-border);
}

.form-select:focus,
.form-control:focus {
    border-color: var(--arena-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 13, 40, 0.25);
}

/* Button styling */
.btn {
    border-radius: 0 !important;
}

.btn-primary {
    background-color: var(--arena-primary) !important;
    border-color: var(--arena-primary) !important;
}

.btn-success {
    background-color: var(--arena-tertiary) !important;
    border-color: var(--arena-tertiary) !important;
}

.btn-secondary {
    background-color: var(--arena-secondary) !important;
    border-color: var(--arena-secondary) !important;
}
</style>
{% endblock %}

{% block content %}
<div class="analytics-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="h2 mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    {% trans "Advanced Analytics" %}
                </h1>
                <p class="mb-0">{% trans "Comprehensive data visualization and insights" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Controls Row -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="chart-controls">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label for="dateRange" class="form-label">{% trans "Date Range" %}</label>
                        <select class="form-select" id="dateRange" onchange="updateAnalytics()">
                            <option value="7">{% trans "Last 7 days" %}</option>
                            <option value="30" selected>{% trans "Last 30 days" %}</option>
                            <option value="90">{% trans "Last 90 days" %}</option>
                            <option value="365">{% trans "Last year" %}</option>
                        </select>
                    </div>
                    
                    {% if user.can_manage_users %}
                    <div class="col-md-3">
                        <label for="locationFilter" class="form-label">{% trans "Location" %}</label>
                        <select class="form-select" id="locationFilter" onchange="updateAnalytics()">
                            <option value="">{% trans "All Locations" %}</option>
                            <!-- Will be populated by JavaScript -->
                        </select>
                    </div>
                    {% endif %}
                    
                    <div class="col-md-3">
                        <label for="currencyFilter" class="form-label">{% trans "Currency" %}</label>
                        <select class="form-select" id="currencyFilter" onchange="updateAnalytics()">
                            <option value="">{% trans "All Currencies" %}</option>
                            <!-- Will be populated by JavaScript -->
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-primary" onclick="exportAnalytics()">
                                <i class="bi bi-download me-2"></i>
                                {% trans "Export Data" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Row -->
    <div class="row mb-4" id="keyMetrics">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value" id="totalTransactions">-</div>
                <div class="metric-label">{% trans "Total Transactions" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value" id="totalVolume">-</div>
                <div class="metric-label">{% trans "Total Volume" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value" id="totalCommission">-</div>
                <div class="metric-label">{% trans "Total Commission" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value" id="avgTransactionSize">-</div>
                <div class="metric-label">{% trans "Avg Transaction Size" %}</div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart me-2"></i>
                        {% trans "Transaction Volume Trends" %}
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary chart-export-btn" onclick="exportChart('transactionChart')">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="transactionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart me-2"></i>
                        {% trans "Currency Distribution" %}
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary chart-export-btn" onclick="exportChart('currencyChart')">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="currencyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-graph-up me-2"></i>
                        {% trans "Profit Analysis" %}
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary chart-export-btn" onclick="exportChart('profitChart')">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="profitChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-speedometer2 me-2"></i>
                        {% trans "Balance Trends" %}
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary chart-export-btn" onclick="exportChart('balanceChart')">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="balanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 3 -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart-fill me-2"></i>
                        {% trans "Transaction Status Distribution" %}
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary chart-export-btn" onclick="exportChart('statusChart')">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        {% if user.can_manage_users %}
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-building me-2"></i>
                        {% trans "Location Performance" %}
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary chart-export-btn" onclick="exportChart('locationChart')">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="locationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Loading Overlay -->
    <div id="analyticsLoading" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.8); z-index: 9999; display: none !important;">
        <div class="text-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-3">
                <h5>{% trans "Loading Analytics..." %}</h5>
                <p class="text-muted">{% trans "Please wait while we process your data" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Force hide loading overlay immediately
    $('#analyticsLoading').hide().css('display', 'none !important');
    showLoading(false);

    // Add timeout to ensure loading is hidden
    setTimeout(function() {
        showLoading(false);
    }, 100);

    loadFilters();
    updateAnalytics();

    // Ensure loading is hidden after 5 seconds maximum
    setTimeout(function() {
        showLoading(false);
        console.log('Force hiding analytics loading after timeout');
    }, 5000);
});

function loadFilters() {
    // Load locations for admin users
    {% if user.can_manage_users %}
    ArenaDoviz.api.request('GET', 'locations/locations/')
        .then(data => {
            const locationSelect = $('#locationFilter');
            data.results.forEach(location => {
                locationSelect.append(`<option value="${location.id}">${location.name}</option>`);
            });
        })
        .catch(error => console.error('Error loading locations:', error));
    {% endif %}
    
    // Load currencies
    ArenaDoviz.api.request('GET', 'currencies/currencies/')
        .then(data => {
            const currencySelect = $('#currencyFilter');
            data.results.forEach(currency => {
                currencySelect.append(`<option value="${currency.id}">${currency.code} - ${currency.name}</option>`);
            });
        })
        .catch(error => console.error('Error loading currencies:', error));
}

function updateAnalytics() {
    showLoading(true);

    const params = new URLSearchParams({
        days: $('#dateRange').val()
    });

    const locationFilter = $('#locationFilter').val();
    if (locationFilter) {
        params.append('location', locationFilter);
    }

    const currencyFilter = $('#currencyFilter').val();
    if (currencyFilter) {
        params.append('currency', currencyFilter);
    }

    console.log('Updating analytics with params:', params.toString());
    console.log('Location filter:', locationFilter);
    console.log('Currency filter:', currencyFilter);

    ArenaDoviz.api.request('GET', `core/dashboard/chart_data/?${params.toString()}`)
        .then(data => {
            console.log('Analytics data received:', data);
            updateKeyMetrics(data);
            ArenaDovizCharts.init(data);
            showLoading(false);
        })
        .catch(error => {
            console.error('Error loading analytics:', error);
            // Always hide loading even on error
            showLoading(false);

            // Show error message but don't block the UI
            if (error.status === 401) {
                showAlert('warning', '{% trans "Please log in to view analytics data" %}');
            } else {
                showAlert('error', '{% trans "Failed to load analytics data" %}');
            }
        });
}

function updateKeyMetrics(data) {
    // Calculate metrics from chart data
    const transactionData = data.transaction_volume;
    const profitData = data.profit_analysis;
    
    if (transactionData && transactionData.datasets.length > 0) {
        const totalTransactions = transactionData.datasets[0].data.reduce((a, b) => a + b, 0);
        const totalVolume = transactionData.datasets[1] ? transactionData.datasets[1].data.reduce((a, b) => a + b, 0) : 0;
        const avgTransactionSize = totalTransactions > 0 ? totalVolume / totalTransactions : 0;
        
        $('#totalTransactions').text(totalTransactions.toLocaleString());
        $('#totalVolume').text('$' + totalVolume.toLocaleString());
        $('#avgTransactionSize').text('$' + avgTransactionSize.toLocaleString());
    }
    
    if (profitData && profitData.total_commission !== undefined) {
        $('#totalCommission').text('$' + profitData.total_commission.toLocaleString());
    }
}

function showLoading(show) {
    const loadingElement = $('#analyticsLoading');
    if (show) {
        loadingElement.show();
        loadingElement.css('display', 'flex');
    } else {
        loadingElement.hide();
        loadingElement.css('display', 'none');
        // Force hide with important
        loadingElement.attr('style', 'display: none !important');
    }
}

function exportChart(chartId) {
    const chart = ArenaDovizCharts.instances[chartId.replace('Chart', '')];
    if (chart) {
        const url = chart.toBase64Image();
        const link = document.createElement('a');
        link.download = `${chartId}_${new Date().toISOString().split('T')[0]}.png`;
        link.href = url;
        link.click();
    }
}

function exportAnalytics() {
    // Export all analytics data as CSV
    const params = new URLSearchParams({
        days: $('#dateRange').val(),
        format: 'csv'
    });

    const locationFilter = $('#locationFilter').val();
    if (locationFilter) {
        params.append('location', locationFilter);
    }

    const currencyFilter = $('#currencyFilter').val();
    if (currencyFilter) {
        params.append('currency', currencyFilter);
    }

    // Use authenticated fetch request instead of window.open
    const headers = {
        'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
    };

    fetch(`/api/v1/core/dashboard/export_data/?${params.toString()}`, {
        method: 'GET',
        headers: headers
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.blob();
    })
    .then(blob => {
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `arena_doviz_analytics_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        showAlert('success', '{% trans "Analytics data exported successfully" %}');
    })
    .catch(error => {
        console.error('Export error:', error);
        showAlert('error', '{% trans "Failed to export analytics data" %}');
    });
}

function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 10000;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('body').append(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
