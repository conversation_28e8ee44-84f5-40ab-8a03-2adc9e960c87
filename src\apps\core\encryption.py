"""
AES-256 encryption utilities for Arena Doviz Exchange Accounting System.
Provides secure encryption/decryption for sensitive data fields.
"""

import os
import base64
import logging
from typing import Optional, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

logger = logging.getLogger(__name__)


class EncryptionError(Exception):
    """Custom exception for encryption-related errors."""
    pass


class AESEncryption:
    """
    AES-256 encryption utility class for sensitive data.
    Uses Fernet (AES 128 in CBC mode with HMAC for authentication).
    """
    
    def __init__(self):
        """Initialize encryption with key from settings."""
        self._fernet = None
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize the Fernet encryption instance."""
        try:
            encryption_key = self._get_encryption_key()
            self._fernet = Fernet(encryption_key)
            logger.debug("Encryption initialized successfully")
        except ImproperlyConfigured:
            # Re-raise configuration errors as-is for proper test handling
            raise
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {str(e)}")
            raise EncryptionError(f"Encryption initialization failed: {str(e)}")
    
    def _get_encryption_key(self) -> bytes:
        """
        Get or generate encryption key from environment variables.
        
        Returns:
            bytes: Base64-encoded encryption key
            
        Raises:
            ImproperlyConfigured: If encryption key is not properly configured
        """
        # Try to get key from environment variable
        key_string = os.environ.get('ARENA_ENCRYPTION_KEY')
        
        if not key_string:
            # Try to get from Django settings
            key_string = getattr(settings, 'ARENA_ENCRYPTION_KEY', None)
        
        if not key_string:
            # Generate a new key for development (not recommended for production)
            if settings.DEBUG:
                logger.warning(
                    "No encryption key found. Generating new key for development. "
                    "Set ARENA_ENCRYPTION_KEY environment variable for production."
                )
                key = Fernet.generate_key()
                logger.warning(f"Generated encryption key: {key.decode()}")
                return key
            else:
                raise ImproperlyConfigured(
                    "ARENA_ENCRYPTION_KEY environment variable must be set in production"
                )
        
        try:
            # Validate the key format
            key_bytes = key_string.encode() if isinstance(key_string, str) else key_string
            Fernet(key_bytes)  # Test if key is valid
            return key_bytes
        except Exception as e:
            raise ImproperlyConfigured(f"Invalid encryption key format: {str(e)}")
    
    def encrypt(self, plaintext: Union[str, bytes, None]) -> Optional[str]:
        """
        Encrypt plaintext data.
        
        Args:
            plaintext: Data to encrypt (string, bytes, or None)
            
        Returns:
            str: Base64-encoded encrypted data, or None if input is None/empty
            
        Raises:
            EncryptionError: If encryption fails
        """
        if not plaintext:
            return None
        
        try:
            # Convert to bytes if string
            if isinstance(plaintext, str):
                plaintext_bytes = plaintext.encode('utf-8')
            else:
                plaintext_bytes = plaintext
            
            # Encrypt the data
            encrypted_bytes = self._fernet.encrypt(plaintext_bytes)
            
            # Return base64-encoded string for database storage
            encrypted_string = base64.b64encode(encrypted_bytes).decode('utf-8')
            
            logger.debug("Data encrypted successfully")
            return encrypted_string
            
        except Exception as e:
            logger.error(f"Encryption failed: {str(e)}")
            raise EncryptionError(f"Failed to encrypt data: {str(e)}")
    
    def decrypt(self, encrypted_data: Union[str, None]) -> Optional[str]:
        """
        Decrypt encrypted data.
        
        Args:
            encrypted_data: Base64-encoded encrypted data or None
            
        Returns:
            str: Decrypted plaintext, or None if input is None/empty
            
        Raises:
            EncryptionError: If decryption fails
        """
        if not encrypted_data:
            return None
        
        try:
            # Decode from base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # Decrypt the data
            decrypted_bytes = self._fernet.decrypt(encrypted_bytes)
            
            # Convert back to string
            decrypted_string = decrypted_bytes.decode('utf-8')
            
            logger.debug("Data decrypted successfully")
            return decrypted_string
            
        except Exception as e:
            logger.error(f"Decryption failed: {str(e)}")
            raise EncryptionError(f"Failed to decrypt data: {str(e)}")
    
    def is_encrypted(self, data: str) -> bool:
        """
        Check if data appears to be encrypted.
        
        Args:
            data: String to check
            
        Returns:
            bool: True if data appears to be encrypted
        """
        if not data:
            return False
        
        try:
            # Try to decode as base64 and decrypt
            base64.b64decode(data.encode('utf-8'))
            return True
        except Exception:
            return False


# Global encryption instance
_encryption_instance = None


def get_encryption_instance() -> AESEncryption:
    """
    Get the global encryption instance (singleton pattern).
    
    Returns:
        AESEncryption: The encryption instance
    """
    global _encryption_instance
    
    if _encryption_instance is None:
        _encryption_instance = AESEncryption()
    
    return _encryption_instance


def encrypt_sensitive_data(data: Union[str, None]) -> Optional[str]:
    """
    Convenience function to encrypt sensitive data.
    
    Args:
        data: Data to encrypt
        
    Returns:
        str: Encrypted data or None
    """
    if not data:
        return None
    
    encryption = get_encryption_instance()
    return encryption.encrypt(data)


def decrypt_sensitive_data(encrypted_data: Union[str, None]) -> Optional[str]:
    """
    Convenience function to decrypt sensitive data.
    
    Args:
        encrypted_data: Encrypted data to decrypt
        
    Returns:
        str: Decrypted data or None
    """
    if not encrypted_data:
        return None
    
    encryption = get_encryption_instance()
    return encryption.decrypt(encrypted_data)


def generate_encryption_key() -> str:
    """
    Generate a new encryption key for use in environment variables.
    
    Returns:
        str: Base64-encoded encryption key
    """
    key = Fernet.generate_key()
    return key.decode('utf-8')


# List of sensitive fields that should be encrypted
SENSITIVE_FIELDS = [
    # Customer sensitive fields
    'phone_number',
    'email',
    'address',
    'whatsapp_group_id',
    'notes',
    'description',
    
    # Transaction sensitive fields
    'reference_number',
    'tracking_code',
    'delivery_address',
    'notes',
    
    # User sensitive fields
    'employee_id',
    'notes',
    'last_login_ip',
    
    # Financial sensitive fields
    'from_amount',
    'to_amount',
    'commission_amount',
    'credit_limit',
]


def should_encrypt_field(field_name: str) -> bool:
    """
    Check if a field should be encrypted.
    
    Args:
        field_name: Name of the field
        
    Returns:
        bool: True if field should be encrypted
    """
    return field_name in SENSITIVE_FIELDS


def mask_sensitive_value(value: str, mask_char: str = '*', visible_chars: int = 4) -> str:
    """
    Mask sensitive value for display purposes.

    Args:
        value: Value to mask
        mask_char: Character to use for masking
        visible_chars: Number of characters to show at the end

    Returns:
        str: Masked value
    """
    if not value or len(value) <= visible_chars:
        return mask_char * len(value) if value else ''

    return mask_char * (len(value) - visible_chars) + value[-visible_chars:]
