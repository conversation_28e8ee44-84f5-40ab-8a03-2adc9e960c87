/* Arena Doviz Exchange Accounting System - Custom Styles */
/* Minimal, Sharp Design with Custom Color Scheme */

:root {
    /* Primary Color Scheme */
    --primary-color: #000d28;      /* Dark Navy */
    --secondary-color: #6a0000;    /* Dark Red */
    --tertiary-color: #013121;     /* Dark Green */
    --white-color: #ffffff;        /* White for all other elements */

    /* Functional Colors */
    --success-color: var(--tertiary-color);
    --danger-color: var(--secondary-color);
    --warning-color: #b8860b;      /* Dark goldenrod for warnings */
    --info-color: var(--primary-color);
    --light-color: var(--white-color);
    --dark-color: var(--primary-color);

    /* Text Colors */
    --text-primary: var(--primary-color);
    --text-secondary: var(--secondary-color);
    --text-white: var(--white-color);
}

/* General Styles - Sharp, Minimal Design */
* {
    border-radius: 0 !important; /* Remove ALL rounded corners */
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--white-color);
    color: var(--text-primary);
    line-height: 1.5;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--white-color) !important;
}

/* Navigation Bar */
.navbar {
    background-color: var(--primary-color) !important;
    border-bottom: 2px solid var(--secondary-color);
}

.navbar-nav .nav-link {
    color: var(--white-color) !important;
    font-weight: 500;
}

.navbar-nav .nav-link:hover {
    color: var(--white-color) !important;
    background-color: var(--secondary-color);
}

.navbar-toggler {
    border-color: var(--white-color);
}

/* Card Enhancements - Sharp Design */
.card {
    border: 2px solid var(--primary-color);
    background-color: var(--white-color);
    box-shadow: none;
    transition: border-color 0.15s ease-in-out;
}

.card:hover {
    border-color: var(--secondary-color);
}

.card-header {
    background-color: var(--primary-color);
    color: var(--white-color);
    border-bottom: 2px solid var(--secondary-color);
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
    background-color: var(--white-color);
}

.card-footer {
    background-color: var(--white-color);
    border-top: 1px solid var(--primary-color);
}

/* Dashboard Cards */
.dashboard-card {
    transition: border-color 0.2s ease-in-out;
}

.dashboard-card:hover {
    border-color: var(--tertiary-color);
}

/* Enhanced Dashboard Metrics - Sharp Design */
.metric-card {
    border: 2px solid var(--primary-color);
    transition: all 0.3s ease;
}

.metric-card:hover {
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.metric-card .card-body {
    padding: 1.5rem;
}

.metric-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: var(--white-color);
}

.metric-card .opacity-75 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--white-color);
}

/* Primary Metric Card */
.metric-card.bg-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color);
}

/* Secondary Metric Card */
.metric-card.bg-secondary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color);
}

/* Success/Tertiary Metric Card */
.metric-card.bg-success {
    background-color: var(--tertiary-color) !important;
    border-color: var(--tertiary-color);
}

/* Balance Cards */
.balance-card {
    border: 2px solid var(--primary-color);
    transition: all 0.3s ease;
}

.balance-card:hover {
    border-color: var(--tertiary-color);
    transform: translateY(-2px);
}

.balance-card .card-header {
    padding: 0.75rem 1rem;
}

.balance-card .card-body {
    padding: 1rem;
}

.balance-card h4 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1rem;
    border: 1px solid var(--primary-color);
    background-color: var(--white-color);
}

.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    color: var(--primary-color);
}

/* Exchange Rate Alerts */
.rate-alert-card {
    border-left: 4px solid var(--primary-color);
    transition: all 0.2s ease;
}

.rate-alert-card:hover {
    border-left-color: var(--tertiary-color);
    background-color: rgba(1, 49, 33, 0.05);
}

.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
}

/* Animation for loading states */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .metric-card h2 {
        font-size: 2rem;
    }

    .balance-card h4 {
        font-size: 1.5rem;
    }

    .chart-container {
        height: 250px;
    }
}

/* Table Enhancements - Sharp Design */
.table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    border-top: none;
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
    color: var(--white-color);
    background-color: var(--primary-color);
    padding: 1rem 0.75rem;
}

.table td {
    border-bottom: 1px solid var(--primary-color);
    padding: 0.75rem;
    background-color: var(--white-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 13, 40, 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 13, 40, 0.02);
}

/* Form Enhancements - Sharp Design */
.form-control {
    border: 2px solid var(--primary-color);
    background-color: var(--white-color);
    color: var(--text-primary);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(106, 0, 0, 0.25);
    background-color: var(--white-color);
}

.form-select {
    border: 2px solid var(--primary-color);
    background-color: var(--white-color);
    color: var(--text-primary);
}

.form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(106, 0, 0, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

/* Button Enhancements - Sharp Design */
.btn {
    font-weight: 500;
    transition: all 0.15s ease-in-out;
    border-width: 2px;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
}

.btn-secondary:hover {
    background-color: var(--tertiary-color);
    border-color: var(--tertiary-color);
    color: var(--white-color);
}

.btn-success {
    background-color: var(--tertiary-color);
    border-color: var(--tertiary-color);
    color: var(--white-color);
}

.btn-success:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: var(--white-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white-color);
}

/* Badge Enhancements - Sharp Design */
.badge {
    font-weight: 500;
    border: 1px solid;
}

.badge.bg-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color);
    color: var(--white-color);
}

.badge.bg-secondary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color);
    color: var(--white-color);
}

.badge.bg-success {
    background-color: var(--tertiary-color) !important;
    border-color: var(--tertiary-color);
    color: var(--white-color);
}

/* Alert Enhancements - Sharp Design */
.alert {
    border: 2px solid;
    background-color: var(--white-color);
}

.alert-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.alert-success {
    border-color: var(--tertiary-color);
    color: var(--tertiary-color);
}

.alert-danger {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

/* Currency Display */
.currency-amount {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.currency-positive {
    color: var(--tertiary-color);
}

.currency-negative {
    color: var(--secondary-color);
}

/* Status Indicators */
.status-active {
    color: var(--tertiary-color);
}

.status-inactive {
    color: var(--secondary-color);
}

.status-pending {
    color: var(--warning-color);
}

.status-completed {
    color: var(--tertiary-color);
}

.status-cancelled {
    color: var(--secondary-color);
}

/* Loading Spinner - Sharp Design */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 0.125rem solid rgba(0, 13, 40, 0.1);
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }

    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .metric-card h2 {
        font-size: 2rem;
    }

    .balance-card h4 {
        font-size: 1.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .card-header .btn,
    footer {
        display: none !important;
    }

    .card {
        border: 2px solid var(--primary-color) !important;
        box-shadow: none !important;
    }

    .table {
        font-size: 0.875rem;
    }

    body {
        background-color: var(--white-color) !important;
        color: var(--text-primary) !important;
    }
}

/* Accessibility Enhancements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Indicators - Sharp Design */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 3px solid var(--secondary-color);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .card {
        border: 3px solid var(--primary-color);
    }

    .btn {
        border-width: 3px;
    }

    .table th {
        border-bottom: 3px solid var(--primary-color);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Utility Classes */
.text-currency {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--tertiary-color), var(--primary-color));
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--secondary-color));
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--primary-color), var(--tertiary-color));
}

/* Footer Styling */
footer {
    background-color: var(--primary-color) !important;
    color: var(--white-color);
    border-top: 2px solid var(--secondary-color);
}

footer .text-muted {
    color: var(--white-color) !important;
}

/* DataTables Styling */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 2px solid var(--primary-color);
    background-color: var(--white-color);
    color: var(--text-primary);
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: 1px solid var(--primary-color);
    background-color: var(--white-color);
    color: var(--text-primary) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: var(--primary-color) !important;
    color: var(--white-color) !important;
    border-color: var(--primary-color);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: var(--secondary-color) !important;
    color: var(--white-color) !important;
    border-color: var(--secondary-color);
}

/* Modal Styling */
.modal-content {
    border: 2px solid var(--primary-color);
    background-color: var(--white-color);
}

.modal-header {
    background-color: var(--primary-color);
    color: var(--white-color);
    border-bottom: 2px solid var(--secondary-color);
}

.modal-footer {
    background-color: var(--white-color);
    border-top: 1px solid var(--primary-color);
}

/* Dropdown Styling */
.dropdown-menu {
    border: 2px solid var(--primary-color);
    background-color: var(--white-color);
}

.dropdown-item {
    color: var(--text-primary);
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--white-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
