"""
Arena Doviz Browser Automation Framework
"""
import asyncio
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict

from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontext
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.progress import Progress, TaskID

from config import TestConfig, TRANSACTION_TYPES, SCREENSHOTS_DIR, LOGS_DIR

# Setup rich console and logging
console = Console()
logging.basicConfig(
    level=getattr(logging, TestConfig.LOG_LEVEL),
    format="%(message)s",
    datefmt="[%X]",
    handlers=[<PERSON><PERSON><PERSON><PERSON>(console=console, rich_tracebacks=True)]
)
logger = logging.getLogger("arena_automation")

@dataclass
class TestResult:
    """Test result data structure"""
    test_name: str
    transaction_type: str
    status: str  # 'PASS', 'FAIL', 'ERROR'
    start_time: datetime
    end_time: datetime
    duration_ms: int
    screenshot_path: Optional[str] = None
    console_logs: List[Dict] = None
    network_logs: List[Dict] = None
    errors: List[str] = None
    form_data: Dict = None
    api_responses: List[Dict] = None
    
    def __post_init__(self):
        if self.console_logs is None:
            self.console_logs = []
        if self.network_logs is None:
            self.network_logs = []
        if self.errors is None:
            self.errors = []
        if self.api_responses is None:
            self.api_responses = []

class BrowserAutomation:
    """Main browser automation class for Arena Doviz testing"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.test_results: List[TestResult] = []
        self.console_logs: List[Dict] = []
        self.network_logs: List[Dict] = []
        self.current_test: Optional[TestResult] = None
        
    async def setup(self) -> None:
        """Initialize browser and context"""
        logger.info(f"🚀 Starting browser automation for {TestConfig.ENVIRONMENT} environment")
        logger.info(f"🌐 Base URL: {TestConfig.BASE_URL}")
        
        playwright = await async_playwright().start()
        
        # Launch browser
        browser_type = getattr(playwright, TestConfig.BROWSER_TYPE)
        self.browser = await browser_type.launch(
            headless=TestConfig.HEADLESS,
            slow_mo=TestConfig.SLOW_MO,
            args=['--no-sandbox', '--disable-dev-shm-usage'] if TestConfig.ENVIRONMENT == 'production' else []
        )
        
        # Create context
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            record_video_dir=LOGS_DIR / "videos" if TestConfig.ENVIRONMENT == 'production' else None
        )
        
        # Create page
        self.page = await self.context.new_page()
        
        # Set timeouts
        self.page.set_default_timeout(TestConfig.DEFAULT_TIMEOUT)
        self.page.set_default_navigation_timeout(TestConfig.NAVIGATION_TIMEOUT)
        
        # Setup event listeners
        await self._setup_event_listeners()
        
        logger.info("✅ Browser automation setup complete")
    
    async def _setup_event_listeners(self) -> None:
        """Setup event listeners for logging and monitoring"""
        
        # Console log listener
        if TestConfig.CONSOLE_LOG_CAPTURE:
            self.page.on("console", self._handle_console_message)
        
        # Network request/response listener
        if TestConfig.NETWORK_LOG_CAPTURE:
            self.page.on("request", self._handle_request)
            self.page.on("response", self._handle_response)
        
        # Page error listener
        self.page.on("pageerror", self._handle_page_error)
    
    def _handle_console_message(self, msg) -> None:
        """Handle browser console messages"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'type': msg.type,
            'text': msg.text,
            'location': msg.location
        }
        self.console_logs.append(log_entry)
        
        if self.current_test:
            self.current_test.console_logs.append(log_entry)
        
        # Log errors to our logger
        if msg.type in ['error', 'warning']:
            logger.warning(f"🔍 Console {msg.type}: {msg.text}")
    
    def _handle_request(self, request) -> None:
        """Handle network requests"""
        if '/api/' in request.url:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'type': 'request',
                'method': request.method,
                'url': request.url,
                'headers': dict(request.headers)
            }
            self.network_logs.append(log_entry)
            
            if self.current_test:
                self.current_test.network_logs.append(log_entry)
    
    def _handle_response(self, response) -> None:
        """Handle network responses"""
        if '/api/' in response.url:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'type': 'response',
                'status': response.status,
                'url': response.url,
                'headers': dict(response.headers)
            }
            self.network_logs.append(log_entry)
            
            if self.current_test:
                self.current_test.network_logs.append(log_entry)
                self.current_test.api_responses.append(log_entry)
            
            # Log API errors
            if response.status >= 400:
                logger.error(f"🔥 API Error: {response.status} - {response.url}")
    
    def _handle_page_error(self, error) -> None:
        """Handle page JavaScript errors"""
        error_msg = f"Page Error: {str(error)}"
        logger.error(f"💥 {error_msg}")
        
        if self.current_test:
            self.current_test.errors.append(error_msg)
    
    async def login(self) -> bool:
        """Login to Arena Doviz system"""
        try:
            logger.info("🔐 Attempting to login...")
            
            # Navigate to login page
            await self.page.goto(f"{TestConfig.BASE_URL}/accounts/login/")
            await self.page.wait_for_load_state('networkidle')
            
            # Fill login form
            await self.page.fill('input[name="username"]', TestConfig.TEST_USERNAME)
            await self.page.fill('input[name="password"]', TestConfig.TEST_PASSWORD)
            
            # Submit form
            await self.page.click('button[type="submit"]')
            await self.page.wait_for_load_state('networkidle')
            
            # Check if login was successful
            if '/dashboard/' in self.page.url or 'dashboard' in await self.page.title():
                logger.info("✅ Login successful")
                return True
            else:
                logger.error("❌ Login failed")
                return False
                
        except Exception as e:
            logger.error(f"💥 Login error: {str(e)}")
            return False
    
    async def take_screenshot(self, name: str, full_page: bool = None) -> str:
        """Take a screenshot and return the file path"""
        if full_page is None:
            full_page = TestConfig.FULL_PAGE_SCREENSHOTS
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{name}.png"
        filepath = SCREENSHOTS_DIR / filename
        
        await self.page.screenshot(
            path=str(filepath),
            full_page=full_page
        )
        
        logger.info(f"📸 Screenshot saved: {filename}")
        return str(filepath)
    
    async def start_test(self, test_name: str, transaction_type: str) -> None:
        """Start a new test"""
        self.current_test = TestResult(
            test_name=test_name,
            transaction_type=transaction_type,
            status='RUNNING',
            start_time=datetime.now(),
            end_time=datetime.now(),  # Will be updated when test ends
            duration_ms=0
        )
        
        # Clear logs for this test
        self.console_logs.clear()
        self.network_logs.clear()
        
        logger.info(f"🧪 Starting test: {test_name} ({transaction_type})")
    
    async def end_test(self, status: str, errors: List[str] = None) -> TestResult:
        """End the current test and return results"""
        if not self.current_test:
            raise ValueError("No active test to end")
        
        end_time = datetime.now()
        self.current_test.end_time = end_time
        self.current_test.duration_ms = int((end_time - self.current_test.start_time).total_seconds() * 1000)
        self.current_test.status = status
        
        if errors:
            self.current_test.errors.extend(errors)
        
        # Take screenshot on failure or if configured
        if (status == 'FAIL' and TestConfig.SCREENSHOT_ON_FAILURE) or \
           (status == 'PASS' and TestConfig.SCREENSHOT_ON_SUCCESS):
            screenshot_path = await self.take_screenshot(f"{self.current_test.test_name}_{status}")
            self.current_test.screenshot_path = screenshot_path
        
        # Add to results
        self.test_results.append(self.current_test)
        
        # Log result
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        logger.info(f"{status_emoji} Test completed: {self.current_test.test_name} - {status} ({self.current_test.duration_ms}ms)")
        
        result = self.current_test
        self.current_test = None
        return result
    
    async def cleanup(self) -> None:
        """Cleanup browser resources"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        
        logger.info("🧹 Browser cleanup complete")
    
    def get_test_summary(self) -> Dict[str, Any]:
        """Get summary of all test results"""
        total_tests = len(self.test_results)
        passed = len([r for r in self.test_results if r.status == 'PASS'])
        failed = len([r for r in self.test_results if r.status == 'FAIL'])
        errors = len([r for r in self.test_results if r.status == 'ERROR'])
        
        total_duration = sum(r.duration_ms for r in self.test_results)
        avg_duration = total_duration / total_tests if total_tests > 0 else 0
        
        return {
            'total_tests': total_tests,
            'passed': passed,
            'failed': failed,
            'errors': errors,
            'success_rate': (passed / total_tests * 100) if total_tests > 0 else 0,
            'total_duration_ms': total_duration,
            'average_duration_ms': avg_duration,
            'test_results': [asdict(r) for r in self.test_results]
        }

class TransactionFormAutomation:
    """Specialized class for automating transaction forms"""

    def __init__(self, browser_automation: BrowserAutomation):
        self.browser = browser_automation
        self.page = browser_automation.page

    async def navigate_to_transaction_form(self, transaction_type: str) -> bool:
        """Navigate to a specific transaction form"""
        try:
            if transaction_type not in TRANSACTION_TYPES:
                raise ValueError(f"Unknown transaction type: {transaction_type}")

            config = TRANSACTION_TYPES[transaction_type]
            url = f"{TestConfig.BASE_URL}{config['url_path']}"

            logger.info(f"🧭 Navigating to {config['name']} form: {url}")

            await self.page.goto(url)
            await self.page.wait_for_load_state('networkidle')

            # Wait for form to be visible
            form_selector = f"#{config['form_id']}"
            await self.page.wait_for_selector(form_selector, timeout=10000)

            logger.info(f"✅ Successfully loaded {config['name']} form")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to navigate to {transaction_type} form: {str(e)}")
            return False

    async def fill_form_field(self, field_name: str, value: str, field_type: str = 'input') -> bool:
        """Fill a form field with the given value"""
        try:
            if field_type == 'select':
                # Handle dropdown selection
                await self.page.select_option(f'select[name="{field_name}"]', value)
                # Wait for any AJAX calls triggered by selection
                await self.page.wait_for_timeout(1000)
            elif field_type == 'input':
                # Handle text input
                await self.page.fill(f'input[name="{field_name}"]', str(value))
            elif field_type == 'textarea':
                # Handle textarea
                await self.page.fill(f'textarea[name="{field_name}"]', str(value))

            logger.debug(f"📝 Filled {field_name} with value: {value}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to fill field {field_name}: {str(e)}")
            return False

    async def wait_for_dropdown_options(self, field_name: str, timeout: int = 10000) -> bool:
        """Wait for dropdown options to load"""
        try:
            selector = f'select[name="{field_name}"] option:not([value=""])'
            await self.page.wait_for_selector(selector, timeout=timeout)

            # Count options
            options = await self.page.query_selector_all(selector)
            logger.debug(f"🔄 Dropdown {field_name} loaded with {len(options)} options")
            return True

        except Exception as e:
            logger.warning(f"⚠️ Dropdown {field_name} options not loaded: {str(e)}")
            return False

    async def get_dropdown_options(self, field_name: str) -> List[Dict[str, str]]:
        """Get all options from a dropdown"""
        try:
            options = []
            elements = await self.page.query_selector_all(f'select[name="{field_name}"] option')

            for element in elements:
                value = await element.get_attribute('value')
                text = await element.inner_text()
                if value:  # Skip empty options
                    options.append({'value': value, 'text': text})

            return options

        except Exception as e:
            logger.error(f"❌ Failed to get dropdown options for {field_name}: {str(e)}")
            return []

    async def submit_form(self, action: str = 'submit') -> bool:
        """Submit the transaction form"""
        try:
            if action == 'submit':
                button_selector = 'button[type="submit"], input[type="submit"], .btn-submit'
            elif action == 'save_draft':
                button_selector = '.btn-draft, button[name="action"][value="draft"]'
            else:
                button_selector = f'button[name="action"][value="{action}"]'

            # Take screenshot before submission
            await self.browser.take_screenshot(f"before_submit_{action}")

            # Click submit button
            await self.page.click(button_selector)

            # Wait for response
            await self.page.wait_for_load_state('networkidle', timeout=30000)

            # Take screenshot after submission
            await self.browser.take_screenshot(f"after_submit_{action}")

            logger.info(f"📤 Form submitted with action: {action}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to submit form: {str(e)}")
            return False

    async def check_form_validation_errors(self) -> List[str]:
        """Check for form validation errors"""
        errors = []

        try:
            # Check for Django form errors
            error_elements = await self.page.query_selector_all('.alert-danger, .error, .invalid-feedback, .field-error')
            for element in error_elements:
                error_text = await element.inner_text()
                if error_text.strip():
                    errors.append(error_text.strip())

            # Check for JavaScript validation errors
            js_errors = await self.page.query_selector_all('.is-invalid, .has-error')
            for element in js_errors:
                field_name = await element.get_attribute('name') or await element.get_attribute('id')
                if field_name:
                    errors.append(f"Validation error on field: {field_name}")

        except Exception as e:
            logger.error(f"❌ Error checking validation errors: {str(e)}")

        return errors

    async def get_form_data(self) -> Dict[str, Any]:
        """Extract current form data"""
        form_data = {}

        try:
            # Get all input fields
            inputs = await self.page.query_selector_all('input[name], select[name], textarea[name]')

            for input_element in inputs:
                name = await input_element.get_attribute('name')
                if name:
                    tag_name = await input_element.evaluate('el => el.tagName.toLowerCase()')

                    if tag_name == 'select':
                        value = await input_element.evaluate('el => el.value')
                        text = await input_element.evaluate('el => el.options[el.selectedIndex]?.text || ""')
                        form_data[name] = {'value': value, 'text': text}
                    else:
                        value = await input_element.get_attribute('value') or ''
                        form_data[name] = value

        except Exception as e:
            logger.error(f"❌ Error extracting form data: {str(e)}")

        return form_data
