"""
Health Check Management Command
"""
import json
from django.core.management.base import BaseCommand
from apps.core.monitoring import run_health_check

class Command(BaseCommand):
    help = 'Run system health check and monitoring'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--format',
            choices=['text', 'json'],
            default='text',
            help='Output format (default: text)'
        )
        
        parser.add_argument(
            '--alerts-only',
            action='store_true',
            help='Show only alerts'
        )
    
    def handle(self, *args, **options):
        """Run health check"""
        self.stdout.write("🔍 Running Arena Doviz Health Check...")
        
        try:
            report = run_health_check()
            
            if options['format'] == 'json':
                self.stdout.write(json.dumps(report, indent=2))
            else:
                self.display_text_report(report, options['alerts_only'])
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Health check failed: {str(e)}")
            )
            return
    
    def display_text_report(self, report, alerts_only=False):
        """Display report in text format"""
        status = report['status']
        
        # Status with color
        if status == 'HEALTHY':
            status_display = self.style.SUCCESS(f"✅ {status}")
        elif status == 'WARNING':
            status_display = self.style.WARNING(f"⚠️ {status}")
        else:
            status_display = self.style.ERROR(f"❌ {status}")
        
        self.stdout.write(f"\nSystem Status: {status_display}")
        self.stdout.write(f"Timestamp: {report['timestamp']}")
        
        if not alerts_only:
            # Display metrics
            self.stdout.write("\n📊 System Metrics:")
            metrics = report.get('metrics', {})
            
            if 'cpu_usage' in metrics:
                self.stdout.write(f"   CPU Usage: {metrics['cpu_usage']}%")
            if 'memory_usage' in metrics:
                self.stdout.write(f"   Memory Usage: {metrics['memory_usage']}%")
            if 'disk_usage' in metrics:
                self.stdout.write(f"   Disk Usage: {metrics['disk_usage']:.1f}%")
            if 'db_response_time' in metrics:
                self.stdout.write(f"   Database Response: {metrics['db_response_time']:.2f}ms")
            
            self.stdout.write("\n📈 Application Metrics:")
            if 'transactions_today' in metrics:
                self.stdout.write(f"   Transactions Today: {metrics['transactions_today']}")
            if 'pending_transactions' in metrics:
                self.stdout.write(f"   Pending Transactions: {metrics['pending_transactions']}")
            if 'active_customers' in metrics:
                self.stdout.write(f"   Active Customers: {metrics['active_customers']}")
        
        # Display alerts
        alerts = report.get('alerts', [])
        if alerts:
            self.stdout.write(f"\n🚨 Alerts ({len(alerts)}):")
            for alert in alerts:
                level = alert['level']
                message = alert['message']
                
                if level == 'CRITICAL':
                    alert_display = self.style.ERROR(f"   [CRITICAL] {message}")
                elif level == 'WARNING':
                    alert_display = self.style.WARNING(f"   [WARNING] {message}")
                elif level == 'ERROR':
                    alert_display = self.style.ERROR(f"   [ERROR] {message}")
                else:
                    alert_display = f"   [INFO] {message}"
                
                self.stdout.write(alert_display)
        else:
            self.stdout.write(f"\n✅ No alerts")
        
        self.stdout.write("")
