{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Reports" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">{% trans "Reports" %}</h1>
                    <p class="text-muted">{% trans "Generate and manage financial reports" %}</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#generateReportModal">
                        <i class="fas fa-plus"></i> {% trans "Generate Report" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Templates -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Available Report Templates" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="reportTemplates">
                        <!-- Report templates will be loaded here -->
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                                    <h6 class="card-title">{% trans "Transaction Summary" %}</h6>
                                    <p class="card-text text-muted">{% trans "Daily, weekly, and monthly transaction summaries" %}</p>
                                    <button class="btn btn-primary btn-sm" onclick="generateReport('transaction_summary')">
                                        {% trans "Generate" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-balance-scale fa-3x text-success mb-3"></i>
                                    <h6 class="card-title">{% trans "Balance Report" %}</h6>
                                    <p class="card-text text-muted">{% trans "Current balances by location and currency" %}</p>
                                    <button class="btn btn-success btn-sm" onclick="generateReport('balance_report')">
                                        {% trans "Generate" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-3x text-info mb-3"></i>
                                    <h6 class="card-title">{% trans "Customer Report" %}</h6>
                                    <p class="card-text text-muted">{% trans "Customer activity and transaction history" %}</p>
                                    <button class="btn btn-info btn-sm" onclick="generateReport('customer_report')">
                                        {% trans "Generate" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-invoice fa-3x text-warning mb-3"></i>
                                    <h6 class="card-title">{% trans "Customer Statement" %}</h6>
                                    <p class="card-text text-muted">{% trans "Detailed customer statement with transactions and balances" %}</p>
                                    <button class="btn btn-warning btn-sm" onclick="generateReport('customer_statement')">
                                        {% trans "Generate" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-secondary">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-3x text-secondary mb-3"></i>
                                    <h6 class="card-title">{% trans "Profit & Loss" %}</h6>
                                    <p class="card-text text-muted">{% trans "Financial performance analysis" %}</p>
                                    <button class="btn btn-secondary btn-sm" onclick="generateReport('profit_loss')">
                                        {% trans "Generate" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Generated Reports -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Generated Reports" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="reportsTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Report Name" %}</th>
                                    <th>{% trans "Type" %}</th>
                                    <th>{% trans "Generated By" %}</th>
                                    <th>{% trans "Generated At" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "File Size" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Reports will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Generate Report Modal -->
<div class="modal fade" id="generateReportModal" tabindex="-1" aria-labelledby="generateReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="generateReportModalLabel">{% trans "Generate Report" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="generateReportForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reportType" class="form-label">{% trans "Report Type" %}</label>
                                <select class="form-select" id="reportType" name="report_type" required>
                                    <option value="">{% trans "Select report type" %}</option>
                                    <option value="transaction_summary">{% trans "Transaction Summary" %}</option>
                                    <option value="balance_report">{% trans "Balance Report" %}</option>
                                    <option value="customer_report">{% trans "Customer Report" %}</option>
                                    <option value="customer_statement">{% trans "Customer Statement" %}</option>
                                    <option value="profit_loss">{% trans "Profit & Loss" %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reportFormat" class="form-label">{% trans "Format" %}</label>
                                <select class="form-select" id="reportFormat" name="format" required>
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="csv">CSV</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dateFrom" class="form-label">{% trans "From Date" %} <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="dateFrom" name="date_from" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dateTo" class="form-label">{% trans "To Date" %} <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="dateTo" name="date_to" required>
                            </div>
                        </div>
                    </div>
                    <!-- Customer Selection (shown for customer reports) -->
                    <div class="row" id="customerSelectionRow" style="display: block;">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="customer" class="form-label">{% trans "Customer" %} <span class="text-danger">*</span></label>
                                <select class="form-select" id="customer" name="customer_id">
                                    <option value="">{% trans "Select Customer" %}</option>
                                </select>
                                <div class="form-text">{% trans "Select a specific customer for detailed statement generation" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">{% trans "Location" %}</label>
                                <select class="form-select" id="location" name="location">
                                    <option value="">{% trans "All Locations" %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency" class="form-label">{% trans "Currency" %}</label>
                                <select class="form-select" id="currency" name="currency">
                                    <option value="">{% trans "All Currencies" %}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" onclick="submitReportGeneration()">
                    <i class="fas fa-cog fa-spin d-none" id="generateSpinner"></i>
                    {% trans "Generate Report" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#reportsTable').DataTable({
        ajax: {
            url: '/api/v1/reports/generated/',
            headers: {
                'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
            },
            dataSrc: 'results'
        },
        columns: [
            { data: 'title' },
            { data: 'template_type' },
            { data: 'generated_by_name' },
            {
                data: 'created_at',
                render: function(data) {
                    return new Date(data).toLocaleString();
                }
            },
            { 
                data: 'status',
                render: function(data) {
                    const statusClass = data === 'completed' ? 'success' : 
                                      data === 'failed' ? 'danger' : 'warning';
                    return `<span class="badge bg-${statusClass}">${data}</span>`;
                }
            },
            { data: 'file_size_formatted' },
            {
                data: null,
                render: function(data, type, row) {
                    let actions = '';
                    if (row.status === 'completed' && row.is_downloadable) {
                        actions += `<a href="/api/v1/reports/generated/${row.id}/download/" class="btn btn-sm btn-outline-primary me-1" download>
                                      <i class="fas fa-download"></i>
                                    </a>`;
                    }
                    actions += `<button class="btn btn-sm btn-outline-danger" onclick="deleteReport('${row.id}')">
                                  <i class="fas fa-trash"></i>
                                </button>`;
                    return actions;
                }
            }
        ],
        responsive: true,
        order: [[3, 'desc']],
        pageLength: 25
    });

    // Load locations, currencies, and customers for filters
    loadLocations();
    loadCurrencies();
    loadCustomers();

    // Handle report type change to show/hide customer selection
    const reportTypeSelect = document.getElementById('reportType');
    if (reportTypeSelect) {
        reportTypeSelect.addEventListener('change', function() {
            const reportType = this.value;
            const customerRow = document.getElementById('customerSelectionRow');
            const customerSelect = document.getElementById('customer');

            console.log('Report type changed to:', reportType);
            console.log('Customer row element:', customerRow);
            console.log('Customer select element:', customerSelect);

            if (reportType === 'customer_report' || reportType === 'customer_statement') {
                console.log('Showing customer selection');
                if (customerRow) {
                    customerRow.style.display = 'block';
                }
                if (customerSelect) {
                    customerSelect.required = true;
                }
            } else {
                console.log('Hiding customer selection');
                if (customerRow) {
                    customerRow.style.display = 'none';
                }
                if (customerSelect) {
                    customerSelect.required = false;
                    customerSelect.value = '';
                }
            }
        });

        // Also trigger the change event on page load to set initial state
        reportTypeSelect.dispatchEvent(new Event('change'));
    } else {
        console.error('Report type select element not found');
    }
});

function loadLocations() {
    fetch('/api/v1/locations/locations/', {
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        const select = document.getElementById('location');
        data.results.forEach(location => {
            const option = document.createElement('option');
            option.value = location.id;
            option.textContent = location.name;
            select.appendChild(option);
        });
    })
    .catch(error => console.error('Error loading locations:', error));
}

function loadCurrencies() {
    fetch('/api/v1/currencies/currencies/', {
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        const select = document.getElementById('currency');
        data.results.forEach(currency => {
            const option = document.createElement('option');
            option.value = currency.id;
            option.textContent = `${currency.code} - ${currency.name}`;
            select.appendChild(option);
        });
    })
    .catch(error => console.error('Error loading currencies:', error));
}

function loadCustomers() {
    console.log('Loading customers...');
    fetch('/api/v1/customers/customers/', {
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
        }
    })
    .then(response => {
        console.log('Customers API response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Customers data received:', data);
        const select = document.getElementById('customer');
        if (!select) {
            console.error('Customer select element not found');
            return;
        }

        // Clear existing options except the first one
        select.innerHTML = '<option value="">{% trans "Select Customer" %}</option>';

        if (data.results && data.results.length > 0) {
            data.results.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = customer.display_name ||
                                   `${customer.first_name || ''} ${customer.last_name || ''}`.trim() ||
                                   customer.company_name ||
                                   customer.customer_code ||
                                   'Unknown Customer';
                select.appendChild(option);
            });
            console.log(`Loaded ${data.results.length} customers`);
        } else {
            console.log('No customers found');
        }
    })
    .catch(error => {
        console.error('Error loading customers:', error);
    });
}

function generateReport(reportType) {
    console.log('Generating report of type:', reportType);
    const reportTypeSelect = document.getElementById('reportType');
    reportTypeSelect.value = reportType;

    // Set default date range (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const dateFromInput = document.getElementById('dateFrom');
    const dateToInput = document.getElementById('dateTo');

    if (dateFromInput && !dateFromInput.value) {
        dateFromInput.value = thirtyDaysAgo.toISOString().split('T')[0];
    }
    if (dateToInput && !dateToInput.value) {
        dateToInput.value = today.toISOString().split('T')[0];
    }

    console.log('Set date range:', dateFromInput.value, 'to', dateToInput.value);

    // Trigger change event to show/hide customer selection
    reportTypeSelect.dispatchEvent(new Event('change'));

    const modal = new bootstrap.Modal(document.getElementById('generateReportModal'));
    modal.show();
}


function submitReportGeneration() {
    const form = document.getElementById('generateReportForm');
    const formData = new FormData(form);
    const spinner = document.getElementById('generateSpinner');

    // Show loading spinner
    spinner.classList.remove('d-none');

    // Debug form data
    console.log('Form data entries:');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
    }

    const data = {
        report_type: formData.get('report_type'),
        format: formData.get('format'),
        parameters: {
            date_from: formData.get('date_from'),
            date_to: formData.get('date_to'),
            location: formData.get('location') || null,
            currency: formData.get('currency') || null,
            customer_id: formData.get('customer_id') || null
        }
    };

    console.log('Report generation data:', data);
    
    fetch('/api/v1/reports/generated/generate/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        spinner.classList.add('d-none');
        console.log('Report generation response:', data);

        // Check for various success indicators
        if (data.report && data.report.id) {
            // Close modal and refresh table
            const modal = bootstrap.Modal.getInstance(document.getElementById('generateReportModal'));
            modal.hide();
            $('#reportsTable').DataTable().ajax.reload();

            // Show success message
            showAlert('success', data.message || '{% trans "Report generation started successfully" %}');
        } else if (data.message && data.message.includes('successfully')) {
            // Alternative success check
            const modal = bootstrap.Modal.getInstance(document.getElementById('generateReportModal'));
            modal.hide();
            $('#reportsTable').DataTable().ajax.reload();
            showAlert('success', data.message);
        } else if (data.download_url) {
            // Direct download case
            const modal = bootstrap.Modal.getInstance(document.getElementById('generateReportModal'));
            modal.hide();
            $('#reportsTable').DataTable().ajax.reload();
            window.open(data.download_url, '_blank');
            showAlert('success', '{% trans "Report generated and downloaded successfully" %}');
        } else {
            // Show error with details if available
            const errorMsg = data.error || data.message || '{% trans "Failed to start report generation" %}';
            showAlert('danger', errorMsg);
        }
    })
    .catch(error => {
        spinner.classList.add('d-none');
        console.error('Error generating report:', error);
        showAlert('danger', '{% trans "Error generating report: " %}' + error.message);
    });
}

function deleteReport(reportId) {
    if (confirm('{% trans "Are you sure you want to delete this report?" %}')) {
        fetch(`/api/v1/reports/generated/${reportId}/`, {
            method: 'DELETE',
            headers: {
                'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
            }
        })
        .then(response => {
            if (response.ok) {
                $('#reportsTable').DataTable().ajax.reload();
                showAlert('success', '{% trans "Report deleted successfully" %}');
            } else {
                showAlert('danger', '{% trans "Failed to delete report" %}');
            }
        })
        .catch(error => {
            console.error('Error deleting report:', error);
            showAlert('danger', '{% trans "Error deleting report" %}');
        });
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
