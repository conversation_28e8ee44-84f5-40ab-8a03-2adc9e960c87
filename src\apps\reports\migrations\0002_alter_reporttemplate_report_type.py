# Generated by Django 4.2.23 on 2025-08-17 09:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('reports', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='reporttemplate',
            name='report_type',
            field=models.CharField(choices=[('customer_statement', 'Customer Statement'), ('balance_summary', 'Balance Summary'), ('balance_report', 'Balance Report'), ('transaction_report', 'Transaction Report'), ('profit_loss', 'Profit & Loss'), ('daily_summary', 'Daily Summary'), ('exchange_rate_report', 'Exchange Rate Report'), ('audit_report', 'Audit Report'), ('custom', 'Custom Report')], help_text='Type of report this template generates', max_length=30, verbose_name='Report type'),
        ),
    ]
