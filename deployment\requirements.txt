# Arena Doviz Exchange Accounting System - Python Dependencies

# Django and core packages
Django>=4.2,<5.0
djangorestframework>=3.14
django-cors-headers>=4.3.0

# Database
psycopg[binary]>=3.1

# Cache and sessions
django-redis>=5.4.0
redis>=5.0.0

# WSGI server
gunicorn>=21.2
uvicorn>=0.30

# Environment management
django-environ>=0.11
python-dotenv>=1.0.0

# Utilities
Pillow>=10.0.0
python-dateutil>=2.8.0
pytz>=2023.3

# Authentication and security
djangorestframework-simplejwt>=5.3.0
cryptography>=41.0.0

# API documentation
drf-spectacular>=0.26.0

# Development tools (optional)
django-debug-toolbar>=4.2.0
django-extensions>=3.2.0

# Excel and PDF generation
openpyxl>=3.1.0
reportlab>=4.0.0

# Background tasks
celery>=5.3.0

