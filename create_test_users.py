#!/usr/bin/env python3
"""
Create test user accounts for Arena Doviz production testing
"""
import os
import sys
import django
from pathlib import Path

# Setup Django
sys.path.insert(0, str(Path(__file__).parent / 'src'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from apps.locations.models import Location

User = get_user_model()

def create_test_users():
    """Create comprehensive test user accounts for all roles"""
    print("🔧 Creating Arena Doviz Test User Accounts...")
    print("=" * 60)
    
    # Get locations for assignment
    dubai = Location.objects.filter(code='DXB').first()
    istanbul = Location.objects.filter(code='IST').first()
    
    # Define test users for each role
    test_users = [
        {
            'username': 'admin_test',
            'password': 'Admin123!@#',
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'User',
            'role': 'admin',
            'location': None,  # Admin can access all locations
            'is_staff': True,
            'is_superuser': True,
            'description': 'System Administrator - Full access to all features'
        },
        {
            'username': 'accountant_test',
            'password': 'Account123!@#',
            'email': '<EMAIL>',
            'first_name': 'Sarah',
            'last_name': 'Johnson',
            'role': 'accountant',
            'location': dubai,
            'is_staff': True,
            'is_superuser': False,
            'description': 'Senior Accountant - Financial operations and reporting'
        },
        {
            'username': 'branch_employee_test',
            'password': 'Branch123!@#',
            'email': '<EMAIL>',
            'first_name': 'Ahmed',
            'last_name': 'Hassan',
            'role': 'branch_employee',
            'location': dubai,
            'is_staff': False,
            'is_superuser': False,
            'description': 'Branch Employee - Daily transaction processing'
        },
        {
            'username': 'viewer_test',
            'password': 'Viewer123!@#',
            'email': '<EMAIL>',
            'first_name': 'Maria',
            'last_name': 'Garcia',
            'role': 'viewer',
            'location': istanbul,
            'is_staff': False,
            'is_superuser': False,
            'description': 'Viewer - Read-only access for monitoring'
        },
        {
            'username': 'courier_test',
            'password': 'Courier123!@#',
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Smith',
            'role': 'courier',
            'location': dubai,
            'is_staff': False,
            'is_superuser': False,
            'description': 'Courier - Cash pickup and delivery services'
        }
    ]
    
    created_users = []
    
    for user_data in test_users:
        try:
            # Check if user already exists
            if User.objects.filter(username=user_data['username']).exists():
                print(f"⚠️ User {user_data['username']} already exists, updating...")
                user = User.objects.get(username=user_data['username'])
                
                # Update user details
                user.email = user_data['email']
                user.first_name = user_data['first_name']
                user.last_name = user_data['last_name']
                user.role = user_data['role']
                user.location = user_data['location']
                user.is_staff = user_data['is_staff']
                user.is_superuser = user_data['is_superuser']
                user.set_password(user_data['password'])
                user.save()
                
            else:
                # Create new user
                user = User.objects.create_user(
                    username=user_data['username'],
                    password=user_data['password'],
                    email=user_data['email'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                    role=user_data['role'],
                    location=user_data['location'],
                    is_staff=user_data['is_staff'],
                    is_superuser=user_data['is_superuser']
                )
                print(f"✅ Created user: {user_data['username']}")
            
            # Add user to appropriate group
            try:
                if user_data['role'] == 'admin':
                    group = Group.objects.get(name='Admin')
                elif user_data['role'] == 'accountant':
                    group = Group.objects.get(name='Accountant')
                elif user_data['role'] == 'branch_employee':
                    group = Group.objects.get(name='Branch Employee')
                elif user_data['role'] == 'viewer':
                    group = Group.objects.get(name='Viewer')
                elif user_data['role'] == 'courier':
                    group = Group.objects.get(name='Courier')
                
                user.groups.clear()  # Remove existing groups
                user.groups.add(group)
                print(f"   Added to group: {group.name}")
                
            except Group.DoesNotExist:
                print(f"   ⚠️ Group for role {user_data['role']} not found")
            
            created_users.append({
                'user': user,
                'credentials': {
                    'username': user_data['username'],
                    'password': user_data['password'],
                    'role': user_data['role'],
                    'description': user_data['description']
                }
            })
            
        except Exception as e:
            print(f"❌ Error creating user {user_data['username']}: {str(e)}")
    
    # Display credentials summary
    print("\n" + "=" * 60)
    print("🔑 ARENA DOVIZ TEST USER CREDENTIALS")
    print("=" * 60)
    
    for user_info in created_users:
        creds = user_info['credentials']
        user = user_info['user']
        
        print(f"\n👤 {creds['role'].upper()} USER:")
        print(f"   Username: {creds['username']}")
        print(f"   Password: {creds['password']}")
        print(f"   Role: {creds['role']}")
        print(f"   Name: {user.get_full_name()}")
        print(f"   Email: {user.email}")
        print(f"   Location: {user.location.name if user.location else 'All Locations'}")
        print(f"   Description: {creds['description']}")
        print(f"   Permissions: {'Superuser' if user.is_superuser else 'Staff' if user.is_staff else 'Regular User'}")
    
    print(f"\n📊 Summary: {len(created_users)} test users created/updated successfully")
    print("\n🔗 Login URL: http://127.0.0.1:8000/accounts/login/")
    print("🏠 Home URL: http://127.0.0.1:8000/")
    print("📊 Dashboard URL: http://127.0.0.1:8000/")
    print("🔍 Monitoring URL: http://127.0.0.1:8000/monitoring/")
    
    return created_users

def test_user_login():
    """Test user login functionality"""
    print("\n🔍 Testing User Login Functionality...")
    
    from django.test import Client
    
    client = Client()
    
    # Test login with admin user
    response = client.post('/accounts/login/', {
        'username': 'admin_test',
        'password': 'Admin123!@#'
    })
    
    if response.status_code in [200, 302]:
        print("✅ Admin login test: PASSED")
    else:
        print(f"❌ Admin login test: FAILED (HTTP {response.status_code})")
    
    # Test accessing protected page
    response = client.get('/dashboard/')
    if response.status_code in [200, 302]:
        print("✅ Dashboard access test: PASSED")
    else:
        print(f"❌ Dashboard access test: FAILED (HTTP {response.status_code})")

if __name__ == "__main__":
    users = create_test_users()
    test_user_login()
    
    print("\n🎉 User account creation completed successfully!")
    print("All test users are ready for production testing.")
