ERROR 2025-08-13 02:53:27,463 apps 9900 10532 Failed to initialize authentication services: [<PERSON>rrno 11001] getaddrinfo failed
ERROR 2025-08-13 02:53:34,274 apps 9900 10532 Failed to setup cache: [<PERSON><PERSON><PERSON> 11001] getaddrin<PERSON> failed
ERROR 2025-08-13 02:53:47,936 apps 10392 6812 Failed to initialize authentication services: [Errno 11001] getaddrinfo failed
ERROR 2025-08-13 02:53:54,729 apps 10392 6812 Failed to setup cache: [Errno 11001] getaddrinfo failed
ERROR 2025-08-13 02:54:46,822 apps 4104 2860 Failed to setup cache: no such table: arena_cache_table
ERROR 2025-08-13 02:56:30,389 middleware 9992 9820 Exception in GET /customers/add/: customers/add.html
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Program Files\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: customers/add.html
ERROR 2025-08-13 02:56:30,395 log 9992 9820 Internal Server Error: /customers/add/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Program Files\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: customers/add.html
ERROR 2025-08-13 02:59:59,586 models 3956 8792 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-13 03:00:32,830 models 3956 8792 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-13 03:01:48,557 models 3956 1308 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-13 03:01:48,630 models 3956 7256 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-13 03:02:25,761 models 3956 4964 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-13 03:02:34,765 models 3956 1308 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-13 03:05:00,077 models 3956 1556 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-13 03:06:44,430 models 3956 1308 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:06:21,777 encryption 8956 2752 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 05:06:21,807 fields 8956 2752 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 05:06:21,808 models 8956 2752 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 05:06:22,105 models 8956 10460 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:06:22,106 models 8956 2728 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:06:22,314 models 8956 4444 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:06:22,475 models 8956 11472 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:06:22,535 models 8956 11984 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:11:22,369 models 8956 10460 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:11:22,482 models 8956 2752 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:11:22,626 models 8956 2728 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:11:22,630 models 8956 4444 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:11:22,635 models 8956 11472 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 05:11:22,704 models 8956 11984 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:08:15,185 encryption 280 7564 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:08:15,185 fields 280 7564 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:08:15,186 models 280 7564 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:08:23,939 models 280 8916 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:18:58,255 models 280 580 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:18:58,262 models 280 7564 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:18:58,471 models 280 7584 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:18:58,537 models 280 1288 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:18:58,763 models 280 8916 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:18:59,638 models 280 2196 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:11,795 models 280 1288 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:12,348 models 280 2196 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:14,110 models 280 8916 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:25,499 models 280 7564 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:31,454 models 280 8916 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:31,510 models 280 7564 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:31,630 models 280 7584 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:32,079 models 280 1288 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:32,166 models 280 2196 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:19:49,351 models 280 7584 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:20:02,195 middleware 280 2196 Exception in GET /transactions/4b2eba38-6dc4-4fd1-b35d-0415d8ae9b58/: Invalid field name(s) given in select_related: 'completed_by'. Choices are: created_by, updated_by, deleted_by, transaction_type, customer, location, from_currency, to_currency, commission_currency, courier, approved_by, parent_transaction
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 54, in transaction_detail
    transaction = get_object_or_404(
        Transaction.objects.select_related(
    ...<5 lines>...
        is_deleted=False
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 736, in as_sql
    extra_select, order_by, group_by = self.pre_sql_setup(
                                       ~~~~~~~~~~~~~~~~~~^
        with_col_aliases=with_col_aliases or bool(combinator),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 84, in pre_sql_setup
    self.setup_query(with_col_aliases=with_col_aliases)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 73, in setup_query
    self.select, self.klass_info, self.annotation_col_map = self.get_select(
                                                            ~~~~~~~~~~~~~~~^
        with_col_aliases=with_col_aliases,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 279, in get_select
    related_klass_infos = self.get_related_selections(select, select_mask)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1367, in get_related_selections
    raise FieldError(
    ...<6 lines>...
    )
django.core.exceptions.FieldError: Invalid field name(s) given in select_related: 'completed_by'. Choices are: created_by, updated_by, deleted_by, transaction_type, customer, location, from_currency, to_currency, commission_currency, courier, approved_by, parent_transaction
ERROR 2025-08-16 10:20:02,377 log 280 2196 Internal Server Error: /transactions/4b2eba38-6dc4-4fd1-b35d-0415d8ae9b58/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 54, in transaction_detail
    transaction = get_object_or_404(
        Transaction.objects.select_related(
    ...<5 lines>...
        is_deleted=False
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 736, in as_sql
    extra_select, order_by, group_by = self.pre_sql_setup(
                                       ~~~~~~~~~~~~~~~~~~^
        with_col_aliases=with_col_aliases or bool(combinator),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 84, in pre_sql_setup
    self.setup_query(with_col_aliases=with_col_aliases)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 73, in setup_query
    self.select, self.klass_info, self.annotation_col_map = self.get_select(
                                                            ~~~~~~~~~~~~~~~^
        with_col_aliases=with_col_aliases,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 279, in get_select
    related_klass_infos = self.get_related_selections(select, select_mask)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1367, in get_related_selections
    raise FieldError(
    ...<6 lines>...
    )
django.core.exceptions.FieldError: Invalid field name(s) given in select_related: 'completed_by'. Choices are: created_by, updated_by, deleted_by, transaction_type, customer, location, from_currency, to_currency, commission_currency, courier, approved_by, parent_transaction
ERROR 2025-08-16 10:20:05,913 models 280 8916 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:21:05,133 middleware 280 7584 Exception in GET /transactions/e395db67-11b8-4e76-98f0-9b6fb9948acf/: Invalid field name(s) given in select_related: 'completed_by'. Choices are: created_by, updated_by, deleted_by, transaction_type, customer, location, from_currency, to_currency, commission_currency, courier, approved_by, parent_transaction
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 54, in transaction_detail
    transaction = get_object_or_404(
        Transaction.objects.select_related(
    ...<5 lines>...
        is_deleted=False
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 736, in as_sql
    extra_select, order_by, group_by = self.pre_sql_setup(
                                       ~~~~~~~~~~~~~~~~~~^
        with_col_aliases=with_col_aliases or bool(combinator),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 84, in pre_sql_setup
    self.setup_query(with_col_aliases=with_col_aliases)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 73, in setup_query
    self.select, self.klass_info, self.annotation_col_map = self.get_select(
                                                            ~~~~~~~~~~~~~~~^
        with_col_aliases=with_col_aliases,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 279, in get_select
    related_klass_infos = self.get_related_selections(select, select_mask)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1367, in get_related_selections
    raise FieldError(
    ...<6 lines>...
    )
django.core.exceptions.FieldError: Invalid field name(s) given in select_related: 'completed_by'. Choices are: created_by, updated_by, deleted_by, transaction_type, customer, location, from_currency, to_currency, commission_currency, courier, approved_by, parent_transaction
ERROR 2025-08-16 10:21:05,157 log 280 7584 Internal Server Error: /transactions/e395db67-11b8-4e76-98f0-9b6fb9948acf/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 54, in transaction_detail
    transaction = get_object_or_404(
        Transaction.objects.select_related(
    ...<5 lines>...
        is_deleted=False
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 85, in get_object_or_404
    return queryset.get(*args, **kwargs)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 736, in as_sql
    extra_select, order_by, group_by = self.pre_sql_setup(
                                       ~~~~~~~~~~~~~~~~~~^
        with_col_aliases=with_col_aliases or bool(combinator),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 84, in pre_sql_setup
    self.setup_query(with_col_aliases=with_col_aliases)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 73, in setup_query
    self.select, self.klass_info, self.annotation_col_map = self.get_select(
                                                            ~~~~~~~~~~~~~~~^
        with_col_aliases=with_col_aliases,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 279, in get_select
    related_klass_infos = self.get_related_selections(select, select_mask)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1367, in get_related_selections
    raise FieldError(
    ...<6 lines>...
    )
django.core.exceptions.FieldError: Invalid field name(s) given in select_related: 'completed_by'. Choices are: created_by, updated_by, deleted_by, transaction_type, customer, location, from_currency, to_currency, commission_currency, courier, approved_by, parent_transaction
ERROR 2025-08-16 10:21:09,062 models 280 2196 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:24:00,316 models 280 1288 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:24:00,403 models 280 2196 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:24:00,645 models 280 580 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:24:01,233 models 280 7564 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:24:01,236 models 280 8916 Failed to create encrypted audit log: Object of type UUID is not JSON serializable
ERROR 2025-08-16 10:32:32,893 encryption 4764 8064 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:32,900 fields 4764 8064 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:32,894 encryption 4764 10796 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:32,902 models 4764 8064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:32,902 fields 4764 10796 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:32,905 encryption 4764 9664 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:32,949 models 4764 10796 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:32,950 fields 4764 9664 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:32,959 models 4764 9664 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:32,959 encryption 4764 6868 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:32,961 fields 4764 6868 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:32,963 models 4764 6868 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:33,530 encryption 4764 11312 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:33,560 fields 4764 11312 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:33,561 models 4764 11312 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:33,595 encryption 4764 8064 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:33,595 fields 4764 8064 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:33,621 models 4764 8064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:39,652 encryption 4764 3924 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:39,659 fields 4764 3924 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:39,676 models 4764 3924 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:39,717 encryption 4764 11312 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:39,717 fields 4764 11312 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:39,718 models 4764 11312 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:39,823 encryption 4764 8064 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:39,824 fields 4764 8064 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:39,826 models 4764 8064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:39,874 encryption 4764 10796 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:39,875 fields 4764 10796 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:39,876 models 4764 10796 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:40,357 encryption 4764 6868 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:40,358 fields 4764 6868 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:40,359 models 4764 6868 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:40,361 encryption 4764 3924 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:40,363 fields 4764 3924 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:40,367 models 4764 3924 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:43,157 encryption 4764 6868 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:43,164 fields 4764 6868 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:43,166 models 4764 6868 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:43,212 encryption 4764 3924 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:43,280 fields 4764 3924 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:43,282 models 4764 3924 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:43,314 encryption 4764 9664 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:43,344 fields 4764 9664 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:43,402 models 4764 9664 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:48,924 encryption 4764 8064 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:48,925 fields 4764 8064 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:48,926 models 4764 8064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:57,798 encryption 4764 11312 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:57,800 fields 4764 11312 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:57,832 models 4764 11312 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:57,937 encryption 4764 9664 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:58,040 fields 4764 9664 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:58,041 models 4764 9664 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:58,040 encryption 4764 10796 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:58,040 encryption 4764 8064 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:58,043 fields 4764 10796 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:58,044 fields 4764 8064 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:58,047 models 4764 8064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:58,046 models 4764 10796 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:32:58,463 encryption 4764 3924 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:58,463 fields 4764 3924 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:32:58,464 models 4764 3924 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:34:34,046 encryption 4764 8064 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:34:34,048 fields 4764 8064 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:34:34,048 models 4764 8064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 10:34:35,154 encryption 4764 3924 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:34:35,155 fields 4764 3924 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 10:34:35,155 models 4764 3924 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:52:14,102 encryption 3052 10740 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:52:14,103 fields 3052 10740 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:52:14,103 models 3052 10740 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:52:28,776 encryption 3052 11596 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:52:28,778 fields 3052 11596 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:52:28,778 models 3052 11596 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:53:06,070 encryption 3052 10740 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:06,165 encryption 3052 6996 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:06,375 encryption 3052 11596 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:06,600 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:06,723 fields 3052 10740 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:07,070 fields 3052 6996 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:07,290 fields 3052 11596 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:07,715 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:09,179 models 3052 10740 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:53:09,650 models 3052 6996 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:53:09,821 models 3052 11596 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:53:10,031 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:53:11,958 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:12,595 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:53:12,840 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:07,657 encryption 3052 11596 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:07,789 fields 3052 11596 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:07,849 models 3052 11596 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:08,062 encryption 3052 10740 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:08,063 encryption 3052 2948 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:08,114 fields 3052 10740 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:08,180 fields 3052 2948 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:08,258 models 3052 2948 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:08,252 models 3052 10740 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:08,311 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:08,312 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:08,313 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:09,134 encryption 3052 6996 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:09,135 fields 3052 6996 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:09,135 models 3052 6996 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:10,685 encryption 3052 10740 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:10,686 fields 3052 10740 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:10,686 models 3052 10740 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:11,031 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:11,032 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:11,034 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:11,125 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:11,125 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:11,125 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:44,915 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:44,916 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:44,916 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:54:47,885 encryption 3052 2948 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:47,887 fields 3052 2948 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:54:47,888 models 3052 2948 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:55:05,390 encryption 3052 11596 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:55:05,391 fields 3052 11596 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:55:05,392 models 3052 11596 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:56:17,598 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:17,599 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:17,599 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:56:19,206 encryption 3052 10740 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:19,221 fields 3052 10740 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:19,214 encryption 3052 11596 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:19,223 fields 3052 11596 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:19,223 models 3052 11596 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:56:19,222 models 3052 10740 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:56:19,537 encryption 3052 6996 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:19,607 fields 3052 6996 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:19,663 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:19,664 models 3052 6996 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:56:19,664 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:19,665 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:56:20,413 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:20,413 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:56:20,414 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:57:09,251 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:57:09,253 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:57:09,255 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:57:09,317 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:57:09,318 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:57:09,318 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:57:11,016 encryption 3052 2948 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:57:11,017 fields 3052 2948 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:57:11,017 models 3052 2948 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:58:53,054 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:58:53,055 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:58:53,055 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:58:53,058 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:58:53,065 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:58:53,065 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:59:46,097 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,098 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,101 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:59:46,165 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,174 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,230 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:59:46,462 encryption 3052 10740 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,463 encryption 3052 2948 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,464 fields 3052 10740 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,465 fields 3052 2948 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,466 models 3052 10740 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:59:46,466 models 3052 2948 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:59:46,667 encryption 3052 11596 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,695 fields 3052 11596 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,698 models 3052 11596 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-16 23:59:46,756 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,756 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-16 23:59:46,756 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:00:04,422 encryption 3052 11596 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:04,437 fields 3052 11596 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:04,443 models 3052 11596 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:00:04,500 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:04,510 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:04,519 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:00:14,816 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:14,821 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:14,822 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:00:15,236 encryption 3052 2948 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:15,299 fields 3052 2948 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:15,302 models 3052 2948 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:00:15,300 encryption 3052 11596 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:15,355 fields 3052 11596 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:15,358 models 3052 11596 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:00:15,535 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:15,536 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:15,537 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:00:16,450 encryption 3052 6996 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:16,500 fields 3052 6996 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:16,502 models 3052 6996 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:00:16,501 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:16,504 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:00:16,505 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:01:52,285 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:01:52,287 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:01:52,291 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:01:52,300 encryption 3052 6996 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:01:52,358 fields 3052 6996 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:01:52,360 models 3052 6996 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:01:52,418 encryption 3052 9076 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:01:52,420 fields 3052 9076 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:01:52,421 models 3052 9076 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:01:55,377 encryption 3052 11596 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:01:55,377 fields 3052 11596 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:01:55,378 models 3052 11596 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:02:20,951 encryption 3052 2948 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:02:20,952 fields 3052 2948 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:02:20,952 models 3052 2948 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:02:33,094 encryption 3052 2012 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:02:33,099 fields 3052 2012 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:02:33,103 models 3052 2012 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:41:23,679 encryption 9944 8056 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:41:23,677 encryption 9944 11464 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:41:23,697 fields 9944 8056 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:41:23,698 fields 9944 11464 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:41:23,700 models 9944 8056 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:41:23,701 models 9944 11464 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:41:23,824 encryption 9944 2948 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:41:23,824 fields 9944 2948 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:41:23,824 models 9944 2948 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 00:41:30,703 encryption 9944 8736 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:41:30,704 fields 9944 8736 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 00:41:30,704 models 9944 8736 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:05:42,251 views 5352 9304 Error getting customer stats for CUS000002: Cannot resolve keyword 'amount' into field. Choices are: approved_at, approved_by, approved_by_id, balance_entries, child_transactions, commission_amount, commission_currency, commission_currency_id, completed_at, courier, courier_id, created_at, created_by, created_by_id, customer, customer_id, deleted_at, deleted_by, deleted_by_id, delivery_address, delivery_method, description, documents, exchange_rate, from_amount, from_currency, from_currency_id, id, is_deleted, location, location_id, notes, parent_transaction, parent_transaction_id, reference_number, status, step_number, to_amount, to_currency, to_currency_id, total_steps, tracking_code, transaction_number, transaction_type, transaction_type_id, updated_at, updated_by, updated_by_id
ERROR 2025-08-17 01:05:42,278 log 5352 9304 Internal Server Error: /api/v1/customers/customers/82ecffff-2a6a-4417-b379-98f042a1a25e/stats/
ERROR 2025-08-17 01:10:19,728 middleware 6752 3180 Exception in GET /api/v1/accounts/audit-logs/: Cannot resolve keyword 'model_name' into field. Choices are: action_type, created_at, encrypted_ip_address, encrypted_new_values, encrypted_old_values, encrypted_user_agent, error_message, id, record_id, success, table_name, updated_at, user_id
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\accounts\views.py", line 445, in list
    queryset = queryset.filter(model_name=model_name)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1427, in build_filter
    lookups, parts, reffed_expression = self.solve_lookup_type(arg, summarize)
                                        ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1237, in solve_lookup_type
    _, field, _, lookup_parts = self.names_to_path(lookup_splitted, self.get_meta())
                                ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1725, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'model_name' into field. Choices are: action_type, created_at, encrypted_ip_address, encrypted_new_values, encrypted_old_values, encrypted_user_agent, error_message, id, record_id, success, table_name, updated_at, user_id
ERROR 2025-08-17 01:10:20,078 log 6752 3180 Internal Server Error: /api/v1/accounts/audit-logs/
ERROR 2025-08-17 01:10:44,321 views 6752 12228 Error getting customer stats for CUS000001: Cannot resolve keyword 'amount' into field. Choices are: approved_at, approved_by, approved_by_id, balance_entries, child_transactions, commission_amount, commission_currency, commission_currency_id, completed_at, courier, courier_id, created_at, created_by, created_by_id, customer, customer_id, deleted_at, deleted_by, deleted_by_id, delivery_address, delivery_method, description, documents, exchange_rate, from_amount, from_currency, from_currency_id, id, is_deleted, location, location_id, notes, parent_transaction, parent_transaction_id, reference_number, status, step_number, to_amount, to_currency, to_currency_id, total_steps, tracking_code, transaction_number, transaction_type, transaction_type_id, updated_at, updated_by, updated_by_id
ERROR 2025-08-17 01:10:44,412 log 6752 12228 Internal Server Error: /api/v1/customers/customers/4add5c82-e6c1-4cf6-9e49-9708910ecedc/stats/
ERROR 2025-08-17 01:10:52,688 middleware 6752 4056 Exception in GET /api/v1/accounts/audit-logs/: Cannot resolve keyword 'model_name' into field. Choices are: action_type, created_at, encrypted_ip_address, encrypted_new_values, encrypted_old_values, encrypted_user_agent, error_message, id, record_id, success, table_name, updated_at, user_id
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\accounts\views.py", line 445, in list
    queryset = queryset.filter(model_name=model_name)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1427, in build_filter
    lookups, parts, reffed_expression = self.solve_lookup_type(arg, summarize)
                                        ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1237, in solve_lookup_type
    _, field, _, lookup_parts = self.names_to_path(lookup_splitted, self.get_meta())
                                ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1725, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'model_name' into field. Choices are: action_type, created_at, encrypted_ip_address, encrypted_new_values, encrypted_old_values, encrypted_user_agent, error_message, id, record_id, success, table_name, updated_at, user_id
ERROR 2025-08-17 01:10:52,813 log 6752 4056 Internal Server Error: /api/v1/accounts/audit-logs/
ERROR 2025-08-17 01:11:09,323 middleware 6752 4520 Exception in GET /api/v1/accounts/audit-logs/: Cannot resolve keyword 'model_name' into field. Choices are: action_type, created_at, encrypted_ip_address, encrypted_new_values, encrypted_old_values, encrypted_user_agent, error_message, id, record_id, success, table_name, updated_at, user_id
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\accounts\views.py", line 445, in list
    queryset = queryset.filter(model_name=model_name)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<6 lines>...
        summarize=summarize,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1427, in build_filter
    lookups, parts, reffed_expression = self.solve_lookup_type(arg, summarize)
                                        ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1237, in solve_lookup_type
    _, field, _, lookup_parts = self.names_to_path(lookup_splitted, self.get_meta())
                                ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1725, in names_to_path
    raise FieldError(
    ...<2 lines>...
    )
django.core.exceptions.FieldError: Cannot resolve keyword 'model_name' into field. Choices are: action_type, created_at, encrypted_ip_address, encrypted_new_values, encrypted_old_values, encrypted_user_agent, error_message, id, record_id, success, table_name, updated_at, user_id
ERROR 2025-08-17 01:11:09,420 log 6752 4520 Internal Server Error: /api/v1/accounts/audit-logs/
ERROR 2025-08-17 01:18:40,269 encryption 8000 6184 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:40,278 fields 8000 6184 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:40,376 models 8000 6184 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:18:40,377 encryption 8000 4236 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:40,380 fields 8000 4236 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:40,378 encryption 8000 9420 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:40,381 models 8000 4236 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:18:40,381 fields 8000 9420 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:40,384 models 8000 9420 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:18:49,693 encryption 8000 4236 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:49,695 fields 8000 4236 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:49,696 models 8000 4236 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:18:49,721 encryption 8000 6184 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:49,723 fields 8000 6184 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:49,724 models 8000 6184 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:18:49,754 encryption 8000 9420 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:49,755 fields 8000 9420 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:49,755 models 8000 9420 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:18:49,981 encryption 8000 7700 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:49,988 fields 8000 7700 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:49,999 models 8000 7700 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:18:50,001 encryption 8000 4556 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:50,003 fields 8000 4556 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:18:50,005 models 8000 4556 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:19:01,797 encryption 8000 12180 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:01,798 fields 8000 12180 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:01,796 encryption 8000 7700 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:01,800 models 8000 12180 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:19:01,801 encryption 8000 4556 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:01,802 fields 8000 7700 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:01,803 fields 8000 4556 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:01,804 models 8000 4556 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:19:01,804 models 8000 7700 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:19:01,876 encryption 8000 4236 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:01,876 fields 8000 4236 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:01,876 models 8000 4236 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:19:01,991 encryption 8000 6184 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:01,992 fields 8000 6184 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:02,004 models 8000 6184 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:19:02,016 encryption 8000 12180 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:02,016 fields 8000 12180 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:02,017 models 8000 12180 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:19:15,423 encryption 8000 4556 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:15,423 fields 8000 4556 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:15,424 models 8000 4556 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:19:15,535 encryption 8000 4236 Failed to initialize encryption: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:15,539 fields 8000 4236 Failed to encrypt field value: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-17 01:19:15,539 models 8000 4236 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: Encryption initialization failed: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-17 01:33:12,645 models 5344 1672 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-17 01:51:03,075 models 3484 6392 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-17 02:07:14,958 models 9544 32 Report generation failed: Customer Report Report - 2025-08-17 02:07 - No module named 'apps.balances'
ERROR 2025-08-17 02:07:14,959 views 9544 32 Report generation failed: No module named 'apps.balances'
ERROR 2025-08-17 02:07:14,965 log 9544 32 Internal Server Error: /api/v1/reports/generated/generate/
ERROR 2025-08-17 02:07:24,531 models 9544 2788 Report generation failed: Customer Report Report - 2025-08-17 02:07 - No module named 'apps.balances'
ERROR 2025-08-17 02:07:24,531 views 9544 2788 Report generation failed: No module named 'apps.balances'
ERROR 2025-08-17 02:07:24,535 log 9544 2788 Internal Server Error: /api/v1/reports/generated/generate/
ERROR 2025-08-17 02:09:07,502 middleware 9544 6528 Exception in GET /api/v1/currencies/rates/: name 'models' is not defined
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
                                    ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\currencies\views.py", line 278, in get_queryset
    models.Q(effective_until__isnull=True) | models.Q(effective_until__gt=now)
    ^^^^^^
NameError: name 'models' is not defined
ERROR 2025-08-17 02:09:07,557 log 9544 6528 Internal Server Error: /api/v1/currencies/rates/
ERROR 2025-08-17 02:35:43,585 models 1792 4764 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-17 03:00:08,939 models 5292 8496 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-17 03:08:48,290 models 6732 6776 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-17 03:09:12,478 views 6732 4648 Error generating report with data: Cannot call select_related() after .values() or .values_list()
ERROR 2025-08-17 03:14:22,010 models 11468 5144 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-17 03:23:11,755 models 11468 8976 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-17 08:54:56,208 models 6636 7664 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-17 22:45:27,103 models 6636 10160 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-17 23:39:14,163 models 6636 10160 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-18 00:18:18,895 middleware 6636 7664 Exception in POST /api/v1/transactions/documents/bulk_upload/: cannot import name 'BulkDocumentUploadSerializer' from 'apps.transactions.serializers' (C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\serializers.py)
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\views.py", line 707, in bulk_upload
    from .serializers import BulkDocumentUploadSerializer
ImportError: cannot import name 'BulkDocumentUploadSerializer' from 'apps.transactions.serializers' (C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\serializers.py)
ERROR 2025-08-18 00:18:19,013 log 6636 7664 Internal Server Error: /api/v1/transactions/documents/bulk_upload/
ERROR 2025-08-18 01:10:06,067 views 8824 3488 Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
ERROR 2025-08-18 01:10:21,044 views 8824 8584 Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
ERROR 2025-08-18 01:10:30,124 views 8824 12364 Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
ERROR 2025-08-18 01:11:08,857 views 8824 12968 Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
ERROR 2025-08-18 01:11:20,786 views 8824 11168 Customer creation failed: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\customers\views.py", line 123, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'phone_number': [ErrorDetail(string='This field is required.', code='required')]}
ERROR 2025-08-18 02:24:39,505 views 11184 9788 Error generating customer statement: Customer ID is required for customer statement
ERROR 2025-08-18 02:31:28,415 views 7708 5016 Error generating customer statement: time data '' does not match format '%Y-%m-%d'
ERROR 2025-08-18 03:14:04,456 models 5584 2228 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-18 05:33:39,147 models 5584 8756 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-19 03:05:07,152 models 11524 11332 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-19 03:05:07,268 middleware 11524 8540 Exception in GET /transactions/type/TRANSFER/add/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 241, in transaction_type_add
    return render(request, template_name, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:05:07,316 log 11524 8540 Internal Server Error: /transactions/type/TRANSFER/add/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 241, in transaction_type_add
    return render(request, template_name, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:05:21,269 middleware 11524 3708 Exception in GET /transactions/type/EXCHANGE/add/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 241, in transaction_type_add
    return render(request, template_name, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:05:21,326 log 11524 3708 Internal Server Error: /transactions/type/EXCHANGE/add/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 241, in transaction_type_add
    return render(request, template_name, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:05:24,282 middleware 11524 6068 Exception in GET /transactions/type/EXCHANGE/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:05:24,327 log 11524 6068 Internal Server Error: /transactions/type/EXCHANGE/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:05:27,059 middleware 11524 8540 Exception in GET /transactions/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 34, in transaction_navigation
    return render(request, 'transactions/navigation.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:05:27,100 log 11524 8540 Internal Server Error: /transactions/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 34, in transaction_navigation
    return render(request, 'transactions/navigation.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:05:30,206 middleware 11524 3708 Exception in GET /transactions/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 34, in transaction_navigation
    return render(request, 'transactions/navigation.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:05:30,269 log 11524 3708 Internal Server Error: /transactions/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 34, in transaction_navigation
    return render(request, 'transactions/navigation.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:06:02,672 middleware 11524 8332 Exception in GET /transactions/type/EXCHANGE/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:06:02,908 log 11524 8332 Internal Server Error: /transactions/type/EXCHANGE/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:07:59,960 middleware 11524 8540 Exception in GET /transactions/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 34, in transaction_navigation
    return render(request, 'transactions/navigation.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:07:59,980 log 11524 8540 Internal Server Error: /transactions/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 34, in transaction_navigation
    return render(request, 'transactions/navigation.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:08:07,607 middleware 11524 8332 Exception in GET /transactions/type/EXCHANGE/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:08:07,624 log 11524 8332 Internal Server Error: /transactions/type/EXCHANGE/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:08:11,516 middleware 11524 8540 Exception in GET /transactions/type/EXCHANGE/add/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 241, in transaction_type_add
    return render(request, template_name, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:08:11,529 log 11524 8540 Internal Server Error: /transactions/type/EXCHANGE/add/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 241, in transaction_type_add
    return render(request, template_name, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:08:35,924 middleware 11524 6068 Exception in GET /transactions/type/EXCHANGE/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:08:35,945 log 11524 6068 Internal Server Error: /transactions/type/EXCHANGE/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:08:39,546 middleware 11524 8540 Exception in GET /transactions/type/EXCHANGE/: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:08:39,601 log 11524 8540 Internal Server Error: /transactions/type/EXCHANGE/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 194, in transaction_type_list
    return render(request, 'transactions/type_list.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'javascript-catalog' not found. 'javascript-catalog' is not a valid view function or pattern name.
ERROR 2025-08-19 03:26:21,650 models 10188 5220 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-19 03:34:37,645 models 9692 10000 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-19 03:50:21,824 middleware 9692 9040 Exception in GET /transactions/: Reverse for 'transfer_navigation' not found. 'transfer_navigation' is not a valid view function or pattern name.
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 34, in transaction_navigation
    return render(request, 'transactions/navigation.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'transfer_navigation' not found. 'transfer_navigation' is not a valid view function or pattern name.
ERROR 2025-08-19 03:50:21,886 log 9692 9040 Internal Server Error: /transactions/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 34, in transaction_navigation
    return render(request, 'transactions/navigation.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'transfer_navigation' not found. 'transfer_navigation' is not a valid view function or pattern name.
ERROR 2025-08-19 04:13:45,122 models 4364 7972 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-19 04:43:06,284 middleware 10596 11168 Exception in GET /transactions/transfer/internal/add/: Invalid block tag on line 110: 'static', expected 'endblock'. Did you forget to register or load this tag?
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'static'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 317, in internal_transfer_add
    return render(request, 'transactions/forms/internal_transfer.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 110: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-08-19 04:43:06,398 log 10596 11168 Internal Server Error: /transactions/transfer/internal/add/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'static'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 317, in internal_transfer_add
    return render(request, 'transactions/forms/internal_transfer.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 110: 'static', expected 'endblock'. Did you forget to register or load this tag?
ERROR 2025-08-19 10:35:39,033 models 10596 11168 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-19 12:13:44,802 models 10596 12672 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-19 14:16:08,778 models 8520 13936 Failed to create encrypted audit log: Python int too large to convert to SQLite INTEGER
ERROR 2025-08-19 14:16:08,808 middleware 8520 4948 Exception in GET /transactions/transfer/external/add/: Reverse for 'type_list' with arguments '('',)' not found. 1 pattern(s) tried: ['transactions/type/(?P<transaction_type_code>[^/]+)/\\Z']
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 350, in external_transfer_add
    return render(request, 'transactions/forms/external_transfer.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'type_list' with arguments '('',)' not found. 1 pattern(s) tried: ['transactions/type/(?P<transaction_type_code>[^/]+)/\\Z']
ERROR 2025-08-19 14:16:08,823 log 8520 4948 Internal Server Error: /transactions/transfer/external/add/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\web_views.py", line 350, in external_transfer_add
    return render(request, 'transactions/forms/external_transfer.html', context)
  File "C:\Program Files\Python313\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'type_list' with arguments '('',)' not found. 1 pattern(s) tried: ['transactions/type/(?P<transaction_type_code>[^/]+)/\\Z']
ERROR 2025-08-20 01:24:20,329 middleware 2748 8516 Exception in GET /api/v1/currencies/rates/current/: DISTINCT ON fields is not supported by this database backend
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\currencies\views.py", line 387, in current
    for rate in rates:
                ^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 785, in as_sql
    distinct_result, distinct_params = self.connection.ops.distinct_sql(
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        distinct_fields,
        ^^^^^^^^^^^^^^^^
        distinct_params,
        ^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\base\operations.py", line 202, in distinct_sql
    raise NotSupportedError(
        "DISTINCT ON fields is not supported by this database backend"
    )
django.db.utils.NotSupportedError: DISTINCT ON fields is not supported by this database backend
ERROR 2025-08-20 01:24:20,395 log 2748 8516 Internal Server Error: /api/v1/currencies/rates/current/
ERROR 2025-08-20 01:24:49,900 middleware 2748 5956 Exception in GET /api/v1/currencies/rates/current/: DISTINCT ON fields is not supported by this database backend
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\currencies\views.py", line 387, in current
    for rate in rates:
                ^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1549, in execute_sql
    sql, params = self.as_sql()
                  ~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 785, in as_sql
    distinct_result, distinct_params = self.connection.ops.distinct_sql(
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        distinct_fields,
        ^^^^^^^^^^^^^^^^
        distinct_params,
        ^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\base\operations.py", line 202, in distinct_sql
    raise NotSupportedError(
        "DISTINCT ON fields is not supported by this database backend"
    )
django.db.utils.NotSupportedError: DISTINCT ON fields is not supported by this database backend
ERROR 2025-08-20 01:24:49,907 log 2748 5956 Internal Server Error: /api/v1/currencies/rates/current/
ERROR 2025-08-20 01:30:22,552 views 2748 10936 Transaction creation failed: {'commission_amount': [ErrorDetail(string='A valid number is required.', code='invalid')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\views.py", line 177, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'commission_amount': [ErrorDetail(string='A valid number is required.', code='invalid')]}
ERROR 2025-08-20 01:54:33,375 fields 9112 11064 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 01:54:33,375 models 9112 11064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 01:54:33,398 fields 9112 11064 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 01:54:33,398 models 9112 11064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 01:54:33,423 fields 9112 11064 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 01:54:33,424 models 9112 11064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 01:54:33,438 fields 9112 11064 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 01:54:33,438 models 9112 11064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 01:54:33,462 fields 9112 11064 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 01:54:33,463 models 9112 11064 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 01:54:44,528 views 11076 13500 Transaction creation failed: {'commission_amount': [ErrorDetail(string='A valid number is required.', code='invalid')]}
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\transactions\views.py", line 177, in create
    return super().create(request, *args, **kwargs)
           ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\rest_framework\serializers.py", line 233, in is_valid
    raise ValidationError(self.errors)
rest_framework.exceptions.ValidationError: {'commission_amount': [ErrorDetail(string='A valid number is required.', code='invalid')]}
ERROR 2025-08-20 02:08:01,722 fields 2436 6560 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 02:08:01,723 models 2436 6560 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 02:08:01,759 fields 2436 6560 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 02:08:01,760 models 2436 6560 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 02:08:01,793 fields 2436 6560 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 02:08:01,793 models 2436 6560 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 02:08:01,818 fields 2436 6560 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 02:08:01,819 models 2436 6560 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 02:08:01,847 fields 2436 6560 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 02:08:01,848 models 2436 6560 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 02:08:01,872 fields 2436 6560 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 02:08:01,875 models 2436 6560 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 02:08:02,505 fields 2436 6560 Failed to encrypt field value: ARENA_ENCRYPTION_KEY environment variable must be set in production
ERROR 2025-08-20 02:08:02,506 models 2436 6560 Failed to create encrypted audit log: ['Failed to encrypt sensitive data: ARENA_ENCRYPTION_KEY environment variable must be set in production']
ERROR 2025-08-20 02:58:18,688 apps 11720 2912 Error setting up default commission rules: no such table: transactions_commissionrule
ERROR 2025-08-20 02:58:18,709 apps 11720 2912 Failed to setup cache: no such table: arena_cache_table
ERROR 2025-08-20 02:59:00,673 apps 10508 5792 Error setting up default commission rules: no such table: transactions_commissionrule
ERROR 2025-08-20 02:59:00,681 apps 10508 5792 Failed to setup cache: no such table: arena_cache_table
ERROR 2025-08-20 02:59:12,478 apps 13920 3872 Error setting up default commission rules: no such table: transactions_commissionrule
ERROR 2025-08-20 02:59:12,492 apps 13920 3872 Failed to setup cache: no such table: arena_cache_table
ERROR 2025-08-20 03:27:59,520 apps 1956 13956 Error setting up default commission rules: no such table: transactions_commissionrule
ERROR 2025-08-20 03:27:59,576 apps 1956 13956 Failed to setup cache: no such table: arena_cache_table
ERROR 2025-08-20 03:28:11,085 log 1956 6520 Internal Server Error: /transactions/list/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: arena_cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 207, in process_request
    if self._is_rate_limited(request, ip_address):
       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 234, in _is_rate_limited
    current_requests = cache.get(cache_key, 0)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 68, in get_many
    cursor.execute(
    ~~~~~~~~~~~~~~^
        "SELECT %s, %s, %s FROM %s WHERE %s IN (%s)"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
        list(key_map),
        ^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: arena_cache_table
ERROR 2025-08-20 03:28:12,111 log 1956 4036 Internal Server Error: /favicon.ico
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: arena_cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 207, in process_request
    if self._is_rate_limited(request, ip_address):
       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 234, in _is_rate_limited
    current_requests = cache.get(cache_key, 0)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 68, in get_many
    cursor.execute(
    ~~~~~~~~~~~~~~^
        "SELECT %s, %s, %s FROM %s WHERE %s IN (%s)"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
        list(key_map),
        ^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: arena_cache_table
ERROR 2025-08-20 03:41:57,275 apps 996 332 Error setting up default commission rules: no such table: transactions_commissionrule
ERROR 2025-08-20 03:41:57,281 apps 996 332 Failed to setup cache: no such table: arena_cache_table
ERROR 2025-08-20 03:42:04,481 log 996 13220 Internal Server Error: /transactions/transfer/international/add/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: arena_cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 207, in process_request
    if self._is_rate_limited(request, ip_address):
       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 234, in _is_rate_limited
    current_requests = cache.get(cache_key, 0)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 68, in get_many
    cursor.execute(
    ~~~~~~~~~~~~~~^
        "SELECT %s, %s, %s FROM %s WHERE %s IN (%s)"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
        list(key_map),
        ^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: arena_cache_table
ERROR 2025-08-20 03:42:05,428 log 996 5416 Internal Server Error: /favicon.ico
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: arena_cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 207, in process_request
    if self._is_rate_limited(request, ip_address):
       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 234, in _is_rate_limited
    current_requests = cache.get(cache_key, 0)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 68, in get_many
    cursor.execute(
    ~~~~~~~~~~~~~~^
        "SELECT %s, %s, %s FROM %s WHERE %s IN (%s)"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
        list(key_map),
        ^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: arena_cache_table
ERROR 2025-08-20 03:42:10,841 log 996 11248 Internal Server Error: /transactions/transfer/international/add/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: arena_cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 207, in process_request
    if self._is_rate_limited(request, ip_address):
       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 234, in _is_rate_limited
    current_requests = cache.get(cache_key, 0)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 68, in get_many
    cursor.execute(
    ~~~~~~~~~~~~~~^
        "SELECT %s, %s, %s FROM %s WHERE %s IN (%s)"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
        list(key_map),
        ^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: arena_cache_table
ERROR 2025-08-20 03:42:11,751 log 996 13064 Internal Server Error: /favicon.ico
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: arena_cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 207, in process_request
    if self._is_rate_limited(request, ip_address):
       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 234, in _is_rate_limited
    current_requests = cache.get(cache_key, 0)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 68, in get_many
    cursor.execute(
    ~~~~~~~~~~~~~~^
        "SELECT %s, %s, %s FROM %s WHERE %s IN (%s)"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
        list(key_map),
        ^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: arena_cache_table
ERROR 2025-08-20 03:42:17,482 log 996 11812 Internal Server Error: /login
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: arena_cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 207, in process_request
    if self._is_rate_limited(request, ip_address):
       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 234, in _is_rate_limited
    current_requests = cache.get(cache_key, 0)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 68, in get_many
    cursor.execute(
    ~~~~~~~~~~~~~~^
        "SELECT %s, %s, %s FROM %s WHERE %s IN (%s)"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
        list(key_map),
        ^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: arena_cache_table
ERROR 2025-08-20 03:42:18,343 log 996 13000 Internal Server Error: /favicon.ico
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: arena_cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 207, in process_request
    if self._is_rate_limited(request, ip_address):
       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 234, in _is_rate_limited
    current_requests = cache.get(cache_key, 0)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 68, in get_many
    cursor.execute(
    ~~~~~~~~~~~~~~^
        "SELECT %s, %s, %s FROM %s WHERE %s IN (%s)"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
        list(key_map),
        ^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: arena_cache_table
ERROR 2025-08-20 03:42:35,615 log 996 13220 Internal Server Error: /reports/
Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: arena_cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Program Files\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 207, in process_request
    if self._is_rate_limited(request, ip_address):
       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\exchange-accounting\src\apps\core\middleware.py", line 234, in _is_rate_limited
    current_requests = cache.get(cache_key, 0)
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\core\cache\backends\db.py", line 68, in get_many
    cursor.execute(
    ~~~~~~~~~~~~~~^
        "SELECT %s, %s, %s FROM %s WHERE %s IN (%s)"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
        list(key_map),
        ^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: arena_cache_table
