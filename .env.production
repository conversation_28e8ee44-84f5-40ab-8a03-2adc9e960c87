# Arena Doviz Production Environment Configuration
# Generated for complete production deployment

# Django Settings
DJANGO_SETTINGS_MODULE=config.settings.prod
SECRET_KEY=arena_doviz_production_secret_key_2025_very_secure_random_string_for_production_deployment_only

# Database Configuration (PostgreSQL)
DB_NAME=arena_doviz_prod
DB_USER=arena_user
DB_PASSWORD=ArenaD0viz2025!SecureProd
DB_HOST=localhost
DB_PORT=5432

# Encryption Key (REQUIRED for production)
ARENA_ENCRYPTION_KEY=sHq7QhFLpRTwjAHzycsZyqHxhM6dglpVV_CmCTYoNMk=

# Security Settings
ARENA_USE_HTTPS=true
SECURE_SSL_REDIRECT=true
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=arena_email_password_2025
EMAIL_USE_TLS=true
DEFAULT_FROM_EMAIL=Arena Doviz System <<EMAIL>>

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
CACHE_BACKEND=redis

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/var/log/arena-doviz/arena.log
ERROR_LOG_FILE=/var/log/arena-doviz/error.log

# File Storage
MEDIA_ROOT=/var/www/arena-doviz/media
STATIC_ROOT=/var/www/arena-doviz/static

# Backup Configuration
BACKUP_DIR=/var/backups/arena-doviz
BACKUP_RETENTION_DAYS=30

# Monitoring Configuration
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=300
ALERT_EMAIL=<EMAIL>

# Performance Settings
DATABASE_CONN_MAX_AGE=600
CACHE_TIMEOUT=3600

# Security Headers
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=true
SECURE_HSTS_PRELOAD=true
SECURE_CONTENT_TYPE_NOSNIFF=true
SECURE_BROWSER_XSS_FILTER=true
X_FRAME_OPTIONS=SAMEORIGIN

# API Rate Limiting
API_RATE_LIMIT=1000/hour
API_BURST_LIMIT=100/minute

# File Upload Limits
MAX_UPLOAD_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx,xls,xlsx

# Session Configuration
SESSION_COOKIE_AGE=86400
SESSION_EXPIRE_AT_BROWSER_CLOSE=true
SESSION_SAVE_EVERY_REQUEST=false

# CORS Configuration (if needed)
CORS_ALLOWED_ORIGINS=https://arena-doviz.com,https://www.arena-doviz.com
CORS_ALLOW_CREDENTIALS=true

# Celery Configuration (for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=UTC

# WhatsApp Integration
WHATSAPP_API_URL=https://api.whatsapp.com/send
WHATSAPP_BUSINESS_NUMBER=+************

# Audit and Compliance
AUDIT_LOG_RETENTION_DAYS=2555
COMPLIANCE_MODE=strict
DATA_RETENTION_POLICY=7_years

# Multi-location Configuration
DEFAULT_LOCATION=DXB
SUPPORTED_LOCATIONS=DXB,IST,TAB,TEH,CHN

# Currency Configuration
DEFAULT_CURRENCY=AED
SUPPORTED_CURRENCIES=USD,AED,IRR,EUR,GBP,TRY,CNY

# Commission Configuration
DEFAULT_COMMISSION_RATE=0.25
MAX_COMMISSION_RATE=5.0
MIN_COMMISSION_AMOUNT=1.0

# Transaction Limits
MAX_TRANSACTION_AMOUNT=1000000
DAILY_TRANSACTION_LIMIT=5000000
MONTHLY_TRANSACTION_LIMIT=50000000

# Reporting Configuration
REPORT_GENERATION_TIMEOUT=300
MAX_REPORT_RECORDS=100000
REPORT_CACHE_TIMEOUT=1800

# Notification Configuration
SMS_PROVIDER=twilio
SMS_API_KEY=your_sms_api_key
SMS_FROM_NUMBER=+************

# Backup and Recovery
AUTO_BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_ENCRYPTION_ENABLED=true
DISASTER_RECOVERY_ENABLED=true

# Load Balancing
LOAD_BALANCER_ENABLED=false
STICKY_SESSIONS=true

# CDN Configuration
CDN_ENABLED=false
CDN_URL=https://cdn.arena-doviz.com

# Analytics
ANALYTICS_ENABLED=true
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Feature Flags
FEATURE_ADVANCED_REPORTING=true
FEATURE_MULTI_CURRENCY_ACCOUNTS=true
FEATURE_AUTOMATED_COMPLIANCE=true
FEATURE_REAL_TIME_RATES=true
FEATURE_MOBILE_APP_API=true

# Integration Settings
BANK_API_ENABLED=true
SWIFT_INTEGRATION_ENABLED=true
REGULATORY_REPORTING_ENABLED=true

# Development/Testing (set to false in production)
DEBUG_TOOLBAR_ENABLED=false
PROFILING_ENABLED=false
TEST_MODE=false
