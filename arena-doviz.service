[Unit]
Description=Arena Doviz Exchange Accounting System
Documentation=https://github.com/arena-doviz/exchange-accounting
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service
Requires=network.target

[Service]
Type=exec
User=arena-doviz
Group=arena-doviz
WorkingDirectory=/opt/arena-doviz/src
Environment=DJANGO_SETTINGS_MODULE=config.settings.prod
EnvironmentFile=/opt/arena-doviz/.env.production

# Main application server
ExecStart=/opt/arena-doviz/venv/bin/python manage.py runserver 127.0.0.1:8000
ExecReload=/bin/kill -HUP $MAINPID

# Process management
Restart=always
RestartSec=3
StartLimitInterval=60
StartLimitBurst=3

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/arena-doviz /var/log/arena-doviz /var/lib/arena-doviz

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=arena-doviz

# Health check
ExecStartPost=/bin/sleep 10
ExecStartPost=/bin/bash -c 'curl -f http://127.0.0.1:8000/health/ || exit 1'

[Install]
WantedBy=multi-user.target
