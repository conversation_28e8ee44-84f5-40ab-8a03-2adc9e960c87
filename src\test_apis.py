#!/usr/bin/env python
"""
Test script for Arena Doviz API endpoints
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import AccessToken
import json

def test_apis():
    """Test key API endpoints"""
    
    # Create test client
    client = Client()
    
    # Get a user and create token
    User = get_user_model()
    user = User.objects.filter(is_superuser=True).first()
    if not user:
        print('No superuser found')
        return
        
    token = AccessToken.for_user(user)
    
    print("=== Arena Doviz API Testing ===")
    print(f"Testing with user: {user.username}")
    print(f"Token: {str(token)[:10]}...")
    
    # Test exchange rate API
    print('\n=== Testing Exchange Rate API ===')
    response = client.get('/api/v1/currencies/rates/current/?location=DXB', 
                         HTTP_AUTHORIZATION=f'Bearer {str(token)}')
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = json.loads(response.content)
        print(f'Exchange rates found: {len(data)}')
        if data:
            print(f'Sample rate: {data[0]["from_currency"]}/{data[0]["to_currency"]} = {data[0]["sell_rate"]}')
    else:
        print(f'Error: {response.content.decode()[:200]}')
        
    # Test customer API
    print('\n=== Testing Customer API ===')
    response = client.get('/api/v1/customers/customers/?search=test',
                         HTTP_AUTHORIZATION=f'Bearer {str(token)}')
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = json.loads(response.content)
        print(f'Customers found: {data.get("count", 0)}')
        if data.get("results"):
            customer = data["results"][0]
            print(f'Sample customer: {customer.get("display_name", "N/A")}')
    else:
        print(f'Error: {response.content.decode()[:200]}')
        
    # Test customer balance API
    print('\n=== Testing Customer Balance API ===')
    customers_response = client.get('/api/v1/customers/customers/',
                                   HTTP_AUTHORIZATION=f'Bearer {str(token)}')
    if customers_response.status_code == 200:
        customers_data = json.loads(customers_response.content)
        if customers_data.get("results"):
            customer_id = customers_data["results"][0]["id"]
            balance_response = client.get(f'/api/v1/customers/customers/{customer_id}/balance/',
                                        HTTP_AUTHORIZATION=f'Bearer {str(token)}')
            print(f'Status: {balance_response.status_code}')
            if balance_response.status_code == 200:
                balance_data = json.loads(balance_response.content)
                print(f'Balance records: {len(balance_data)}')
                if balance_data:
                    print(f'Sample balance: {balance_data[0]["currency_code"]} = {balance_data[0]["formatted_balance"]}')
            else:
                print(f'Error: {balance_response.content.decode()[:200]}')
    
    # Test transaction types API
    print('\n=== Testing Transaction Types API ===')
    response = client.get('/api/v1/transactions/types/',
                         HTTP_AUTHORIZATION=f'Bearer {str(token)}')
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        data = json.loads(response.content)
        print(f'Transaction types found: {data.get("count", 0)}')
        if data.get("results"):
            print(f'Available types: {[t["code"] for t in data["results"]]}')
    else:
        print(f'Error: {response.content.decode()[:200]}')
        
    # Test transaction creation with commission validation
    print('\n=== Testing Transaction Creation ===')
    if customers_response.status_code == 200:
        customers_data = json.loads(customers_response.content)
        if customers_data.get("results"):
            customer_id = customers_data["results"][0]["id"]

            # Test cash deposit with commission
            deposit_data = {
                "transaction_type": "DEPOSIT",
                "customer": customer_id,
                "from_currency": "USD",
                "to_currency": "USD",
                "from_amount": 100.00,
                "to_amount": 100.00,
                "commission_amount": 2.50,
                "exchange_rate": 1.0,
                "delivery_method": "cash",
                "status": "draft"
            }

            # Get transaction type ID
            types_response = client.get('/api/v1/transactions/types/',
                                       HTTP_AUTHORIZATION=f'Bearer {str(token)}')
            if types_response.status_code == 200:
                types_data = json.loads(types_response.content)
                deposit_type = next((t for t in types_data["results"] if t["code"] == "DEPOSIT"), None)
                if deposit_type:
                    deposit_data["transaction_type"] = deposit_type["id"]

                    # Test transaction creation
                    create_response = client.post('/api/v1/transactions/transactions/',
                                                 data=json.dumps(deposit_data),
                                                 content_type='application/json',
                                                 HTTP_AUTHORIZATION=f'Bearer {str(token)}')
                    print(f'Transaction creation status: {create_response.status_code}')
                    if create_response.status_code == 201:
                        print('✅ Cash deposit transaction created successfully')
                    else:
                        print(f'❌ Transaction creation failed: {create_response.content.decode()[:200]}')

    print('\n=== Testing Complete ===')

if __name__ == '__main__':
    test_apis()
