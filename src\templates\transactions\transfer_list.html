{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ page_title }} - Arena Doviz{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                {% if transfer_type == 'internal' %}
                    <i class="bi bi-arrow-left-right"></i>
                {% elif transfer_type == 'external' %}
                    <i class="bi bi-bank"></i>
                {% elif transfer_type == 'international' %}
                    <i class="bi bi-globe"></i>
                {% endif %}
                {{ page_title }}
            </h1>
            <p class="text-muted mb-0">
                {% if transfer_type == 'internal' %}
                    {% trans "Transfers between customers within the system" %}
                {% elif transfer_type == 'external' %}
                    {% trans "Transfers to external banks and institutions" %}
                {% elif transfer_type == 'international' %}
                    {% trans "Cross-border international transfers" %}
                {% endif %}
            </p>
        </div>
        <div>
            {% if transfer_type == 'internal' %}
                <a href="{% url 'transactions_web:internal_transfer_add' %}" class="btn btn-primary">
                    <i class="bi bi-plus"></i> {% trans "New Internal Transfer" %}
                </a>
            {% elif transfer_type == 'external' %}
                <a href="{% url 'transactions_web:external_transfer_add' %}" class="btn btn-success">
                    <i class="bi bi-plus"></i> {% trans "New External Transfer" %}
                </a>
            {% elif transfer_type == 'international' %}
                <a href="{% url 'transactions_web:international_transfer_add' %}" class="btn btn-warning">
                    <i class="bi bi-plus"></i> {% trans "New International Transfer" %}
                </a>
            {% endif %}
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">{% trans "From Date" %}</label>
                    <input type="date" class="form-control" id="date_from">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">{% trans "To Date" %}</label>
                    <input type="date" class="form-control" id="date_to">
                </div>
                <div class="col-md-3">
                    <label for="status_filter" class="form-label">{% trans "Status" %}</label>
                    <select class="form-select" id="status_filter">
                        <option value="">{% trans "All Statuses" %}</option>
                        <option value="draft">{% trans "Draft" %}</option>
                        <option value="pending">{% trans "Pending" %}</option>
                        <option value="approved">{% trans "Approved" %}</option>
                        <option value="completed">{% trans "Completed" %}</option>
                        <option value="rejected">{% trans "Rejected" %}</option>
                        <option value="cancelled">{% trans "Cancelled" %}</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-primary me-2" onclick="applyFilters()">
                        <i class="bi bi-funnel"></i> {% trans "Filter" %}
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="bi bi-x-circle"></i> {% trans "Clear" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfers Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="bi bi-list"></i>
                {{ page_title }}
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="transfers-table">
                    <thead>
                        <tr>
                            <th>{% trans "Transaction #" %}</th>
                            <th>{% trans "Date" %}</th>
                            <th>{% trans "Customer" %}</th>
                            {% if transfer_type == 'internal' %}
                                <th>{% trans "Recipient" %}</th>
                            {% elif transfer_type == 'external' %}
                                <th>{% trans "Bank" %}</th>
                            {% elif transfer_type == 'international' %}
                                <th>{% trans "Country" %}</th>
                            {% endif %}
                            <th>{% trans "Amount" %}</th>
                            <th>{% trans "Currency" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="8" class="text-center text-muted py-4">
                                <i class="bi bi-inbox"></i>
                                {% trans "No transfers found" %}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadTransfers();
});

function loadTransfers() {
    // This would load transfers from the API based on transfer_type
    // For now, showing empty state
    console.log('Loading {{ transfer_type }} transfers...');
}

function applyFilters() {
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;
    const status = document.getElementById('status_filter').value;
    
    console.log('Applying filters:', { dateFrom, dateTo, status });
    loadTransfers();
}

function clearFilters() {
    document.getElementById('date_from').value = '';
    document.getElementById('date_to').value = '';
    document.getElementById('status_filter').value = '';
    loadTransfers();
}
</script>
{% endblock %}
