#!/usr/bin/env python3
"""
Complete Arena Doviz Production Deployment Script
"""
import os
import sys
import subprocess
import shutil
import getpass
from pathlib import Path

class ProductionDeployer:
    """Complete production deployment automation"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.src_dir = self.project_root / 'src'
        self.deployment_user = 'arena-doviz'
        self.deployment_path = '/opt/arena-doviz'
        self.domain = 'arena-doviz.com'
        
    def run_complete_deployment(self):
        """Run complete production deployment"""
        print("🚀 Arena Doviz Complete Production Deployment")
        print("=" * 60)
        
        steps = [
            ("Checking prerequisites", self.check_prerequisites),
            ("Creating deployment user", self.create_deployment_user),
            ("Setting up directory structure", self.setup_directories),
            ("Installing system dependencies", self.install_system_dependencies),
            ("Setting up Python environment", self.setup_python_environment),
            ("Configuring PostgreSQL", self.setup_postgresql),
            ("Configuring Redis", self.setup_redis),
            ("Deploying application code", self.deploy_application),
            ("Running database migrations", self.run_migrations),
            ("Collecting static files", self.collect_static_files),
            ("Setting up Nginx", self.setup_nginx),
            ("Setting up SSL certificates", self.setup_ssl),
            ("Configuring systemd services", self.setup_systemd_services),
            ("Setting up monitoring", self.setup_monitoring),
            ("Configuring backups", self.setup_backups),
            ("Running security hardening", self.security_hardening),
            ("Final verification", self.final_verification)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            try:
                if not step_func():
                    print(f"❌ {step_name} failed. Deployment stopped.")
                    return False
                print(f"✅ {step_name} completed successfully")
            except Exception as e:
                print(f"❌ {step_name} failed with error: {str(e)}")
                return False
        
        print("\n" + "=" * 60)
        print("🎉 PRODUCTION DEPLOYMENT COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        self.print_deployment_summary()
        
        return True
    
    def check_prerequisites(self):
        """Check deployment prerequisites"""
        # Check if running as root
        if os.geteuid() != 0:
            print("❌ This script must be run as root")
            return False
        
        # Check operating system
        if not os.path.exists('/etc/debian_version') and not os.path.exists('/etc/redhat-release'):
            print("❌ Unsupported operating system. This script supports Debian/Ubuntu and RHEL/CentOS.")
            return False
        
        # Check internet connectivity
        try:
            subprocess.run(['ping', '-c', '1', 'google.com'], 
                         check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except subprocess.CalledProcessError:
            print("❌ No internet connectivity")
            return False
        
        print("✅ Prerequisites check passed")
        return True
    
    def create_deployment_user(self):
        """Create deployment user"""
        try:
            # Check if user exists
            subprocess.run(['id', self.deployment_user], 
                         check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ User {self.deployment_user} already exists")
        except subprocess.CalledProcessError:
            # Create user
            subprocess.run([
                'useradd', '--system', '--shell', '/bin/bash',
                '--home-dir', self.deployment_path,
                '--create-home', self.deployment_user
            ], check=True)
            print(f"✅ Created user {self.deployment_user}")
        
        return True
    
    def setup_directories(self):
        """Setup directory structure"""
        directories = [
            f"{self.deployment_path}/src",
            f"{self.deployment_path}/venv",
            f"{self.deployment_path}/logs",
            f"{self.deployment_path}/backups",
            f"{self.deployment_path}/scripts",
            "/var/www/arena-doviz/static",
            "/var/www/arena-doviz/media",
            "/var/log/arena-doviz",
            "/var/backups/arena-doviz"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            shutil.chown(directory, self.deployment_user, self.deployment_user)
        
        return True
    
    def install_system_dependencies(self):
        """Install system dependencies"""
        if os.path.exists('/etc/debian_version'):
            # Debian/Ubuntu
            packages = [
                'python3', 'python3-pip', 'python3-venv', 'python3-dev',
                'postgresql', 'postgresql-contrib', 'postgresql-server-dev-all',
                'redis-server', 'nginx', 'certbot', 'python3-certbot-nginx',
                'git', 'curl', 'wget', 'unzip', 'supervisor',
                'build-essential', 'libpq-dev', 'libssl-dev', 'libffi-dev',
                'fail2ban', 'ufw', 'htop', 'iotop', 'netstat-nat'
            ]
            
            subprocess.run(['apt-get', 'update'], check=True)
            subprocess.run(['apt-get', 'install', '-y'] + packages, check=True)
            
        elif os.path.exists('/etc/redhat-release'):
            # RHEL/CentOS
            packages = [
                'python3', 'python3-pip', 'python3-devel',
                'postgresql-server', 'postgresql-contrib', 'postgresql-devel',
                'redis', 'nginx', 'certbot', 'python3-certbot-nginx',
                'git', 'curl', 'wget', 'unzip', 'supervisor',
                'gcc', 'gcc-c++', 'make', 'openssl-devel', 'libffi-devel',
                'fail2ban', 'firewalld', 'htop', 'iotop', 'net-tools'
            ]
            
            subprocess.run(['yum', 'install', '-y', 'epel-release'], check=True)
            subprocess.run(['yum', 'install', '-y'] + packages, check=True)
        
        return True
    
    def setup_python_environment(self):
        """Setup Python virtual environment"""
        venv_path = f"{self.deployment_path}/venv"
        
        # Create virtual environment
        subprocess.run([
            'python3', '-m', 'venv', venv_path
        ], check=True)
        
        # Upgrade pip
        subprocess.run([
            f"{venv_path}/bin/pip", 'install', '--upgrade', 'pip', 'setuptools', 'wheel'
        ], check=True)
        
        return True
    
    def setup_postgresql(self):
        """Setup PostgreSQL"""
        # Initialize PostgreSQL (for RHEL/CentOS)
        if os.path.exists('/etc/redhat-release'):
            try:
                subprocess.run(['postgresql-setup', 'initdb'], check=True)
            except subprocess.CalledProcessError:
                pass  # Already initialized
        
        # Start and enable PostgreSQL
        subprocess.run(['systemctl', 'enable', 'postgresql'], check=True)
        subprocess.run(['systemctl', 'start', 'postgresql'], check=True)
        
        # Run PostgreSQL setup script
        setup_script = self.src_dir / 'scripts' / 'setup_postgresql.py'
        if setup_script.exists():
            subprocess.run([
                'python3', str(setup_script)
            ], check=True)
        
        return True
    
    def setup_redis(self):
        """Setup Redis"""
        # Start and enable Redis
        subprocess.run(['systemctl', 'enable', 'redis'], check=True)
        subprocess.run(['systemctl', 'start', 'redis'], check=True)
        
        return True
    
    def deploy_application(self):
        """Deploy application code"""
        # Copy source code
        src_dest = f"{self.deployment_path}/src"
        
        # Remove existing code
        if os.path.exists(src_dest):
            shutil.rmtree(src_dest)
        
        # Copy new code
        shutil.copytree(str(self.src_dir), src_dest)
        
        # Set ownership
        shutil.chown(src_dest, self.deployment_user, self.deployment_user)
        for root, dirs, files in os.walk(src_dest):
            for d in dirs:
                shutil.chown(os.path.join(root, d), self.deployment_user, self.deployment_user)
            for f in files:
                shutil.chown(os.path.join(root, f), self.deployment_user, self.deployment_user)
        
        # Install Python dependencies
        requirements_file = f"{src_dest}/requirements.txt"
        if os.path.exists(requirements_file):
            subprocess.run([
                f"{self.deployment_path}/venv/bin/pip", 'install', '-r', requirements_file
            ], check=True)
        
        # Copy environment file
        env_source = self.project_root / '.env.production'
        env_dest = f"{self.deployment_path}/.env.production"
        if env_source.exists():
            shutil.copy2(str(env_source), env_dest)
            shutil.chown(env_dest, self.deployment_user, self.deployment_user)
            os.chmod(env_dest, 0o600)
        
        return True
    
    def run_migrations(self):
        """Run database migrations"""
        manage_py = f"{self.deployment_path}/src/manage.py"
        venv_python = f"{self.deployment_path}/venv/bin/python"
        
        # Run migrations
        subprocess.run([
            'sudo', '-u', self.deployment_user,
            venv_python, manage_py, 'migrate',
            '--settings=config.settings.prod'
        ], check=True, cwd=f"{self.deployment_path}/src")
        
        return True
    
    def collect_static_files(self):
        """Collect static files"""
        manage_py = f"{self.deployment_path}/src/manage.py"
        venv_python = f"{self.deployment_path}/venv/bin/python"
        
        # Collect static files
        subprocess.run([
            'sudo', '-u', self.deployment_user,
            venv_python, manage_py, 'collectstatic',
            '--noinput', '--settings=config.settings.prod'
        ], check=True, cwd=f"{self.deployment_path}/src")
        
        return True
    
    def setup_nginx(self):
        """Setup Nginx"""
        # Copy Nginx configuration
        nginx_source = self.project_root / 'nginx.conf.production'
        nginx_dest = '/etc/nginx/sites-available/arena-doviz'
        
        if nginx_source.exists():
            shutil.copy2(str(nginx_source), nginx_dest)
            
            # Enable site
            nginx_enabled = '/etc/nginx/sites-enabled/arena-doviz'
            if os.path.exists(nginx_enabled):
                os.remove(nginx_enabled)
            os.symlink(nginx_dest, nginx_enabled)
            
            # Remove default site
            default_enabled = '/etc/nginx/sites-enabled/default'
            if os.path.exists(default_enabled):
                os.remove(default_enabled)
            
            # Test configuration
            subprocess.run(['nginx', '-t'], check=True)
            
            # Enable and start Nginx
            subprocess.run(['systemctl', 'enable', 'nginx'], check=True)
            subprocess.run(['systemctl', 'restart', 'nginx'], check=True)
        
        return True
    
    def setup_ssl(self):
        """Setup SSL certificates"""
        ssl_script = self.src_dir / 'scripts' / 'setup_ssl.sh'
        if ssl_script.exists():
            os.chmod(str(ssl_script), 0o755)
            
            # Get email for SSL certificate
            email = input("Enter email for SSL certificate: ").strip()
            if not email:
                email = f"admin@{self.domain}"
            
            subprocess.run([
                'bash', str(ssl_script), self.domain, email
            ], check=True)
        
        return True
    
    def setup_systemd_services(self):
        """Setup systemd services"""
        services = [
            'arena-doviz.service',
            'arena-doviz-backup.service',
            'arena-doviz-backup.timer'
        ]
        
        for service in services:
            service_source = self.project_root / service
            service_dest = f"/etc/systemd/system/{service}"
            
            if service_source.exists():
                shutil.copy2(str(service_source), service_dest)
                
                # Update paths in service files
                with open(service_dest, 'r') as f:
                    content = f.read()
                
                content = content.replace('/opt/arena-doviz', self.deployment_path)
                
                with open(service_dest, 'w') as f:
                    f.write(content)
        
        # Reload systemd and enable services
        subprocess.run(['systemctl', 'daemon-reload'], check=True)
        subprocess.run(['systemctl', 'enable', 'arena-doviz.service'], check=True)
        subprocess.run(['systemctl', 'enable', 'arena-doviz-backup.timer'], check=True)
        subprocess.run(['systemctl', 'start', 'arena-doviz.service'], check=True)
        subprocess.run(['systemctl', 'start', 'arena-doviz-backup.timer'], check=True)
        
        return True
    
    def setup_monitoring(self):
        """Setup monitoring"""
        # Copy monitoring scripts
        scripts = ['backup_database.sh']
        
        for script in scripts:
            script_source = self.src_dir / 'scripts' / script
            script_dest = f"{self.deployment_path}/scripts/{script}"
            
            if script_source.exists():
                shutil.copy2(str(script_source), script_dest)
                os.chmod(script_dest, 0o755)
                shutil.chown(script_dest, self.deployment_user, self.deployment_user)
        
        return True
    
    def setup_backups(self):
        """Setup backup system"""
        # Make backup script executable
        backup_script = f"{self.deployment_path}/scripts/backup_database.sh"
        if os.path.exists(backup_script):
            os.chmod(backup_script, 0o755)
        
        return True
    
    def security_hardening(self):
        """Apply security hardening"""
        # Configure firewall
        if shutil.which('ufw'):
            subprocess.run(['ufw', '--force', 'enable'], check=True)
            subprocess.run(['ufw', 'allow', 'ssh'], check=True)
            subprocess.run(['ufw', 'allow', 'http'], check=True)
            subprocess.run(['ufw', 'allow', 'https'], check=True)
        elif shutil.which('firewall-cmd'):
            subprocess.run(['systemctl', 'enable', 'firewalld'], check=True)
            subprocess.run(['systemctl', 'start', 'firewalld'], check=True)
            subprocess.run(['firewall-cmd', '--permanent', '--add-service=ssh'], check=True)
            subprocess.run(['firewall-cmd', '--permanent', '--add-service=http'], check=True)
            subprocess.run(['firewall-cmd', '--permanent', '--add-service=https'], check=True)
            subprocess.run(['firewall-cmd', '--reload'], check=True)
        
        # Configure fail2ban
        if shutil.which('fail2ban-server'):
            subprocess.run(['systemctl', 'enable', 'fail2ban'], check=True)
            subprocess.run(['systemctl', 'start', 'fail2ban'], check=True)
        
        return True
    
    def final_verification(self):
        """Final deployment verification"""
        # Check if application is running
        try:
            subprocess.run(['systemctl', 'is-active', 'arena-doviz.service'], 
                         check=True, stdout=subprocess.DEVNULL)
            print("✅ Arena Doviz service is running")
        except subprocess.CalledProcessError:
            print("❌ Arena Doviz service is not running")
            return False
        
        # Check if Nginx is running
        try:
            subprocess.run(['systemctl', 'is-active', 'nginx'], 
                         check=True, stdout=subprocess.DEVNULL)
            print("✅ Nginx service is running")
        except subprocess.CalledProcessError:
            print("❌ Nginx service is not running")
            return False
        
        # Test HTTP response
        try:
            import requests
            response = requests.get(f"http://localhost", timeout=10)
            if response.status_code in [200, 301, 302]:
                print("✅ HTTP response test passed")
            else:
                print(f"⚠️ HTTP response test returned status {response.status_code}")
        except Exception as e:
            print(f"⚠️ HTTP response test failed: {str(e)}")
        
        return True
    
    def print_deployment_summary(self):
        """Print deployment summary"""
        print(f"""
🎉 Arena Doviz Production Deployment Summary

Application Details:
- Deployment Path: {self.deployment_path}
- User: {self.deployment_user}
- Domain: {self.domain}
- Python Environment: {self.deployment_path}/venv

Services:
- Application: arena-doviz.service
- Backup: arena-doviz-backup.service (timer enabled)
- Web Server: nginx
- Database: postgresql
- Cache: redis

URLs:
- Application: https://{self.domain}
- Admin: https://{self.domain}/admin/

Important Files:
- Environment: {self.deployment_path}/.env.production
- Nginx Config: /etc/nginx/sites-available/arena-doviz
- SSL Certificates: /etc/ssl/arena-doviz/
- Logs: /var/log/arena-doviz/
- Backups: /var/backups/arena-doviz/

Next Steps:
1. Create Django superuser: sudo -u {self.deployment_user} {self.deployment_path}/venv/bin/python {self.deployment_path}/src/manage.py createsuperuser --settings=config.settings.prod
2. Load initial data if needed
3. Test all functionality
4. Set up monitoring alerts
5. Configure backup notifications
6. Update DNS records to point to this server

Security:
- Firewall configured and enabled
- Fail2ban configured for intrusion prevention
- SSL certificates configured with auto-renewal
- Application running as non-root user
- File permissions properly set

Monitoring:
- Health checks available at: https://{self.domain}/health/
- System monitoring configured
- Automated backups scheduled daily at 2:00 AM
- SSL certificate auto-renewal configured

Support:
- Service logs: journalctl -u arena-doviz.service -f
- Nginx logs: tail -f /var/log/nginx/arena-doviz.*.log
- Application logs: tail -f /var/log/arena-doviz/arena.log
""")

def main():
    """Main deployment function"""
    deployer = ProductionDeployer()
    
    print("⚠️  This will deploy Arena Doviz to production.")
    print("⚠️  Make sure you have:")
    print("   - Root access to the server")
    print("   - Domain DNS pointing to this server")
    print("   - Email configured for SSL certificates")
    print("   - Database credentials ready")
    
    confirm = input("\nProceed with production deployment? (yes/no): ").strip().lower()
    if confirm != 'yes':
        print("Deployment cancelled.")
        return
    
    success = deployer.run_complete_deployment()
    
    if success:
        print("\n🎉 Production deployment completed successfully!")
        print("🔗 Your Arena Doviz system is now live!")
    else:
        print("\n❌ Production deployment failed!")
        print("📋 Please check the error messages above and retry.")

if __name__ == "__main__":
    main()
