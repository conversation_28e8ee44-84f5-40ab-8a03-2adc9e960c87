{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Generate Report" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-file-earmark-text"></i>
                {% trans "Generate Report" %}
            </h1>
            <a href="{% url 'reports_web:list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                {% trans "Back to Reports" %}
            </a>
        </div>
    </div>
</div>

<!-- Report Type Selection -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-check"></i>
                    {% trans "Report Type" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card report-type-card" data-report-type="balance_report">
                            <div class="card-body text-center">
                                <i class="bi bi-wallet2 fs-1 text-primary mb-3"></i>
                                <h5>{% trans "Balance Report" %}</h5>
                                <p class="text-muted">{% trans "Current balances by currency and location" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card report-type-card" data-report-type="transaction_report">
                            <div class="card-body text-center">
                                <i class="bi bi-arrow-left-right fs-1 text-success mb-3"></i>
                                <h5>{% trans "Transaction Report" %}</h5>
                                <p class="text-muted">{% trans "Detailed transaction history and analysis" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card report-type-card" data-report-type="customer_statement">
                            <div class="card-body text-center">
                                <i class="bi bi-person-lines-fill fs-1 text-secondary mb-3"></i>
                                <h5>{% trans "Customer Statement" %}</h5>
                                <p class="text-muted">{% trans "Individual customer account statement" %}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second row of report types -->
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="card report-type-card" data-report-type="customer_report">
                            <div class="card-body text-center">
                                <i class="bi bi-person-check fs-1 text-info mb-3"></i>
                                <h5>{% trans "Customer Report" %}</h5>
                                <p class="text-muted">{% trans "Detailed customer transaction history and analysis" %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Configuration -->
<div class="row mb-4" id="report-config" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear"></i>
                    {% trans "Report Configuration" %}
                </h5>
            </div>
            <div class="card-body">
                <form id="report-form">
                    <input type="hidden" id="report-type" name="report_type">
                    
                    <!-- Common Fields -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date-from" class="form-label">{% trans "Date From" %}</label>
                                <input type="date" class="form-control" id="date-from" name="date_from" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date-to" class="form-label">{% trans "Date To" %}</label>
                                <input type="date" class="form-control" id="date-to" name="date_to" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location-filter" class="form-label">{% trans "Location" %}</label>
                                <select class="form-select" id="location-filter" name="location_id">
                                    <option value="">{% trans "All Locations" %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency-filter" class="form-label">{% trans "Currency" %}</label>
                                <select class="form-select" id="currency-filter" name="currency_code">
                                    <option value="">{% trans "All Currencies" %}</option>
                                    <option value="USD">USD</option>
                                    <option value="AED">AED</option>
                                    <option value="IRR">IRR</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Customer Statement Specific -->
                    <div class="row" id="customer-specific" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer-filter" class="form-label">{% trans "Customer" %} *</label>
                                <select class="form-select" id="customer-filter" name="customer_id" required>
                                    <option value="">{% trans "Select Customer" %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="include-pending" class="form-label">{% trans "Include Pending Transactions" %}</label>
                                <select class="form-select" id="include-pending" name="include_pending">
                                    <option value="false">{% trans "No" %}</option>
                                    <option value="true">{% trans "Yes" %}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Transaction Report Specific -->
                    <div class="row" id="transaction-specific" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status-filter" class="form-label">{% trans "Transaction Status" %}</label>
                                <select class="form-select" id="status-filter" name="status">
                                    <option value="">{% trans "All Statuses" %}</option>
                                    <option value="completed">{% trans "Completed" %}</option>
                                    <option value="pending">{% trans "Pending" %}</option>
                                    <option value="cancelled">{% trans "Cancelled" %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="group-by" class="form-label">{% trans "Group By" %}</label>
                                <select class="form-select" id="group-by" name="group_by">
                                    <option value="date">{% trans "Date" %}</option>
                                    <option value="customer">{% trans "Customer" %}</option>
                                    <option value="location">{% trans "Location" %}</option>
                                    <option value="currency">{% trans "Currency" %}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Balance Report Specific -->
                    <div class="row" id="balance-specific" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="balance-location-filter" class="form-label">{% trans "Location" %} *</label>
                                <select class="form-select" id="balance-location-filter" name="balance_location_id" required>
                                    <option value="">{% trans "Select Location" %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="balance-currency-filter" class="form-label">{% trans "Currency" %}</label>
                                <select class="form-select" id="balance-currency-filter" name="balance_currency_code">
                                    <option value="">{% trans "All Currencies" %}</option>
                                    <option value="USD">USD</option>
                                    <option value="AED">AED</option>
                                    <option value="IRR">IRR</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Report Specific -->
                    <div class="row" id="customer-report-specific" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer-search" class="form-label">{% trans "Customer Search" %}</label>
                                <input type="text" class="form-control" id="customer-search" name="customer_search" placeholder="{% trans 'Search by name or customer code...' %}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer-report-filter" class="form-label">{% trans "Select Customer" %}</label>
                                <select class="form-select" id="customer-report-filter" name="customer_report_id">
                                    <option value="">{% trans "All Customers" %}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Output Format -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="format" class="form-label">{% trans "Output Format" %}</label>
                                <select class="form-select" id="format" name="format">
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="csv">CSV</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="report-title" class="form-label">{% trans "Report Title" %}</label>
                                <input type="text" class="form-control" id="report-title" name="title" placeholder="{% trans 'Optional custom title' %}">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Generate Button -->
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-play-circle"></i>
                                {% trans "Generate Report" %}
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-lg ms-2" id="preview-report">
                                <i class="bi bi-eye"></i>
                                {% trans "Preview" %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Report Preview -->
<div class="row" id="report-preview" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-eye"></i>
                    {% trans "Report Preview" %}
                </h5>
                <button class="btn btn-sm btn-outline-secondary" id="close-preview">
                    <i class="bi bi-x"></i>
                    {% trans "Close Preview" %}
                </button>
            </div>
            <div class="card-body">
                <div id="preview-content">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Generation Progress -->
<div class="row" id="generation-progress" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>{% trans "Generating Report..." %}</h5>
                <p class="text-muted">{% trans "Please wait while your report is being generated." %}</p>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="progress-bar"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedReportType = null;

$(document).ready(function() {
    // Set default dates
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    $('#date-to').val(today.toISOString().split('T')[0]);
    $('#date-from').val(lastMonth.toISOString().split('T')[0]);
    
    // Load dropdown data
    loadLocations();
    loadCustomers();
    
    // Event handlers
    $('.report-type-card').on('click', function() {
        selectReportType($(this).data('report-type'));
    });
    
    $('#report-form').on('submit', function(e) {
        e.preventDefault();
        generateReport();
    });
    
    $('#preview-report').on('click', function() {
        previewReport();
    });
    
    $('#close-preview').on('click', function() {
        $('#report-preview').hide();
    });
});

function selectReportType(reportType) {
    selectedReportType = reportType;

    // Update UI
    $('.report-type-card').removeClass('border-primary');
    $(`.report-type-card[data-report-type="${reportType}"]`).addClass('border-primary');

    // Set form values
    $('#report-type').val(reportType);

    // Hide all specific sections first
    $('#customer-specific, #transaction-specific, #balance-specific, #customer-report-specific').hide();

    // Reset required fields
    $('#customer-filter, #balance-location-filter').prop('required', false);

    // Show appropriate specific fields based on report type
    if (reportType === 'customer_statement') {
        $('#customer-specific').show();
        $('#customer-filter').prop('required', true);
        loadCustomers();
    } else if (reportType === 'transaction_report' || reportType === 'transaction_summary') {
        $('#transaction-specific').show();
        loadTransactionTypes();
    } else if (reportType === 'balance_report' || reportType === 'balance_summary') {
        $('#balance-specific').show();
        $('#balance-location-filter').prop('required', true);
        loadBalanceLocations();
    } else if (reportType === 'customer_report') {
        $('#customer-report-specific').show();
        loadCustomersForReport();
    }

    // Show configuration
    $('#report-config').show();
}

function loadLocations() {
    $.ajax({
        url: '/api/v1/locations/locations/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#location-filter');
            select.find('option:not(:first)').remove();
            
            if (data.results) {
                data.results.forEach(function(location) {
                    select.append(`<option value="${location.id}">${location.name}</option>`);
                });
            }
        }
    });
}

function loadCustomers() {
    $.ajax({
        url: '/api/v1/customers/customers/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#customer-filter');
            select.find('option:not(:first)').remove();

            if (data.results) {
                data.results.forEach(function(customer) {
                    const displayName = customer.display_name || customer.customer_code || 'Unknown Customer';
                    const customerCode = customer.customer_code || '';
                    const optionText = customerCode ? `${displayName} (${customerCode})` : displayName;
                    select.append(`<option value="${customer.id}">${optionText}</option>`);
                });
            }
        }
    });
}

function loadCustomersForReport() {
    $.ajax({
        url: '/api/v1/customers/customers/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#customer-report-filter');
            select.find('option:not(:first)').remove();

            if (data.results) {
                data.results.forEach(function(customer) {
                    const displayName = customer.display_name || customer.customer_code || 'Unknown Customer';
                    const customerCode = customer.customer_code || '';
                    const optionText = customerCode ? `${displayName} (${customerCode})` : displayName;
                    select.append(`<option value="${customer.id}" data-code="${customerCode}" data-name="${displayName}">${optionText}</option>`);
                });
            }
        },
        error: function(xhr) {
            console.error('Error loading customers for report:', xhr);
        }
    });
}

// Add customer search functionality
$(document).ready(function() {
    let searchTimeout;

    // Customer search functionality with API integration
    $('#customer-search').on('input', function() {
        const searchTerm = $(this).val().trim();
        const select = $('#customer-report-filter');

        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        if (searchTerm.length === 0) {
            // Reload all customers
            loadCustomersForReport();
            return;
        }

        if (searchTerm.length < 2) {
            return; // Wait for at least 2 characters
        }

        // Debounce the search
        searchTimeout = setTimeout(function() {
            searchCustomersForReport(searchTerm);
        }, 300);
    });

    // Clear search when customer is selected
    $('#customer-report-filter').on('change', function() {
        if ($(this).val()) {
            const selectedText = $(this).find('option:selected').text();
            $('#customer-search').val(selectedText);
        }
    });
});

function searchCustomersForReport(query) {
    const select = $('#customer-report-filter');

    // Show loading
    select.find('option:not(:first)').remove();
    select.append('<option value="">{% trans "Searching..." %}</option>');

    $.ajax({
        url: '/api/v1/customers/customers/search/',
        method: 'GET',
        headers: getAuthHeaders(),
        data: { q: query },
        success: function(data) {
            // Clear loading option
            select.find('option:not(:first)').remove();

            if (data.results && data.results.length > 0) {
                data.results.forEach(function(customer) {
                    const displayName = customer.display_name || customer.customer_code || 'Unknown Customer';
                    const customerCode = customer.customer_code || '';
                    const optionText = customerCode ? `${displayName} (${customerCode})` : displayName;
                    select.append(`<option value="${customer.id}" data-code="${customerCode}" data-name="${displayName}">${optionText}</option>`);
                });
            } else {
                select.append('<option value="">{% trans "No customers found" %}</option>');
            }
        },
        error: function(xhr) {
            console.error('Error searching customers:', xhr);
            select.find('option:not(:first)').remove();
            select.append('<option value="">{% trans "Error searching customers" %}</option>');
        }
    });
}

function loadBalanceLocations() {
    $.ajax({
        url: '/api/v1/locations/locations/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#balance-location-filter');
            select.find('option:not(:first)').remove();

            if (data.results) {
                data.results.forEach(function(location) {
                    select.append(`<option value="${location.id}">${location.name}</option>`);
                });
            }
        },
        error: function(xhr) {
            console.error('Error loading locations for balance report:', xhr);
        }
    });
}

function loadTransactionTypes() {
    $.ajax({
        url: '/api/v1/transactions/types/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#transaction-type-filter');
            select.find('option:not(:first)').remove();

            if (data.results) {
                data.results.forEach(function(type) {
                    select.append(`<option value="${type.id}">${type.name}</option>`);
                });
            }
        },
        error: function(xhr) {
            console.error('Error loading transaction types:', xhr);
        }
    });
}

function generateReport() {
    if (!selectedReportType) {
        showAlert('warning', '{% trans "Please select a report type" %}');
        return;
    }

    const formData = new FormData($('#report-form')[0]);
    const parameters = {};
    let title = '';
    let format = 'pdf';

    // Extract form data
    for (let [key, value] of formData.entries()) {
        if (value) {
            if (key === 'format') {
                format = value;
            } else if (key === 'report_title') {
                title = value;
            } else if (key !== 'report_type') {
                parameters[key] = value;
            }
        }
    }

    // Create proper request data structure
    const reportData = {
        report_type: selectedReportType,
        title: title || `${selectedReportType.replace('_', ' ').toUpperCase()} Report - ${new Date().toLocaleDateString()}`,
        format: format,
        parameters: parameters
    };
    
    // Show progress
    $('#generation-progress').show();
    $('#report-config').hide();
    
    // Simulate progress
    let progress = 0;
    const progressInterval = setInterval(function() {
        progress += Math.random() * 20;
        if (progress > 90) progress = 90;
        $('#progress-bar').css('width', progress + '%');
    }, 500);
    
    // Generate report
    $.ajax({
        url: '/api/v1/reports/generated/generate/',
        method: 'POST',
        headers: {
            ...getAuthHeaders(),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(reportData),
        success: function(data) {
            clearInterval(progressInterval);
            $('#progress-bar').css('width', '100%');

            setTimeout(function() {
                $('#generation-progress').hide();

                // Close the form and reset UI
                $('#report-config').hide();
                $('.report-type-card').removeClass('border-primary');
                selectedReportType = null;
                $('#report-form')[0].reset();

                if (data.download_url) {
                    // Download the report
                    window.open(data.download_url, '_blank');
                    showAlert('success', '{% trans "Report generated and downloaded successfully!" %}');
                } else {
                    showAlert('success', '{% trans "Report generation completed successfully!" %}');
                }

                // Refresh the page to show the new report in the list
                setTimeout(function() {
                    window.location.reload();
                }, 2000);
            }, 1000);
        },
        error: function(xhr) {
            clearInterval(progressInterval);
            $('#generation-progress').hide();
            $('#report-config').show();

            let errorMessage = '{% trans "Failed to generate report" %}';

            if (xhr.responseJSON) {
                if (xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
            } else if (xhr.status === 400) {
                errorMessage = '{% trans "Invalid report parameters. Please check your inputs." %}';
            } else if (xhr.status === 500) {
                errorMessage = '{% trans "Server error occurred while generating report." %}';
            }

            showAlert('danger', errorMessage);
            console.error('Report generation error:', xhr);
        }
    });
}

function previewReport() {
    if (!selectedReportType) {
        showAlert('warning', '{% trans "Please select a report type" %}');
        return;
    }
    
    const formData = new FormData($('#report-form')[0]);
    const reportData = {};
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            reportData[key] = value;
        }
    }
    
    // Add preview flag
    reportData.preview = true;
    
    $('#preview-content').html('<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">{% trans "Loading preview..." %}</p></div>');
    $('#report-preview').show();
    
    $.ajax({
        url: `/api/v1/reports/data/${selectedReportType}_report/`,
        method: 'GET',
        headers: getAuthHeaders(),
        data: reportData,
        success: function(data) {
            displayPreview(data);
        },
        error: function(xhr) {
            $('#preview-content').html('<div class="text-center text-danger">{% trans "Failed to load preview" %}</div>');
        }
    });
}

function displayPreview(data) {
    let html = '<div class="table-responsive"><table class="table table-striped">';
    
    if (data.headers && data.rows) {
        // Table format
        html += '<thead><tr>';
        data.headers.forEach(header => {
            html += `<th>${header}</th>`;
        });
        html += '</tr></thead><tbody>';
        
        data.rows.forEach(row => {
            html += '<tr>';
            row.forEach(cell => {
                html += `<td>${cell}</td>`;
            });
            html += '</tr>';
        });
        html += '</tbody>';
    } else {
        // Simple data display
        html += '<tbody>';
        Object.entries(data).forEach(([key, value]) => {
            html += `<tr><td><strong>${key}</strong></td><td>${value}</td></tr>`;
        });
        html += '</tbody>';
    }
    
    html += '</table></div>';
    
    if (data.summary) {
        html += '<div class="mt-3"><h6>{% trans "Summary" %}</h6>';
        Object.entries(data.summary).forEach(([key, value]) => {
            html += `<p><strong>${key}:</strong> ${value}</p>`;
        });
        html += '</div>';
    }
    
    $('#preview-content').html(html);
}

function getAuthHeaders() {
    const token = ArenaDoviz.auth.getAccessToken() || '';
    const headers = {
        'Content-Type': 'application/json'
    };

    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('main .container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>

<style>
.report-type-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.report-type-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.report-type-card.border-primary {
    border-color: var(--primary-color) !important;
    background-color: rgba(0, 13, 40, 0.05);
}
</style>
{% endblock %}
