"""
Report models for Arena Doviz Exchange Accounting System.
Handles report generation, templates, and analytics.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel, TimeStampedModel
import logging

logger = logging.getLogger(__name__)


class ReportTemplate(BaseModel):
    """
    Model for storing report templates and configurations.
    """
    
    class ReportType(models.TextChoices):
        CUSTOMER_STATEMENT = 'customer_statement', _('Customer Statement')
        BALANCE_SUMMARY = 'balance_summary', _('Balance Summary')
        BALANCE_REPORT = 'balance_report', _('Balance Report')
        TRANSACTION_REPORT = 'transaction_report', _('Transaction Report')
        PROFIT_LOSS = 'profit_loss', _('Profit & Loss')
        DAILY_SUMMARY = 'daily_summary', _('Daily Summary')
        EXCHANGE_RATE_REPORT = 'exchange_rate_report', _('Exchange Rate Report')
        AUDIT_REPORT = 'audit_report', _('Audit Report')
        CUSTOM = 'custom', _('Custom Report')
    
    name = models.CharField(
        _('Template name'),
        max_length=200,
        help_text=_('Name of the report template')
    )
    
    report_type = models.CharField(
        _('Report type'),
        max_length=30,
        choices=ReportType.choices,
        help_text=_('Type of report this template generates')
    )
    
    description = models.TextField(
        _('Description'),
        blank=True,
        help_text=_('Description of what this report template does')
    )
    
    # Template configuration
    template_config = models.JSONField(
        _('Template configuration'),
        default=dict,
        help_text=_('JSON configuration for the report template')
    )
    
    # SQL query for data extraction
    sql_query = models.TextField(
        _('SQL query'),
        blank=True,
        help_text=_('SQL query to extract data for the report')
    )
    
    # Report layout and formatting
    layout_config = models.JSONField(
        _('Layout configuration'),
        default=dict,
        help_text=_('JSON configuration for report layout and formatting')
    )
    
    # Access control
    is_public = models.BooleanField(
        _('Is public'),
        default=False,
        help_text=_('Whether this template is available to all users')
    )
    
    allowed_roles = models.JSONField(
        _('Allowed roles'),
        default=list,
        help_text=_('List of user roles allowed to use this template')
    )
    
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this template is currently active')
    )
    
    sort_order = models.PositiveIntegerField(
        _('Sort order'),
        default=0,
        help_text=_('Order in which templates should be displayed')
    )
    
    class Meta:
        app_label = 'reports'
        verbose_name = _('Report Template')
        verbose_name_plural = _('Report Templates')
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['report_type']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_public']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.get_report_type_display()})"
    
    def can_be_used_by_role(self, role):
        """Check if a user role can use this template."""
        if self.is_public:
            return True
        return role in self.allowed_roles
    
    def get_default_parameters(self):
        """Get default parameters for this template."""
        return self.template_config.get('default_parameters', {})
    
    def get_required_parameters(self):
        """Get required parameters for this template."""
        return self.template_config.get('required_parameters', [])
    
    def validate_parameters(self, parameters):
        """Validate parameters against template requirements."""
        required_params = self.get_required_parameters()
        missing_params = [param for param in required_params if param not in parameters]
        
        if missing_params:
            raise ValueError(f"Missing required parameters: {', '.join(missing_params)}")
        
        return True


class GeneratedReport(BaseModel):
    """
    Model for storing generated reports and their metadata.
    """
    
    class Status(models.TextChoices):
        GENERATING = 'generating', _('Generating')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')
        EXPIRED = 'expired', _('Expired')
    
    class Format(models.TextChoices):
        PDF = 'pdf', _('PDF')
        EXCEL = 'excel', _('Excel')
        CSV = 'csv', _('CSV')
        JSON = 'json', _('JSON')
        HTML = 'html', _('HTML')
    
    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.PROTECT,
        related_name='generated_reports',
        verbose_name=_('Report template'),
        help_text=_('Template used to generate this report')
    )
    
    generated_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        related_name='generated_reports',
        verbose_name=_('Generated by'),
        help_text=_('User who generated this report')
    )
    
    # Report metadata
    title = models.CharField(
        _('Report title'),
        max_length=200,
        help_text=_('Title of the generated report')
    )
    
    parameters = models.JSONField(
        _('Report parameters'),
        default=dict,
        help_text=_('Parameters used to generate this report')
    )
    
    # Generation details
    status = models.CharField(
        _('Status'),
        max_length=20,
        choices=Status.choices,
        default=Status.GENERATING,
        help_text=_('Current status of the report generation')
    )
    
    format = models.CharField(
        _('Format'),
        max_length=10,
        choices=Format.choices,
        default=Format.PDF,
        help_text=_('Format of the generated report')
    )
    
    # File information
    file_path = models.CharField(
        _('File path'),
        max_length=500,
        blank=True,
        help_text=_('Path to the generated report file')
    )
    
    file_size = models.PositiveIntegerField(
        _('File size'),
        null=True,
        blank=True,
        help_text=_('Size of the generated report file in bytes')
    )
    
    # Generation timing
    generation_started_at = models.DateTimeField(
        _('Generation started at'),
        auto_now_add=True,
        help_text=_('When report generation started')
    )
    
    generation_completed_at = models.DateTimeField(
        _('Generation completed at'),
        null=True,
        blank=True,
        help_text=_('When report generation completed')
    )
    
    # Expiration
    expires_at = models.DateTimeField(
        _('Expires at'),
        null=True,
        blank=True,
        help_text=_('When this report expires and should be deleted')
    )
    
    # Error information
    error_message = models.TextField(
        _('Error message'),
        blank=True,
        help_text=_('Error message if generation failed')
    )
    
    # Statistics
    record_count = models.PositiveIntegerField(
        _('Record count'),
        null=True,
        blank=True,
        help_text=_('Number of records in the report')
    )
    
    download_count = models.PositiveIntegerField(
        _('Download count'),
        default=0,
        help_text=_('Number of times this report has been downloaded')
    )
    
    last_downloaded_at = models.DateTimeField(
        _('Last downloaded at'),
        null=True,
        blank=True,
        help_text=_('When this report was last downloaded')
    )
    
    class Meta:
        app_label = 'reports'
        verbose_name = _('Generated Report')
        verbose_name_plural = _('Generated Reports')
        ordering = ['-generation_started_at']
        indexes = [
            models.Index(fields=['generated_by', 'status']),
            models.Index(fields=['template', 'status']),
            models.Index(fields=['status']),
            models.Index(fields=['generation_started_at']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.get_status_display()}"
    
    def is_completed(self):
        """Check if report generation is completed."""
        return self.status == self.Status.COMPLETED
    
    def is_failed(self):
        """Check if report generation failed."""
        return self.status == self.Status.FAILED
    
    def is_expired(self):
        """Check if report is expired."""
        if not self.expires_at:
            return False
        
        from django.utils import timezone
        return timezone.now() > self.expires_at
    
    def get_generation_duration(self):
        """Get the duration of report generation."""
        if self.generation_completed_at and self.generation_started_at:
            return self.generation_completed_at - self.generation_started_at
        return None
    
    def get_file_size_formatted(self):
        """Get formatted file size."""
        if not self.file_size:
            return "Unknown"
        
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def mark_downloaded(self):
        """Mark the report as downloaded."""
        from django.utils import timezone
        
        self.download_count += 1
        self.last_downloaded_at = timezone.now()
        self.save(update_fields=['download_count', 'last_downloaded_at'])
        
        logger.info(f"Report downloaded: {self.title} (download #{self.download_count})")
    
    def mark_completed(self, file_path, file_size=None, record_count=None):
        """Mark the report generation as completed."""
        from django.utils import timezone
        
        self.status = self.Status.COMPLETED
        self.file_path = file_path
        self.file_size = file_size
        self.record_count = record_count
        self.generation_completed_at = timezone.now()
        
        # Set expiration (default: 7 days from completion)
        if not self.expires_at:
            from datetime import timedelta
            self.expires_at = self.generation_completed_at + timedelta(days=7)
        
        self.save(update_fields=[
            'status', 'file_path', 'file_size', 'record_count',
            'generation_completed_at', 'expires_at'
        ])
        
        logger.info(f"Report generation completed: {self.title}")
    
    def mark_failed(self, error_message):
        """Mark the report generation as failed."""
        from django.utils import timezone
        
        self.status = self.Status.FAILED
        self.error_message = error_message
        self.generation_completed_at = timezone.now()
        
        self.save(update_fields=['status', 'error_message', 'generation_completed_at'])
        
        logger.error(f"Report generation failed: {self.title} - {error_message}")
    
    @classmethod
    def cleanup_expired_reports(cls):
        """Clean up expired reports."""
        from django.utils import timezone
        import os
        
        expired_reports = cls.objects.filter(
            expires_at__lt=timezone.now(),
            status=cls.Status.COMPLETED
        )
        
        deleted_count = 0
        for report in expired_reports:
            try:
                # Delete the file if it exists
                if report.file_path and os.path.exists(report.file_path):
                    os.remove(report.file_path)
                
                # Mark as expired
                report.status = cls.Status.EXPIRED
                report.save(update_fields=['status'])
                
                deleted_count += 1
                
            except Exception as e:
                logger.error(f"Error cleaning up expired report {report.id}: {e}")
        
        logger.info(f"Cleaned up {deleted_count} expired reports")
        return deleted_count


class ReportSchedule(BaseModel):
    """
    Model for scheduling automatic report generation.
    """
    
    class Frequency(models.TextChoices):
        DAILY = 'daily', _('Daily')
        WEEKLY = 'weekly', _('Weekly')
        MONTHLY = 'monthly', _('Monthly')
        QUARTERLY = 'quarterly', _('Quarterly')
        YEARLY = 'yearly', _('Yearly')
    
    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name=_('Report template'),
        help_text=_('Template to use for scheduled reports')
    )
    
    name = models.CharField(
        _('Schedule name'),
        max_length=200,
        help_text=_('Name of the report schedule')
    )
    
    description = models.TextField(
        _('Description'),
        blank=True,
        help_text=_('Description of the scheduled report')
    )
    
    # Schedule configuration
    frequency = models.CharField(
        _('Frequency'),
        max_length=20,
        choices=Frequency.choices,
        help_text=_('How often to generate the report')
    )
    
    parameters = models.JSONField(
        _('Report parameters'),
        default=dict,
        help_text=_('Parameters to use for scheduled report generation')
    )
    
    format = models.CharField(
        _('Format'),
        max_length=10,
        choices=GeneratedReport.Format.choices,
        default=GeneratedReport.Format.PDF,
        help_text=_('Format for scheduled reports')
    )
    
    # Recipients
    email_recipients = models.JSONField(
        _('Email recipients'),
        default=list,
        help_text=_('List of email addresses to send the report to')
    )
    
    # Schedule timing
    next_run_at = models.DateTimeField(
        _('Next run at'),
        help_text=_('When to generate the next report')
    )
    
    last_run_at = models.DateTimeField(
        _('Last run at'),
        null=True,
        blank=True,
        help_text=_('When the report was last generated')
    )
    
    # Status
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this schedule is currently active')
    )
    
    # Statistics
    run_count = models.PositiveIntegerField(
        _('Run count'),
        default=0,
        help_text=_('Number of times this schedule has run')
    )
    
    success_count = models.PositiveIntegerField(
        _('Success count'),
        default=0,
        help_text=_('Number of successful report generations')
    )
    
    failure_count = models.PositiveIntegerField(
        _('Failure count'),
        default=0,
        help_text=_('Number of failed report generations')
    )
    
    class Meta:
        app_label = 'reports'
        verbose_name = _('Report Schedule')
        verbose_name_plural = _('Report Schedules')
        ordering = ['next_run_at']
        indexes = [
            models.Index(fields=['is_active', 'next_run_at']),
            models.Index(fields=['template']),
            models.Index(fields=['frequency']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.get_frequency_display()})"
    
    def calculate_next_run(self):
        """Calculate the next run time based on frequency."""
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        
        if self.frequency == self.Frequency.DAILY:
            return now + timedelta(days=1)
        elif self.frequency == self.Frequency.WEEKLY:
            return now + timedelta(weeks=1)
        elif self.frequency == self.Frequency.MONTHLY:
            return now + timedelta(days=30)  # Approximate
        elif self.frequency == self.Frequency.QUARTERLY:
            return now + timedelta(days=90)  # Approximate
        elif self.frequency == self.Frequency.YEARLY:
            return now + timedelta(days=365)  # Approximate
        
        return now + timedelta(days=1)  # Default to daily
    
    def mark_run_completed(self, success=True):
        """Mark a scheduled run as completed."""
        from django.utils import timezone
        
        self.run_count += 1
        if success:
            self.success_count += 1
        else:
            self.failure_count += 1
        
        self.last_run_at = timezone.now()
        self.next_run_at = self.calculate_next_run()
        
        self.save(update_fields=[
            'run_count', 'success_count', 'failure_count',
            'last_run_at', 'next_run_at'
        ])
        
        status = "successfully" if success else "with failure"
        logger.info(f"Report schedule run completed {status}: {self.name}")
    
    def get_success_rate(self):
        """Get the success rate as a percentage."""
        if self.run_count == 0:
            return 0
        return (self.success_count / self.run_count) * 100
    
    @classmethod
    def get_due_schedules(cls):
        """Get schedules that are due to run."""
        from django.utils import timezone
        
        return cls.objects.filter(
            is_active=True,
            is_deleted=False,
            next_run_at__lte=timezone.now()
        ).order_by('next_run_at')
