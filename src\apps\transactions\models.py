"""
Transaction models for Arena Doviz Exchange Accounting System.
Implements double-entry bookkeeping with comprehensive transaction tracking.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from django.core.exceptions import ValidationError
from apps.core.models import BaseModel, TimeStampedModel
from decimal import Decimal
import logging

# Import commission models
from .commission_models import CommissionRule, CommissionTier

logger = logging.getLogger(__name__)


class TransactionType(BaseModel):
    """
    Model defining different types of transactions supported by the system.
    """
    
    code = models.CharField(
        _('Transaction type code'),
        max_length=10,
        unique=True,
        help_text=_('Unique code for the transaction type (e.g., JV, TSN, TRQ)')
    )
    
    name = models.CharField(
        _('Transaction type name'),
        max_length=100,
        help_text=_('Human-readable name for the transaction type')
    )
    
    description = models.TextField(
        _('Description'),
        blank=True,
        help_text=_('Detailed description of the transaction type')
    )
    
    is_exchange = models.BooleanField(
        _('Is exchange transaction'),
        default=False,
        help_text=_('Whether this transaction type involves currency exchange')
    )
    
    requires_approval = models.BooleanField(
        _('Requires approval'),
        default=False,
        help_text=_('Whether transactions of this type require approval')
    )
    
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this transaction type is currently active')
    )
    
    sort_order = models.PositiveIntegerField(
        _('Sort order'),
        default=0,
        help_text=_('Order in which transaction types should be displayed')
    )
    
    class Meta:
        verbose_name = _('Transaction Type')
        verbose_name_plural = _('Transaction Types')
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_exchange']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class Transaction(BaseModel):
    """
    Main transaction model implementing double-entry bookkeeping principles.
    Each transaction affects customer and company balances.
    """
    
    class Status(models.TextChoices):
        DRAFT = 'draft', _('Draft')
        PENDING = 'pending', _('Pending Approval')
        APPROVED = 'approved', _('Approved')
        COMPLETED = 'completed', _('Completed')
        CANCELLED = 'cancelled', _('Cancelled')
        REJECTED = 'rejected', _('Rejected')
    
    class DeliveryMethod(models.TextChoices):
        IN_PERSON = 'in_person', _('In Person')
        COURIER = 'courier', _('Courier')
        BANK_TRANSFER = 'bank_transfer', _('Bank Transfer')
        SWIFT = 'swift', _('SWIFT Transfer')
        CASH = 'cash', _('Cash')
        INTERNAL = 'internal', _('Internal Transfer')
    
    # Transaction identification
    transaction_number = models.CharField(
        _('Transaction number'),
        max_length=50,
        unique=True,
        help_text=_('Unique transaction number (auto-generated)')
    )
    
    transaction_type = models.ForeignKey(
        TransactionType,
        on_delete=models.PROTECT,
        related_name='transactions',
        verbose_name=_('Transaction type'),
        help_text=_('Type of transaction')
    )
    
    # Parties involved
    customer = models.ForeignKey(
        'customers.Customer',
        on_delete=models.PROTECT,
        related_name='transactions',
        verbose_name=_('Customer'),
        help_text=_('Customer involved in the transaction')
    )
    
    location = models.ForeignKey(
        'locations.Location',
        on_delete=models.PROTECT,
        related_name='transactions',
        verbose_name=_('Location'),
        help_text=_('Location where the transaction takes place')
    )
    
    # Transaction details
    description = models.TextField(
        _('Description'),
        blank=True,
        help_text=_('Description of the transaction')
    )
    
    reference_number = models.CharField(
        _('Reference number'),
        max_length=100,
        blank=True,
        help_text=_('External reference number (bank reference, etc.)')
    )
    
    # Currency and amounts
    from_currency = models.ForeignKey(
        'currencies.Currency',
        on_delete=models.PROTECT,
        related_name='transactions_from',
        verbose_name=_('From currency'),
        help_text=_('Currency being exchanged from')
    )
    
    to_currency = models.ForeignKey(
        'currencies.Currency',
        on_delete=models.PROTECT,
        related_name='transactions_to',
        verbose_name=_('To currency'),
        help_text=_('Currency being exchanged to')
    )
    
    from_amount = models.DecimalField(
        _('From amount'),
        max_digits=15,
        decimal_places=6,
        validators=[MinValueValidator(Decimal('0.000001'))],
        help_text=_('Amount in the from currency')
    )
    
    to_amount = models.DecimalField(
        _('To amount'),
        max_digits=15,
        decimal_places=6,
        validators=[MinValueValidator(Decimal('0.000001'))],
        help_text=_('Amount in the to currency')
    )
    
    exchange_rate = models.DecimalField(
        _('Exchange rate'),
        max_digits=15,
        decimal_places=6,
        validators=[MinValueValidator(Decimal('0.000001'))],
        help_text=_('Exchange rate used for the transaction')
    )
    
    commission_amount = models.DecimalField(
        _('Commission amount'),
        max_digits=15,
        decimal_places=6,
        default=Decimal('0'),
        help_text=_('Commission charged for the transaction')
    )
    
    commission_currency = models.ForeignKey(
        'currencies.Currency',
        on_delete=models.PROTECT,
        related_name='commission_transactions',
        null=True,
        blank=True,
        verbose_name=_('Commission currency'),
        help_text=_('Currency in which commission is charged')
    )
    
    # Transaction status and workflow
    status = models.CharField(
        _('Status'),
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT,
        help_text=_('Current status of the transaction')
    )
    
    # Delivery information
    delivery_method = models.CharField(
        _('Delivery method'),
        max_length=20,
        choices=DeliveryMethod.choices,
        default=DeliveryMethod.IN_PERSON,
        help_text=_('Method of delivery for the transaction')
    )
    
    courier = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='courier_transactions',
        verbose_name=_('Courier'),
        help_text=_('Courier assigned for delivery (if applicable)')
    )
    
    delivery_address = models.TextField(
        _('Delivery address'),
        blank=True,
        help_text=_('Address for delivery (if applicable)')
    )
    
    tracking_code = models.CharField(
        _('Tracking code'),
        max_length=100,
        blank=True,
        help_text=_('Tracking code for delivery')
    )
    
    # Approval workflow
    approved_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_transactions',
        verbose_name=_('Approved by'),
        help_text=_('User who approved the transaction')
    )
    
    approved_at = models.DateTimeField(
        _('Approved at'),
        null=True,
        blank=True,
        help_text=_('Date and time when transaction was approved')
    )
    
    # Completion information
    completed_at = models.DateTimeField(
        _('Completed at'),
        null=True,
        blank=True,
        help_text=_('Date and time when transaction was completed')
    )
    
    # Multi-step transaction support
    parent_transaction = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='child_transactions',
        verbose_name=_('Parent transaction'),
        help_text=_('Parent transaction for multi-step transactions')
    )
    
    step_number = models.PositiveIntegerField(
        _('Step number'),
        default=1,
        help_text=_('Step number in multi-step transaction')
    )
    
    total_steps = models.PositiveIntegerField(
        _('Total steps'),
        default=1,
        help_text=_('Total number of steps in multi-step transaction')
    )
    
    # Additional information
    notes = models.TextField(
        _('Notes'),
        blank=True,
        help_text=_('Additional notes about the transaction')
    )
    
    class Meta:
        verbose_name = _('Transaction')
        verbose_name_plural = _('Transactions')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['transaction_number']),
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['location', 'status']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['from_currency', 'to_currency']),
            models.Index(fields=['parent_transaction']),
        ]
    
    def __str__(self):
        return f"{self.transaction_number} - {self.customer.get_display_name()}"
    
    def get_display_amount(self):
        """Return formatted display amount."""
        return f"{self.from_currency.format_amount_with_symbol(self.from_amount)} → {self.to_currency.format_amount_with_symbol(self.to_amount)}"
    
    def is_exchange_transaction(self):
        """Check if this is an exchange transaction."""
        return self.from_currency != self.to_currency
    
    def is_multi_step(self):
        """Check if this is part of a multi-step transaction."""
        return self.total_steps > 1 or self.parent_transaction is not None
    
    def is_completed(self):
        """Check if transaction is completed."""
        return self.status == self.Status.COMPLETED
    
    def is_pending_approval(self):
        """Check if transaction is pending approval."""
        return self.status == self.Status.PENDING
    
    def can_be_approved(self):
        """Check if transaction can be approved."""
        return self.status in [self.Status.DRAFT, self.Status.PENDING]
    
    def can_be_cancelled(self):
        """Check if transaction can be cancelled."""
        return self.status in [self.Status.DRAFT, self.Status.PENDING, self.Status.APPROVED]
    
    def calculate_profit(self):
        """Calculate profit from the transaction."""
        # This is a simplified calculation - in practice, you'd need to consider
        # the actual exchange rates and costs
        if not self.is_exchange_transaction():
            return Decimal('0')
        
        # Get current market rate and compare with transaction rate
        from apps.currencies.models import ExchangeRate
        market_rate = ExchangeRate.get_current_rate(
            self.from_currency, self.to_currency, self.location, 'sell'
        )
        
        if market_rate:
            market_amount = self.from_amount * market_rate
            profit = self.to_amount - market_amount
            return profit + (self.commission_amount or Decimal('0'))
        
        return self.commission_amount or Decimal('0')
    
    def get_child_transactions(self):
        """Get all child transactions for multi-step transactions."""
        if self.parent_transaction:
            return self.parent_transaction.child_transactions.all()
        return self.child_transactions.all()
    
    def get_total_completed_amount(self):
        """Get total completed amount for multi-step transactions."""
        if not self.is_multi_step():
            return self.from_amount if self.is_completed() else Decimal('0')
        
        completed_transactions = self.get_child_transactions().filter(
            status=self.Status.COMPLETED
        )
        return sum(t.from_amount for t in completed_transactions)
    
    def get_completion_percentage(self):
        """Get completion percentage for multi-step transactions."""
        if not self.is_multi_step():
            return 100 if self.is_completed() else 0
        
        total_amount = self.from_amount
        completed_amount = self.get_total_completed_amount()
        
        if total_amount > 0:
            return min(100, (completed_amount / total_amount) * 100)
        return 0
    
    def clean(self):
        """Validate transaction data."""
        super().clean()
        
        # Validate amounts
        if self.from_amount <= 0:
            raise ValidationError({
                'from_amount': _('From amount must be positive')
            })
        
        if self.to_amount <= 0:
            raise ValidationError({
                'to_amount': _('To amount must be positive')
            })
        
        # Validate exchange rate calculation
        if self.is_exchange_transaction():
            calculated_rate = self.to_amount / self.from_amount
            rate_difference = abs(calculated_rate - self.exchange_rate)
            
            # Allow small rounding differences
            if rate_difference > Decimal('0.000001'):
                raise ValidationError({
                    'exchange_rate': _('Exchange rate does not match the amount calculation')
                })
        
        # Validate multi-step transaction
        if self.parent_transaction and self.parent_transaction == self:
            raise ValidationError({
                'parent_transaction': _('Transaction cannot be its own parent')
            })
        
        # Validate step numbers
        if self.step_number > self.total_steps:
            raise ValidationError({
                'step_number': _('Step number cannot be greater than total steps')
            })
    
    def calculate_commission_automatically(self):
        """
        Calculate commission automatically using commission rules.
        """
        try:
            from .commission_utils import commission_calculator

            commission_result = commission_calculator.calculate_commission(self)

            if commission_result['amount'] > 0:
                self.commission_amount = commission_result['amount']
                self.commission_currency = commission_result['currency']

                logger.info(f"Auto-calculated commission for transaction {self.transaction_number}: "
                           f"{self.commission_currency.format_amount_with_symbol(self.commission_amount)}")

                return commission_result
            else:
                logger.info(f"No commission calculated for transaction {self.transaction_number}")
                return None

        except Exception as e:
            logger.error(f"Error calculating commission for transaction {self.transaction_number}: {str(e)}")
            return None

    def save(self, *args, **kwargs):
        """
        Override save to handle business logic.
        """
        # Auto-generate transaction number if not provided
        if not self.transaction_number:
            self.transaction_number = self._generate_transaction_number()

        # Calculate commission automatically if not set and transaction is being created
        is_new_transaction = self._state.adding
        if is_new_transaction and self.commission_amount == 0:
            commission_result = self.calculate_commission_automatically()

        # Set commission currency to from_currency if not specified
        if not self.commission_currency:
            self.commission_currency = self.from_currency

        super().save(*args, **kwargs)

        # Create balance entries for double-entry bookkeeping
        if self.status == self.Status.COMPLETED and not hasattr(self, '_balance_entries_created'):
            self._create_balance_entries()
            self._balance_entries_created = True

        logger.info(f"Transaction saved: {self.transaction_number} - {self.status}")
    
    def _generate_transaction_number(self):
        """Generate a unique transaction number."""
        from django.utils import timezone

        # Format: TXN-YYYYMMDD-NNNN (prefix + date + sequential number)
        today = timezone.now().date()
        date_str = today.strftime('%Y%m%d')
        prefix = f"TXN-{date_str}"

        # Find the last transaction number for today
        last_transaction = Transaction.objects.filter(
            transaction_number__startswith=prefix
        ).order_by('-transaction_number').first()

        if last_transaction and last_transaction.transaction_number:
            try:
                last_number = int(last_transaction.transaction_number.split('-')[2])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1

        return f"{prefix}-{next_number:04d}"
    
    def _create_balance_entries(self):
        """Create balance entries for double-entry bookkeeping."""
        # Customer receives to_currency
        BalanceEntry.objects.create(
            transaction=self,
            customer=self.customer,
            location=self.location,
            currency=self.to_currency,
            amount=self.to_amount,
            entry_type=BalanceEntry.EntryType.CREDIT,
            description=f"Received from transaction {self.transaction_number}"
        )
        
        # Customer pays from_currency
        BalanceEntry.objects.create(
            transaction=self,
            customer=self.customer,
            location=self.location,
            currency=self.from_currency,
            amount=-self.from_amount,
            entry_type=BalanceEntry.EntryType.DEBIT,
            description=f"Paid for transaction {self.transaction_number}"
        )
        
        # Company receives from_currency
        BalanceEntry.objects.create(
            transaction=self,
            customer=None,  # Company balance
            location=self.location,
            currency=self.from_currency,
            amount=self.from_amount,
            entry_type=BalanceEntry.EntryType.CREDIT,
            description=f"Received from customer for transaction {self.transaction_number}"
        )
        
        # Company pays to_currency
        BalanceEntry.objects.create(
            transaction=self,
            customer=None,  # Company balance
            location=self.location,
            currency=self.to_currency,
            amount=-self.to_amount,
            entry_type=BalanceEntry.EntryType.DEBIT,
            description=f"Paid to customer for transaction {self.transaction_number}"
        )
        
        # Commission entry if applicable
        if self.commission_amount > 0:
            BalanceEntry.objects.create(
                transaction=self,
                customer=None,  # Company balance
                location=self.location,
                currency=self.commission_currency,
                amount=self.commission_amount,
                entry_type=BalanceEntry.EntryType.CREDIT,
                description=f"Commission from transaction {self.transaction_number}"
            )
        
        logger.info(f"Balance entries created for transaction {self.transaction_number}")


class BalanceEntry(BaseModel):
    """
    Model for individual balance entries implementing double-entry bookkeeping.
    Each transaction creates multiple balance entries to maintain accurate balances.
    """
    
    class EntryType(models.TextChoices):
        DEBIT = 'debit', _('Debit')
        CREDIT = 'credit', _('Credit')
    
    transaction = models.ForeignKey(
        Transaction,
        on_delete=models.PROTECT,
        related_name='balance_entries',
        verbose_name=_('Transaction'),
        help_text=_('Transaction that created this balance entry')
    )
    
    customer = models.ForeignKey(
        'customers.Customer',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='balance_entries',
        verbose_name=_('Customer'),
        help_text=_('Customer whose balance is affected (null for company balance)')
    )
    
    location = models.ForeignKey(
        'locations.Location',
        on_delete=models.PROTECT,
        related_name='balance_entries',
        verbose_name=_('Location'),
        help_text=_('Location where the balance change occurred')
    )
    
    currency = models.ForeignKey(
        'currencies.Currency',
        on_delete=models.PROTECT,
        related_name='balance_entries',
        verbose_name=_('Currency'),
        help_text=_('Currency of the balance entry')
    )
    
    amount = models.DecimalField(
        _('Amount'),
        max_digits=15,
        decimal_places=6,
        help_text=_('Amount of the balance change (positive for credit, negative for debit)')
    )
    
    entry_type = models.CharField(
        _('Entry type'),
        max_length=10,
        choices=EntryType.choices,
        help_text=_('Type of balance entry (debit or credit)')
    )
    
    description = models.TextField(
        _('Description'),
        help_text=_('Description of the balance entry')
    )
    
    # Running balance after this entry
    running_balance = models.DecimalField(
        _('Running balance'),
        max_digits=15,
        decimal_places=6,
        null=True,
        blank=True,
        help_text=_('Running balance after this entry')
    )
    
    class Meta:
        verbose_name = _('Balance Entry')
        verbose_name_plural = _('Balance Entries')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'currency']),
            models.Index(fields=['location', 'currency']),
            models.Index(fields=['transaction']),
            models.Index(fields=['created_at']),
            models.Index(fields=['entry_type']),
        ]
    
    def __str__(self):
        customer_str = self.customer.get_display_name() if self.customer else 'Company'
        return f"{customer_str} - {self.currency.code} {self.amount}"
    
    def is_debit(self):
        """Check if this is a debit entry."""
        return self.entry_type == self.EntryType.DEBIT
    
    def is_credit(self):
        """Check if this is a credit entry."""
        return self.entry_type == self.EntryType.CREDIT
    
    def save(self, *args, **kwargs):
        """
        Override save to calculate running balance.
        """
        # Set entry type based on amount if not specified
        if not self.entry_type:
            self.entry_type = self.EntryType.CREDIT if self.amount >= 0 else self.EntryType.DEBIT
        
        super().save(*args, **kwargs)
        
        # Calculate running balance
        self._calculate_running_balance()
        
        logger.debug(f"Balance entry saved: {self}")
    
    def _calculate_running_balance(self):
        """Calculate and update the running balance."""
        # Get all balance entries for the same customer/location/currency up to this entry
        entries = BalanceEntry.objects.filter(
            customer=self.customer,
            location=self.location,
            currency=self.currency,
            created_at__lte=self.created_at,
            is_deleted=False
        ).order_by('created_at')
        
        running_balance = Decimal('0')
        for entry in entries:
            running_balance += entry.amount
            if entry.pk == self.pk:
                self.running_balance = running_balance
                BalanceEntry.objects.filter(pk=self.pk).update(running_balance=running_balance)
                break
    
    @classmethod
    def get_current_balance(cls, customer=None, location=None, currency=None):
        """
        Get current balance for a customer/location/currency combination.
        
        Args:
            customer: Customer object (None for company balance)
            location: Location object
            currency: Currency object
        
        Returns:
            Decimal: Current balance
        """
        try:
            balance = cls.objects.filter(
                customer=customer,
                location=location,
                currency=currency,
                is_deleted=False
            ).aggregate(total=models.Sum('amount'))['total']
            
            return balance or Decimal('0')
            
        except Exception as e:
            logger.error(f"Error calculating current balance: {e}")
            return Decimal('0')
    
    @classmethod
    def get_balance_history(cls, customer=None, location=None, currency=None, limit=100):
        """Get balance history for a customer/location/currency combination."""
        return cls.objects.filter(
            customer=customer,
            location=location,
            currency=currency,
            is_deleted=False
        ).order_by('-created_at')[:limit]


class TransactionDocument(BaseModel):
    """
    Model for storing transaction-related documents and attachments.
    """

    class DocumentType(models.TextChoices):
        RECEIPT = 'receipt', _('Receipt')
        INVOICE = 'invoice', _('Invoice')
        DELIVERY_CONFIRMATION = 'delivery_confirmation', _('Delivery Confirmation')
        BANK_SLIP = 'bank_slip', _('Bank Slip')
        SWIFT_CONFIRMATION = 'swift_confirmation', _('SWIFT Confirmation')
        COURIER_RECEIPT = 'courier_receipt', _('Courier Receipt')
        CUSTOMER_ID = 'customer_id', _('Customer ID Copy')
        CONTRACT = 'contract', _('Contract')
        OTHER = 'other', _('Other')

    transaction = models.ForeignKey(
        Transaction,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('Transaction')
    )

    document_type = models.CharField(
        _('Document type'),
        max_length=50,
        choices=DocumentType.choices,
        help_text=_('Type of document')
    )

    title = models.CharField(
        _('Document title'),
        max_length=200,
        help_text=_('Title or description of the document')
    )

    file = models.FileField(
        _('File'),
        upload_to='transaction_documents/%Y/%m/',
        help_text=_('Document file')
    )

    file_size = models.PositiveIntegerField(
        _('File size'),
        null=True,
        blank=True,
        help_text=_('File size in bytes')
    )

    mime_type = models.CharField(
        _('MIME type'),
        max_length=100,
        blank=True,
        help_text=_('MIME type of the file')
    )

    uploaded_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='uploaded_transaction_documents',
        verbose_name=_('Uploaded by'),
        help_text=_('User who uploaded this document')
    )

    is_required = models.BooleanField(
        _('Is required'),
        default=False,
        help_text=_('Whether this document is required for transaction completion')
    )

    is_verified = models.BooleanField(
        _('Is verified'),
        default=False,
        help_text=_('Whether this document has been verified')
    )

    verified_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_transaction_documents',
        verbose_name=_('Verified by'),
        help_text=_('User who verified this document')
    )

    verified_at = models.DateTimeField(
        _('Verified at'),
        null=True,
        blank=True,
        help_text=_('Date and time when document was verified')
    )

    notes = models.TextField(
        _('Notes'),
        blank=True,
        help_text=_('Additional notes about this document')
    )

    class Meta:
        verbose_name = _('Transaction Document')
        verbose_name_plural = _('Transaction Documents')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['transaction', 'document_type']),
            models.Index(fields=['is_required']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.title} ({self.transaction.transaction_number})"

    def get_file_extension(self):
        """Get file extension."""
        if self.file:
            return self.file.name.split('.')[-1].lower()
        return ''

    def get_file_size_formatted(self):
        """Get formatted file size."""
        if not self.file_size:
            return ''

        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    def save(self, *args, **kwargs):
        """
        Override save to handle file metadata.
        """
        if self.file:
            # Set file size
            if hasattr(self.file, 'size'):
                self.file_size = self.file.size

            # Set MIME type based on file extension
            if not self.mime_type:
                extension = self.get_file_extension()
                mime_types = {
                    'pdf': 'application/pdf',
                    'jpg': 'image/jpeg',
                    'jpeg': 'image/jpeg',
                    'png': 'image/png',
                    'gif': 'image/gif',
                    'doc': 'application/msword',
                    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'xls': 'application/vnd.ms-excel',
                    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'txt': 'text/plain',
                    'csv': 'text/csv',
                }
                self.mime_type = mime_types.get(extension, 'application/octet-stream')

        super().save(*args, **kwargs)

        logger.info(f"Transaction document saved: {self.title} for {self.transaction.transaction_number}")

    def delete(self, *args, **kwargs):
        """
        Override delete to handle file cleanup.
        """
        # Delete the file from storage
        if self.file:
            try:
                self.file.delete(save=False)
            except Exception as e:
                logger.warning(f"Failed to delete file {self.file.name}: {e}")

        super().delete(*args, **kwargs)
        logger.info(f"Transaction document deleted: {self.title}")
