#!/usr/bin/env python3
"""
Complete PostgreSQL Setup for Arena Doviz Production
"""
import os
import sys
import subprocess
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import getpass
from pathlib import Path

class PostgreSQLSetup:
    """Complete PostgreSQL setup and configuration"""
    
    def __init__(self):
        self.db_name = "arena_doviz_prod"
        self.db_user = "arena_user"
        self.db_password = "ArenaD0viz2025!SecureProd"
        self.db_host = "localhost"
        self.db_port = "5432"
        
    def install_postgresql(self):
        """Install PostgreSQL based on the operating system"""
        print("🐘 Installing PostgreSQL...")
        
        system = os.name
        
        if system == 'nt':  # Windows
            print("Windows detected. Please install PostgreSQL manually:")
            print("1. Download from: https://www.postgresql.org/download/windows/")
            print("2. Run the installer and follow the setup wizard")
            print("3. Remember the superuser password you set")
            print("4. Ensure PostgreSQL service is running")
            input("Press Enter after PostgreSQL installation is complete...")
        else:
            # Linux/Unix systems
            try:
                # Detect Linux distribution
                if os.path.exists('/etc/debian_version'):
                    # Debian/Ubuntu
                    subprocess.run(['sudo', 'apt-get', 'update'], check=True)
                    subprocess.run(['sudo', 'apt-get', 'install', '-y', 'postgresql', 'postgresql-contrib', 'python3-psycopg2'], check=True)
                elif os.path.exists('/etc/redhat-release'):
                    # RHEL/CentOS/Fedora
                    subprocess.run(['sudo', 'yum', 'install', '-y', 'postgresql-server', 'postgresql-contrib', 'python3-psycopg2'], check=True)
                    subprocess.run(['sudo', 'postgresql-setup', 'initdb'], check=True)
                    subprocess.run(['sudo', 'systemctl', 'enable', 'postgresql'], check=True)
                    subprocess.run(['sudo', 'systemctl', 'start', 'postgresql'], check=True)
                else:
                    print("Unsupported Linux distribution. Please install PostgreSQL manually.")
                    return False
                
                print("✅ PostgreSQL installed successfully")
                return True
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install PostgreSQL: {e}")
                return False
    
    def create_database_and_user(self):
        """Create database and user"""
        print("🔧 Creating database and user...")
        
        # Get PostgreSQL superuser password
        postgres_password = getpass.getpass("Enter PostgreSQL superuser (postgres) password: ")
        
        try:
            # Connect as postgres superuser
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user='postgres',
                password=postgres_password,
                database='postgres'
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            # Create user
            print(f"Creating user: {self.db_user}")
            cursor.execute(f"""
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{self.db_user}') THEN
                        CREATE USER {self.db_user} WITH PASSWORD '{self.db_password}';
                    END IF;
                END
                $$;
            """)
            
            # Create database
            print(f"Creating database: {self.db_name}")
            cursor.execute(f"""
                SELECT 1 FROM pg_database WHERE datname = '{self.db_name}'
            """)
            
            if not cursor.fetchone():
                cursor.execute(f"CREATE DATABASE {self.db_name} OWNER {self.db_user}")
            
            # Grant privileges
            cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {self.db_name} TO {self.db_user}")
            cursor.execute(f"ALTER USER {self.db_user} CREATEDB")
            
            cursor.close()
            conn.close()
            
            print("✅ Database and user created successfully")
            return True
            
        except psycopg2.Error as e:
            print(f"❌ Database creation failed: {e}")
            return False
    
    def configure_postgresql(self):
        """Configure PostgreSQL for production"""
        print("⚙️ Configuring PostgreSQL for production...")
        
        # Find PostgreSQL configuration directory
        config_paths = [
            '/etc/postgresql/*/main/',
            '/var/lib/pgsql/data/',
            '/usr/local/var/postgres/',
            'C:\\Program Files\\PostgreSQL\\*\\data\\'
        ]
        
        config_dir = None
        for path_pattern in config_paths:
            import glob
            matches = glob.glob(path_pattern)
            if matches:
                config_dir = matches[0]
                break
        
        if not config_dir:
            print("⚠️ Could not find PostgreSQL configuration directory")
            print("Please manually configure postgresql.conf and pg_hba.conf")
            return False
        
        print(f"Found PostgreSQL config directory: {config_dir}")
        
        # Backup original configurations
        postgresql_conf = os.path.join(config_dir, 'postgresql.conf')
        pg_hba_conf = os.path.join(config_dir, 'pg_hba.conf')
        
        if os.path.exists(postgresql_conf):
            subprocess.run(['cp', postgresql_conf, f"{postgresql_conf}.backup"], check=False)
        
        if os.path.exists(pg_hba_conf):
            subprocess.run(['cp', pg_hba_conf, f"{pg_hba_conf}.backup"], check=False)
        
        # Production PostgreSQL configuration
        production_config = """
# Arena Doviz Production PostgreSQL Configuration

# Connection Settings
listen_addresses = 'localhost'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# WAL Settings
wal_level = replica
max_wal_size = 1GB
min_wal_size = 80MB
checkpoint_completion_target = 0.9

# Query Planner
random_page_cost = 1.1
effective_io_concurrency = 200

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on

# Security
ssl = on
password_encryption = scram-sha-256

# Performance
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
"""
        
        try:
            # Append production settings to postgresql.conf
            with open(postgresql_conf, 'a') as f:
                f.write(production_config)
            
            print("✅ PostgreSQL configuration updated")
            
            # Update pg_hba.conf for security
            hba_config = f"""
# Arena Doviz Production Authentication
local   {self.db_name}   {self.db_user}   scram-sha-256
host    {self.db_name}   {self.db_user}   127.0.0.1/32   scram-sha-256
host    {self.db_name}   {self.db_user}   ::1/128        scram-sha-256
"""
            
            with open(pg_hba_conf, 'a') as f:
                f.write(hba_config)
            
            print("✅ PostgreSQL authentication configured")
            
            # Restart PostgreSQL
            if os.name != 'nt':
                subprocess.run(['sudo', 'systemctl', 'restart', 'postgresql'], check=True)
                print("✅ PostgreSQL restarted")
            else:
                print("⚠️ Please restart PostgreSQL service manually on Windows")
            
            return True
            
        except Exception as e:
            print(f"❌ Configuration failed: {e}")
            return False
    
    def test_connection(self):
        """Test database connection"""
        print("🔍 Testing database connection...")
        
        try:
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user=self.db_user,
                password=self.db_password,
                database=self.db_name
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            print(f"✅ Database connection successful")
            print(f"PostgreSQL version: {version}")
            return True
            
        except psycopg2.Error as e:
            print(f"❌ Database connection failed: {e}")
            return False
    
    def create_extensions(self):
        """Create useful PostgreSQL extensions"""
        print("🔧 Creating PostgreSQL extensions...")
        
        extensions = [
            'uuid-ossp',
            'pg_stat_statements',
            'pg_trgm',
            'btree_gin',
            'btree_gist'
        ]
        
        try:
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user=self.db_user,
                password=self.db_password,
                database=self.db_name
            )
            
            cursor = conn.cursor()
            
            for extension in extensions:
                try:
                    cursor.execute(f"CREATE EXTENSION IF NOT EXISTS \"{extension}\"")
                    print(f"✅ Extension {extension} created")
                except psycopg2.Error as e:
                    print(f"⚠️ Could not create extension {extension}: {e}")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return True
            
        except psycopg2.Error as e:
            print(f"❌ Extension creation failed: {e}")
            return False
    
    def setup_monitoring(self):
        """Setup database monitoring"""
        print("📊 Setting up database monitoring...")
        
        monitoring_sql = """
        -- Create monitoring views
        CREATE OR REPLACE VIEW arena_db_stats AS
        SELECT 
            schemaname,
            tablename,
            attname,
            n_distinct,
            correlation
        FROM pg_stats 
        WHERE schemaname NOT IN ('information_schema', 'pg_catalog');
        
        -- Create performance monitoring function
        CREATE OR REPLACE FUNCTION arena_performance_report()
        RETURNS TABLE(
            query text,
            calls bigint,
            total_time double precision,
            mean_time double precision
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                pg_stat_statements.query,
                pg_stat_statements.calls,
                pg_stat_statements.total_exec_time,
                pg_stat_statements.mean_exec_time
            FROM pg_stat_statements
            ORDER BY total_exec_time DESC
            LIMIT 20;
        END;
        $$ LANGUAGE plpgsql;
        """
        
        try:
            conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                user=self.db_user,
                password=self.db_password,
                database=self.db_name
            )
            
            cursor = conn.cursor()
            cursor.execute(monitoring_sql)
            conn.commit()
            
            cursor.close()
            conn.close()
            
            print("✅ Database monitoring setup complete")
            return True
            
        except psycopg2.Error as e:
            print(f"❌ Monitoring setup failed: {e}")
            return False
    
    def run_complete_setup(self):
        """Run complete PostgreSQL setup"""
        print("🚀 Starting Complete PostgreSQL Setup for Arena Doviz")
        print("=" * 60)
        
        steps = [
            ("Installing PostgreSQL", self.install_postgresql),
            ("Creating database and user", self.create_database_and_user),
            ("Configuring PostgreSQL", self.configure_postgresql),
            ("Testing connection", self.test_connection),
            ("Creating extensions", self.create_extensions),
            ("Setting up monitoring", self.setup_monitoring)
        ]
        
        for step_name, step_func in steps:
            print(f"\n{step_name}...")
            if not step_func():
                print(f"❌ {step_name} failed. Stopping setup.")
                return False
        
        print("\n" + "=" * 60)
        print("🎉 PostgreSQL Setup Complete!")
        print("=" * 60)
        print(f"Database: {self.db_name}")
        print(f"User: {self.db_user}")
        print(f"Host: {self.db_host}")
        print(f"Port: {self.db_port}")
        print("\n📋 Next Steps:")
        print("1. Update .env.production with database credentials")
        print("2. Run Django migrations")
        print("3. Create Django superuser")
        print("4. Load initial data")
        
        return True

def main():
    """Main function"""
    setup = PostgreSQLSetup()
    setup.run_complete_setup()

if __name__ == "__main__":
    main()
