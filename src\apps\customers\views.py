"""
API views for Arena Doviz Customers app.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
from .models import Customer, CustomerContact, CustomerDocument
from .serializers import (
    CustomerSerializer, CustomerListSerializer, CustomerCreateSerializer,
    CustomerContactSerializer, CustomerDocumentSerializer,
    CustomerStatsSerializer, CustomerBalanceSerializer
)
from apps.core.utils import log_user_action, get_client_ip
import logging

logger = logging.getLogger(__name__)


class CustomerViewSet(viewsets.ModelViewSet):
    """ViewSet for Customer management."""
    
    queryset = Customer.objects.filter(is_deleted=False)
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['first_name', 'last_name', 'company_name', 'customer_code', 'phone_number', 'email']
    ordering_fields = ['customer_code', 'registration_date', 'last_transaction_date']
    ordering = ['-registration_date']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return CustomerListSerializer
        elif self.action == 'create':
            return CustomerCreateSerializer
        return CustomerSerializer

    def check_object_permissions(self, request, obj):
        """Check permissions for object-level operations."""
        super().check_object_permissions(request, obj)

        # Viewers can only read
        if request.user.role == 'viewer' and request.method not in ['GET', 'HEAD', 'OPTIONS']:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("Viewers can only read customer data.")
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = self.queryset
        
        # Filter by customer type
        customer_type = self.request.query_params.get('customer_type')
        if customer_type:
            queryset = queryset.filter(customer_type=customer_type)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by location
        location = self.request.query_params.get('location')
        if location:
            queryset = queryset.filter(preferred_location_id=location)
        
        # Filter by risk level
        risk_level = self.request.query_params.get('risk_level')
        if risk_level:
            queryset = queryset.filter(risk_level=risk_level)
        
        # Filter by registration date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        
        if date_from:
            queryset = queryset.filter(registration_date__gte=date_from)
        
        if date_to:
            queryset = queryset.filter(registration_date__lte=date_to)
        
        return queryset.select_related('preferred_currency', 'preferred_location')
    
    def perform_create(self, serializer):
        """Create customer with audit logging."""
        customer = serializer.save()
        
        log_user_action(
            user=self.request.user,
            action='create',
            model_name='Customer',
            object_id=str(customer.id),
            object_repr=str(customer),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Customer created: {customer.customer_code} by {self.request.user.username}")

    def create(self, request, *args, **kwargs):
        """Create a new customer with enhanced error handling."""
        try:
            logger.info(f"Customer creation request from {request.user}: {request.data}")

            # Validate required fields before serialization
            required_fields = ['first_name', 'phone_number', 'id_type', 'id_number']
            missing_fields = []

            for field in required_fields:
                if field not in request.data or not request.data[field]:
                    missing_fields.append(field)

            # Check customer type specific requirements
            customer_type = request.data.get('customer_type', 'individual')
            if customer_type == 'corporate':
                if not request.data.get('company_name'):
                    missing_fields.append('company_name')
            else:
                if not request.data.get('last_name'):
                    missing_fields.append('last_name')

            if missing_fields:
                logger.warning(f"Missing required fields: {missing_fields}")
                return Response({
                    'error': 'Missing required fields',
                    'missing_fields': missing_fields,
                    'details': f"The following fields are required: {', '.join(missing_fields)}"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create the customer using the create serializer
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)

            # Return the full customer data using the main serializer
            customer = serializer.instance
            response_serializer = CustomerSerializer(customer, context={'request': request})
            headers = self.get_success_headers(response_serializer.data)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED, headers=headers)

        except Exception as e:
            logger.error(f"Customer creation failed: {str(e)}", exc_info=True)

            # Return detailed error information
            error_response = {
                'error': 'Customer creation failed',
                'message': str(e),
                'request_data': request.data
            }

            # Add validation errors if available
            if hasattr(e, 'detail'):
                error_response['validation_errors'] = e.detail

            return Response(error_response, status=status.HTTP_400_BAD_REQUEST)

    def perform_update(self, serializer):
        """Update customer with audit logging."""
        old_instance = self.get_object()
        customer = serializer.save()
        
        log_user_action(
            user=self.request.user,
            action='update',
            model_name='Customer',
            object_id=str(customer.id),
            object_repr=str(customer),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Customer updated: {customer.customer_code} by {self.request.user.username}")
    
    def perform_destroy(self, instance):
        """Soft delete customer with audit logging."""
        instance.delete(user=self.request.user)
        
        log_user_action(
            user=self.request.user,
            action='delete',
            model_name='Customer',
            object_id=str(instance.id),
            object_repr=str(instance),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Customer deleted: {instance.customer_code} by {self.request.user.username}")
    
    @action(detail=True, methods=['get'])
    def balance(self, request, pk=None):
        """Get customer balance summary."""
        customer = self.get_object()

        try:
            balance_summary = customer.get_balance_summary()

            # Format balance data
            balance_data = []
            for currency_code, balance_info in balance_summary.items():
                balance_data.append({
                    'customer_id': customer.id,
                    'customer_name': customer.get_display_name(),
                    'currency_code': currency_code,
                    'balance': balance_info['amount'],
                    'formatted_balance': f"{balance_info['symbol']} {balance_info['amount']:,.2f}",
                    'last_updated': timezone.now()
                })

            serializer = CustomerBalanceSerializer(balance_data, many=True)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error getting customer balance for {customer.customer_code}: {str(e)}")
            return Response(
                {'error': 'Failed to load customer balance'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get individual customer statistics."""
        customer = self.get_object()

        try:
            from apps.transactions.models import Transaction
            from django.db.models import Sum, Count, Avg

            # Get customer transactions
            transactions = Transaction.objects.filter(
                customer=customer,
                is_deleted=False
            )

            # Calculate statistics
            total_transactions = transactions.count()

            # Get total volume (sum of all transaction amounts)
            # Use from_amount as the primary transaction amount
            total_volume = transactions.aggregate(
                total=Sum('from_amount')
            )['total'] or 0

            # Get average transaction amount
            avg_transaction = transactions.aggregate(
                avg=Avg('from_amount')
            )['avg'] or 0

            # Get last transaction date
            last_transaction = transactions.order_by('-created_at').first()
            last_transaction_date = last_transaction.created_at if last_transaction else None

            stats_data = {
                'total_transactions': total_transactions,
                'total_volume': f"${total_volume:,.2f}",
                'avg_transaction_amount': f"${avg_transaction:,.2f}",
                'last_transaction_date': last_transaction_date.isoformat() if last_transaction_date else None
            }

            return Response(stats_data)

        except Exception as e:
            logger.error(f"Error getting customer stats for {customer.customer_code}: {str(e)}")
            return Response(
                {'error': 'Failed to load customer statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
        except Exception as e:
            logger.error(f"Error getting balance for customer {customer.customer_code}: {e}")
            return Response(
                {'error': 'Failed to retrieve balance information'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate customer."""
        customer = self.get_object()
        
        if customer.status != Customer.Status.ACTIVE:
            customer.status = Customer.Status.ACTIVE
            customer.save(update_fields=['status'])
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='Customer',
                object_id=str(customer.id),
                object_repr=str(customer),
                ip_address=get_client_ip(request),
                additional_data={'status_changed': 'activated'}
            )
            
            return Response({'message': 'Customer activated successfully'})
        
        return Response({'message': 'Customer is already active'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate customer."""
        customer = self.get_object()
        
        if customer.status == Customer.Status.ACTIVE:
            customer.status = Customer.Status.INACTIVE
            customer.save(update_fields=['status'])
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='Customer',
                object_id=str(customer.id),
                object_repr=str(customer),
                ip_address=get_client_ip(request),
                additional_data={'status_changed': 'deactivated'}
            )
            
            return Response({'message': 'Customer deactivated successfully'})
        
        return Response({'message': 'Customer is not active'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def overview_stats(self, request):
        """Get customer overview statistics."""
        # Calculate statistics
        total_customers = Customer.objects.filter(is_deleted=False).count()
        active_customers = Customer.objects.filter(status=Customer.Status.ACTIVE, is_deleted=False).count()
        individual_customers = Customer.objects.filter(
            customer_type=Customer.CustomerType.INDIVIDUAL, is_deleted=False
        ).count()
        corporate_customers = Customer.objects.filter(
            customer_type=Customer.CustomerType.CORPORATE, is_deleted=False
        ).count()
        
        # Customers by status
        customers_by_status = dict(
            Customer.objects.filter(is_deleted=False)
            .values('status')
            .annotate(count=Count('id'))
            .values_list('status', 'count')
        )
        
        # Customers by location
        customers_by_location = dict(
            Customer.objects.filter(is_deleted=False, preferred_location__isnull=False)
            .values('preferred_location__name')
            .annotate(count=Count('id'))
            .values_list('preferred_location__name', 'count')
        )
        
        # Customers by risk level
        customers_by_risk_level = dict(
            Customer.objects.filter(is_deleted=False)
            .values('risk_level')
            .annotate(count=Count('id'))
            .values_list('risk_level', 'count')
        )
        
        # New customers this month
        this_month = timezone.now().replace(day=1)
        new_customers_this_month = Customer.objects.filter(
            registration_date__gte=this_month, is_deleted=False
        ).count()
        
        # Customers with negative balance (placeholder - would need actual balance calculation)
        customers_with_negative_balance = 0  # TODO: Implement actual calculation
        
        stats_data = {
            'total_customers': total_customers,
            'active_customers': active_customers,
            'individual_customers': individual_customers,
            'corporate_customers': corporate_customers,
            'customers_by_status': customers_by_status,
            'customers_by_location': customers_by_location,
            'customers_by_risk_level': customers_by_risk_level,
            'new_customers_this_month': new_customers_this_month,
            'customers_with_negative_balance': customers_with_negative_balance
        }
        
        serializer = CustomerStatsSerializer(stats_data)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def transactions(self, request, pk=None):
        """Get customer transaction history."""
        customer = self.get_object()

        # Get transactions for this customer
        from apps.transactions.models import Transaction
        from apps.transactions.serializers import TransactionListSerializer

        transactions = Transaction.objects.filter(
            customer=customer,
            is_deleted=False
        ).order_by('-created_at')

        # Apply pagination
        page = self.paginate_queryset(transactions)
        if page is not None:
            serializer = TransactionListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = TransactionListSerializer(transactions, many=True)
        return Response({'results': serializer.data})
    
    @action(detail=False, methods=['get'])
    def search(self, request):
        """Advanced customer search."""
        query = request.query_params.get('q', '')
        
        if not query:
            return Response({'results': []})
        
        # Search across multiple fields
        customers = Customer.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(company_name__icontains=query) |
            Q(customer_code__icontains=query) |
            Q(phone_number__icontains=query) |
            Q(email__icontains=query),
            is_deleted=False
        )[:20]  # Limit to 20 results
        
        serializer = CustomerListSerializer(customers, many=True)
        return Response({'results': serializer.data})

    @action(detail=False, methods=['get'])
    def export(self, request):
        """Export customers to CSV format."""
        try:
            from django.http import HttpResponse
            import csv
            from datetime import datetime

            # Get filtered queryset
            queryset = self.filter_queryset(self.get_queryset())

            # Get format parameter
            export_format = request.query_params.get('format', 'csv').lower()

            if export_format not in ['csv', 'excel']:
                return Response(
                    {'error': 'Invalid format. Supported formats: csv, excel'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create response
            if export_format == 'csv':
                response = HttpResponse(content_type='text/csv')
                response['Content-Disposition'] = f'attachment; filename="customers_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'

                writer = csv.writer(response)

                # Write header
                writer.writerow([
                    'Customer Code',
                    'Name',
                    'Type',
                    'Phone',
                    'Email',
                    'Status',
                    'Registration Date',
                    'Location',
                    'Created By'
                ])

                # Write data
                for customer in queryset:
                    writer.writerow([
                        customer.customer_code,
                        customer.get_display_name(),
                        customer.get_customer_type_display(),
                        customer.phone_number or 'N/A',
                        customer.email or 'N/A',
                        'Active' if customer.is_active else 'Inactive',
                        customer.registration_date.strftime('%Y-%m-%d') if customer.registration_date else 'N/A',
                        customer.preferred_location.name if customer.preferred_location else 'N/A',
                        customer.created_by.username if customer.created_by else 'N/A'
                    ])

                logger.info(f"Customers exported by user: {request.user.username}")
                return response

            else:  # excel format
                return Response(
                    {'error': 'Excel export not yet implemented'},
                    status=status.HTTP_501_NOT_IMPLEMENTED
                )

        except Exception as e:
            logger.error(f"Error exporting customers: {str(e)}")
            return Response(
                {'error': 'Failed to export customers'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get customer statistics."""
        try:
            queryset = self.get_queryset()

            # Basic counts
            total_customers = queryset.count()
            active_customers = queryset.filter(status=Customer.Status.ACTIVE).count()
            individual_customers = queryset.filter(customer_type=Customer.CustomerType.INDIVIDUAL).count()
            corporate_customers = queryset.filter(customer_type=Customer.CustomerType.CORPORATE).count()

            # Customers by status
            customers_by_status = {}
            for status_choice in Customer.Status.choices:
                status_value = status_choice[0]
                count = queryset.filter(status=status_value).count()
                customers_by_status[status_value] = count

            # Customers by location
            customers_by_location = {}
            from apps.locations.models import Location
            for location in Location.objects.all():
                count = queryset.filter(preferred_location=location).count()
                customers_by_location[location.name] = count

            stats = {
                'total_customers': total_customers,
                'active_customers': active_customers,
                'individual_customers': individual_customers,
                'corporate_customers': corporate_customers,
                'customers_by_status': customers_by_status,
                'customers_by_location': customers_by_location,
            }

            return Response(stats)

        except Exception as e:
            logger.error(f"Error getting customer stats: {str(e)}")
            return Response(
                {'error': 'Failed to get customer statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CustomerContactViewSet(viewsets.ModelViewSet):
    """ViewSet for CustomerContact management."""
    
    queryset = CustomerContact.objects.filter(is_deleted=False)
    serializer_class = CustomerContactSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter contacts by customer if specified."""
        queryset = self.queryset
        
        customer_id = self.request.query_params.get('customer')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)
        
        return queryset
    
    def perform_create(self, serializer):
        """Create contact with audit logging."""
        contact = serializer.save()
        
        log_user_action(
            user=self.request.user,
            action='create',
            model_name='CustomerContact',
            object_id=str(contact.id),
            object_repr=str(contact),
            ip_address=get_client_ip(self.request)
        )


class CustomerDocumentViewSet(viewsets.ModelViewSet):
    """ViewSet for CustomerDocument management."""
    
    queryset = CustomerDocument.objects.filter(is_deleted=False)
    serializer_class = CustomerDocumentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter documents by customer if specified."""
        queryset = self.queryset
        
        customer_id = self.request.query_params.get('customer')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)
        
        return queryset
    
    def perform_create(self, serializer):
        """Create document with audit logging."""
        document = serializer.save()

        try:
            log_user_action(
                user=self.request.user,
                action='create',
                model_name='CustomerDocument',
                object_id=str(document.id),
                object_repr=str(document),
                ip_address=get_client_ip(self.request)
            )
        except Exception as e:
            logger.error(f"Failed to log user action for document creation: {e}")
            # Don't fail the request if audit logging fails
    
    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """Verify a customer document."""
        document = self.get_object()
        
        if not document.is_verified:
            document.is_verified = True
            document.verified_by = request.user
            document.verified_at = timezone.now()
            document.save(update_fields=['is_verified', 'verified_by', 'verified_at'])
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='CustomerDocument',
                object_id=str(document.id),
                object_repr=str(document),
                ip_address=get_client_ip(request),
                additional_data={'document_verified': True}
            )
            
            return Response({'message': 'Document verified successfully'})
        
        return Response({'message': 'Document is already verified'}, status=status.HTTP_400_BAD_REQUEST)
