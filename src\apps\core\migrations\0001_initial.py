# Generated by Django 4.2.23 on 2025-08-13 08:44

import apps.core.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SystemConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('key', models.CharField(help_text='Unique key for the configuration setting', max_length=100, unique=True, verbose_name='Configuration key')),
                ('value', models.TextField(help_text='Value of the configuration setting', verbose_name='Configuration value')),
                ('description', models.TextField(blank=True, help_text='Description of what this configuration setting does', verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this configuration setting is active', verbose_name='Is active')),
            ],
            options={
                'verbose_name': 'System Configuration',
                'verbose_name_plural': 'System Configurations',
                'ordering': ['key'],
            },
        ),
        migrations.CreateModel(
            name='EncryptedSystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('key', models.CharField(help_text='Unique identifier for the setting', max_length=100, unique=True, verbose_name='Setting Key')),
                ('encrypted_value', apps.core.fields.EncryptedTextField(blank=True, help_text='Encrypted value for sensitive settings', null=True, verbose_name='Encrypted Value')),
                ('plain_value', models.TextField(blank=True, help_text='Plain text value for non-sensitive settings', null=True, verbose_name='Plain Value')),
                ('description', models.TextField(blank=True, help_text='Description of what this setting controls', verbose_name='Description')),
                ('is_sensitive', models.BooleanField(default=False, help_text='Whether this setting contains sensitive data', verbose_name='Is Sensitive')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this setting is active', verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Encrypted System Setting',
                'verbose_name_plural': 'Encrypted System Settings',
                'db_table': 'core_encrypted_system_settings',
                'ordering': ['key'],
                'indexes': [models.Index(fields=['key'], name='core_encryp_key_15e3ca_idx'), models.Index(fields=['is_sensitive'], name='core_encryp_is_sens_b9bf81_idx'), models.Index(fields=['is_active'], name='core_encryp_is_acti_7c9b7b_idx')],
            },
        ),
        migrations.CreateModel(
            name='EncryptedAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated at')),
                ('action_type', models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('delete', 'Delete'), ('login', 'Login'), ('logout', 'Logout'), ('transaction', 'Transaction'), ('balance_change', 'Balance Change'), ('system', 'System'), ('encryption', 'Encryption')], help_text='Type of action performed', max_length=20, verbose_name='Action Type')),
                ('table_name', models.CharField(help_text='Database table affected by the action', max_length=100, verbose_name='Table Name')),
                ('record_id', models.PositiveIntegerField(help_text='ID of the affected record', verbose_name='Record ID')),
                ('user_id', models.PositiveIntegerField(blank=True, help_text='ID of the user who performed the action', null=True, verbose_name='User ID')),
                ('encrypted_old_values', apps.core.fields.EncryptedTextField(blank=True, help_text='Encrypted JSON of old field values', null=True, verbose_name='Old Values')),
                ('encrypted_new_values', apps.core.fields.EncryptedTextField(blank=True, help_text='Encrypted JSON of new field values', null=True, verbose_name='New Values')),
                ('encrypted_ip_address', apps.core.fields.EncryptedCharField(blank=True, help_text='Encrypted IP address of the user', max_length=1000, null=True, verbose_name='IP Address')),
                ('encrypted_user_agent', apps.core.fields.EncryptedTextField(blank=True, help_text='Encrypted browser user agent string', null=True, verbose_name='User Agent')),
                ('success', models.BooleanField(default=True, help_text='Whether the action was successful', verbose_name='Success')),
                ('error_message', models.TextField(blank=True, help_text='Error message if action failed', verbose_name='Error Message')),
            ],
            options={
                'verbose_name': 'Encrypted Audit Log',
                'verbose_name_plural': 'Encrypted Audit Logs',
                'db_table': 'core_encrypted_audit_log',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['action_type', 'created_at'], name='core_encryp_action__f73c3c_idx'), models.Index(fields=['table_name', 'record_id'], name='core_encryp_table_n_223e32_idx'), models.Index(fields=['user_id', 'created_at'], name='core_encryp_user_id_16c898_idx'), models.Index(fields=['success'], name='core_encryp_success_1ff5fe_idx')],
            },
        ),
    ]
