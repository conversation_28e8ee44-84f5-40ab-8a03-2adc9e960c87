"""
Arena Doviz Load Testing Script
"""
import asyncio
import aiohttp
import time
import json
import random
import statistics
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import argparse

class LoadTester:
    """Load testing for Arena Doviz"""
    
    def __init__(self, base_url, num_users=10, duration=60):
        self.base_url = base_url.rstrip('/')
        self.num_users = num_users
        self.duration = duration
        self.results = []
        self.errors = []
        
    async def authenticate(self, session):
        """Authenticate and get JWT token"""
        try:
            # Login with test credentials
            login_data = {
                'username': 'loadtest_user',
                'password': 'loadtest_password'
            }
            
            async with session.post(
                f"{self.base_url}/api/v1/accounts/jwt/token/",
                json=login_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('access')
                else:
                    print(f"Authentication failed: {response.status}")
                    return None
        except Exception as e:
            print(f"Authentication error: {str(e)}")
            return None
    
    async def make_request(self, session, method, endpoint, headers=None, data=None):
        """Make HTTP request and measure response time"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == 'GET':
                async with session.get(url, headers=headers) as response:
                    await response.text()
                    status = response.status
            elif method.upper() == 'POST':
                async with session.post(url, headers=headers, json=data) as response:
                    await response.text()
                    status = response.status
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response_time = (time.time() - start_time) * 1000
            
            return {
                'endpoint': endpoint,
                'method': method,
                'status': status,
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            error = {
                'endpoint': endpoint,
                'method': method,
                'error': str(e),
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            }
            self.errors.append(error)
            return error
    
    async def user_simulation(self, user_id):
        """Simulate realistic user behavior patterns"""
        async with aiohttp.ClientSession() as session:
            # Authenticate
            token = await self.authenticate(session)
            if not token:
                return

            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }

            start_time = time.time()
            user_type = user_id % 4  # Different user behavior patterns

            while (time.time() - start_time) < self.duration:
                if user_type == 0:
                    # Heavy transaction user
                    await self.simulate_heavy_transaction_user(session, headers)
                elif user_type == 1:
                    # Customer service user
                    await self.simulate_customer_service_user(session, headers)
                elif user_type == 2:
                    # Manager/reporting user
                    await self.simulate_manager_user(session, headers)
                else:
                    # Casual user
                    await self.simulate_casual_user(session, headers)

                # Random wait between user sessions
                await asyncio.sleep(random.uniform(2, 8))

                # Check if duration exceeded
                if (time.time() - start_time) >= self.duration:
                    break

    async def simulate_heavy_transaction_user(self, session, headers):
        """Simulate user who creates many transactions"""
        actions = [
            ('GET', '/api/v1/customers/customers/?search=test'),
            ('GET', '/api/v1/currencies/rates/current/?location=DXB'),
            ('GET', '/transactions/exchange/add/'),
            ('GET', '/api/v1/transactions/commission/preview/'),
            ('GET', '/api/v1/customers/customers/1/balance/'),
            ('GET', '/api/v1/transactions/transactions/?limit=20'),
            ('GET', '/transactions/transfer/internal/add/'),
            ('GET', '/api/v1/locations/locations/'),
        ]

        for method, endpoint in actions:
            result = await self.make_request(session, method, endpoint, headers)
            if 'error' not in result:
                self.results.append(result)
            await asyncio.sleep(random.uniform(0.5, 2))

    async def simulate_customer_service_user(self, session, headers):
        """Simulate customer service representative"""
        actions = [
            ('GET', '/api/v1/customers/customers/?search='),
            ('GET', '/api/v1/customers/customers/1/'),
            ('GET', '/api/v1/customers/customers/1/transactions/'),
            ('GET', '/api/v1/customers/customers/1/balance/'),
            ('GET', '/api/v1/transactions/transactions/?customer=1'),
            ('GET', '/customers/'),
            ('GET', '/customers/add/'),
        ]

        for method, endpoint in actions:
            result = await self.make_request(session, method, endpoint, headers)
            if 'error' not in result:
                self.results.append(result)
            await asyncio.sleep(random.uniform(1, 3))

    async def simulate_manager_user(self, session, headers):
        """Simulate manager accessing reports and analytics"""
        actions = [
            ('GET', '/reports/'),
            ('GET', '/api/v1/reports/daily-summary/'),
            ('GET', '/api/v1/reports/transaction-volume/'),
            ('GET', '/api/v1/reports/commission-summary/'),
            ('GET', '/api/v1/transactions/transactions/?status=pending'),
            ('GET', '/api/v1/currencies/rates/history/'),
            ('GET', '/dashboard/'),
        ]

        for method, endpoint in actions:
            result = await self.make_request(session, method, endpoint, headers)
            if 'error' not in result:
                self.results.append(result)
            await asyncio.sleep(random.uniform(2, 5))

    async def simulate_casual_user(self, session, headers):
        """Simulate casual user browsing"""
        actions = [
            ('GET', '/'),
            ('GET', '/dashboard/'),
            ('GET', '/api/v1/currencies/rates/current/?location=DXB'),
            ('GET', '/api/v1/transactions/transactions/?limit=10'),
            ('GET', '/health/'),
        ]

        for method, endpoint in actions:
            result = await self.make_request(session, method, endpoint, headers)
            if 'error' not in result:
                self.results.append(result)
            await asyncio.sleep(random.uniform(3, 7))
    
    async def run_load_test(self):
        """Run the load test"""
        print(f"🚀 Starting load test...")
        print(f"   Target: {self.base_url}")
        print(f"   Users: {self.num_users}")
        print(f"   Duration: {self.duration} seconds")
        print(f"   Start time: {datetime.now()}")
        
        start_time = time.time()
        
        # Create user tasks
        tasks = []
        for user_id in range(self.num_users):
            task = asyncio.create_task(self.user_simulation(user_id))
            tasks.append(task)
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        print(f"✅ Load test completed in {total_time:.2f} seconds")
        
        return self.generate_report()
    
    def generate_report(self):
        """Generate load test report"""
        if not self.results:
            return {
                'error': 'No successful requests recorded',
                'total_errors': len(self.errors)
            }
        
        # Calculate statistics
        response_times = [r['response_time'] for r in self.results]
        status_codes = [r['status'] for r in self.results]
        
        # Group by endpoint
        endpoint_stats = {}
        for result in self.results:
            endpoint = result['endpoint']
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = []
            endpoint_stats[endpoint].append(result['response_time'])
        
        # Calculate endpoint statistics
        endpoint_summary = {}
        for endpoint, times in endpoint_stats.items():
            endpoint_summary[endpoint] = {
                'requests': len(times),
                'avg_response_time': statistics.mean(times),
                'min_response_time': min(times),
                'max_response_time': max(times),
                'median_response_time': statistics.median(times)
            }
        
        report = {
            'test_summary': {
                'total_requests': len(self.results),
                'total_errors': len(self.errors),
                'success_rate': (len(self.results) / (len(self.results) + len(self.errors))) * 100,
                'avg_response_time': statistics.mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'median_response_time': statistics.median(response_times),
                'requests_per_second': len(self.results) / self.duration
            },
            'endpoint_statistics': endpoint_summary,
            'status_code_distribution': {
                str(code): status_codes.count(code) for code in set(status_codes)
            },
            'errors': self.errors[:10]  # Show first 10 errors
        }
        
        return report
    
    def print_report(self, report):
        """Print formatted report"""
        if 'error' in report:
            print(f"❌ {report['error']}")
            return
        
        summary = report['test_summary']
        
        print("\n" + "=" * 60)
        print("📊 LOAD TEST REPORT")
        print("=" * 60)
        
        print(f"Total Requests: {summary['total_requests']}")
        print(f"Total Errors: {summary['total_errors']}")
        print(f"Success Rate: {summary['success_rate']:.2f}%")
        print(f"Requests/Second: {summary['requests_per_second']:.2f}")
        
        print(f"\n⏱️ Response Times:")
        print(f"   Average: {summary['avg_response_time']:.2f}ms")
        print(f"   Minimum: {summary['min_response_time']:.2f}ms")
        print(f"   Maximum: {summary['max_response_time']:.2f}ms")
        print(f"   Median: {summary['median_response_time']:.2f}ms")
        
        print(f"\n📈 Endpoint Statistics:")
        for endpoint, stats in report['endpoint_statistics'].items():
            print(f"   {endpoint}:")
            print(f"      Requests: {stats['requests']}")
            print(f"      Avg Time: {stats['avg_response_time']:.2f}ms")
            print(f"      Min Time: {stats['min_response_time']:.2f}ms")
            print(f"      Max Time: {stats['max_response_time']:.2f}ms")
        
        if report['errors']:
            print(f"\n🚨 Sample Errors:")
            for error in report['errors'][:5]:
                print(f"   {error['endpoint']}: {error['error']}")

async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Arena Doviz Load Testing')
    parser.add_argument('--url', default='http://localhost:8000', help='Base URL')
    parser.add_argument('--users', type=int, default=10, help='Number of concurrent users')
    parser.add_argument('--duration', type=int, default=60, help='Test duration in seconds')
    parser.add_argument('--output', help='Output file for results (JSON)')
    
    args = parser.parse_args()
    
    tester = LoadTester(args.url, args.users, args.duration)
    report = await tester.run_load_test()
    
    tester.print_report(report)
    
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\n📄 Report saved to {args.output}")

if __name__ == "__main__":
    asyncio.run(main())
