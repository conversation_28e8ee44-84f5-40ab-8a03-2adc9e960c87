"""
Core models for Arena Doviz Exchange Accounting System.
Provides base models and common functionality.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
import uuid
import logging
from .fields import Encry<PERSON><PERSON><PERSON><PERSON><PERSON>, Encrypted<PERSON><PERSON>t<PERSON>ield, EncryptedEmailField

logger = logging.getLogger(__name__)


class TimeStampedModel(models.Model):
    """
    Abstract base model that provides self-updating created and modified fields.
    """
    created_at = models.DateTimeField(
        _('Created at'),
        auto_now_add=True,
        help_text=_('Date and time when the record was created')
    )
    updated_at = models.DateTimeField(
        _('Updated at'),
        auto_now=True,
        help_text=_('Date and time when the record was last updated')
    )
    
    class Meta:
        abstract = True
        ordering = ['-created_at']


class UUIDModel(models.Model):
    """
    Abstract base model that provides a UUID primary key.
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text=_('Unique identifier for this record')
    )
    
    class Meta:
        abstract = True


class AuditModel(TimeStampedModel):
    """
    Abstract base model that provides audit trail functionality.
    """
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        related_name='%(class)s_created',
        null=True,
        blank=True,
        verbose_name=_('Created by'),
        help_text=_('User who created this record')
    )
    updated_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        related_name='%(class)s_updated',
        null=True,
        blank=True,
        verbose_name=_('Updated by'),
        help_text=_('User who last updated this record')
    )
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """
        Override save to automatically set created_by and updated_by fields.
        """
        user = kwargs.pop('user', None)
        
        if user and hasattr(user, 'id'):
            if not self.pk:  # New record
                self.created_by = user
            self.updated_by = user
        
        super().save(*args, **kwargs)
        
        # Log the save operation
        action = 'created' if not self.pk else 'updated'
        logger.info(
            f"{self.__class__.__name__} {action}: {self.pk} by user {user.id if user else 'system'}"
        )


class SoftDeleteModel(models.Model):
    """
    Abstract base model that provides soft delete functionality.
    """
    is_deleted = models.BooleanField(
        _('Is deleted'),
        default=False,
        help_text=_('Whether this record has been soft deleted')
    )
    deleted_at = models.DateTimeField(
        _('Deleted at'),
        null=True,
        blank=True,
        help_text=_('Date and time when the record was deleted')
    )
    deleted_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        related_name='%(class)s_deleted',
        null=True,
        blank=True,
        verbose_name=_('Deleted by'),
        help_text=_('User who deleted this record')
    )
    
    class Meta:
        abstract = True
    
    def delete(self, user=None, *args, **kwargs):
        """
        Soft delete the record instead of actually deleting it.
        """
        from django.utils import timezone
        
        self.is_deleted = True
        self.deleted_at = timezone.now()
        if user:
            self.deleted_by = user
        
        self.save(update_fields=['is_deleted', 'deleted_at', 'deleted_by'])
        
        logger.info(
            f"{self.__class__.__name__} soft deleted: {self.pk} by user {user.id if user else 'system'}"
        )
    
    def restore(self, user=None):
        """
        Restore a soft deleted record.
        """
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        
        self.save(update_fields=['is_deleted', 'deleted_at', 'deleted_by'])
        
        logger.info(
            f"{self.__class__.__name__} restored: {self.pk} by user {user.id if user else 'system'}"
        )


class BaseModel(UUIDModel, AuditModel, SoftDeleteModel):
    """
    Base model that combines UUID, audit trail, and soft delete functionality.
    """
    
    class Meta:
        abstract = True
    
    def __str__(self):
        """
        Default string representation.
        """
        return f"{self.__class__.__name__} ({self.pk})"


class SystemConfiguration(TimeStampedModel):
    """
    Model for storing system-wide configuration settings.
    """
    key = models.CharField(
        _('Configuration key'),
        max_length=100,
        unique=True,
        help_text=_('Unique key for the configuration setting')
    )
    value = models.TextField(
        _('Configuration value'),
        help_text=_('Value of the configuration setting')
    )
    description = models.TextField(
        _('Description'),
        blank=True,
        help_text=_('Description of what this configuration setting does')
    )
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this configuration setting is active')
    )
    
    class Meta:
        app_label = 'core'
        verbose_name = _('System Configuration')
        verbose_name_plural = _('System Configurations')
        ordering = ['key']
    
    def __str__(self):
        return f"{self.key}: {self.value[:50]}..."
    
    @classmethod
    def get_value(cls, key, default=None):
        """
        Get a configuration value by key.
        """
        try:
            config = cls.objects.get(key=key, is_active=True)
            return config.value
        except cls.DoesNotExist:
            logger.warning(f"Configuration key '{key}' not found, using default: {default}")
            return default
    
    @classmethod
    def set_value(cls, key, value, description='', user=None):
        """
        Set a configuration value.
        """
        config, created = cls.objects.get_or_create(
            key=key,
            defaults={
                'value': value,
                'description': description,
            }
        )
        
        if not created:
            config.value = value
            config.description = description
            config.save()
        
        action = 'created' if created else 'updated'
        logger.info(f"Configuration {action}: {key} = {value} by user {user.id if user else 'system'}")
        
        return config


class EncryptedSystemSetting(TimeStampedModel):
    """
    System settings model with encrypted values for sensitive configurations.
    Extends SystemConfiguration with encryption capabilities.
    """

    key = models.CharField(
        _('Setting Key'),
        max_length=100,
        unique=True,
        help_text=_('Unique identifier for the setting')
    )

    # Encrypted value for sensitive settings
    encrypted_value = EncryptedTextField(
        _('Encrypted Value'),
        blank=True,
        null=True,
        help_text=_('Encrypted value for sensitive settings')
    )

    # Plain value for non-sensitive settings
    plain_value = models.TextField(
        _('Plain Value'),
        blank=True,
        null=True,
        help_text=_('Plain text value for non-sensitive settings')
    )

    description = models.TextField(
        _('Description'),
        blank=True,
        help_text=_('Description of what this setting controls')
    )

    is_sensitive = models.BooleanField(
        _('Is Sensitive'),
        default=False,
        help_text=_('Whether this setting contains sensitive data')
    )

    is_active = models.BooleanField(
        _('Is Active'),
        default=True,
        help_text=_('Whether this setting is active')
    )

    class Meta:
        app_label = 'core'
        verbose_name = _('Encrypted System Setting')
        verbose_name_plural = _('Encrypted System Settings')
        db_table = 'core_encrypted_system_settings'
        ordering = ['key']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['is_sensitive']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.key} ({'encrypted' if self.is_sensitive else 'plain'})"

    def get_value(self):
        """Get the setting value (encrypted or plain based on is_sensitive flag)."""
        if self.is_sensitive:
            return self.encrypted_value
        return self.plain_value

    def set_value(self, value):
        """Set the setting value (automatically chooses encrypted or plain storage)."""
        if self.is_sensitive:
            self.encrypted_value = value
            self.plain_value = None
        else:
            self.plain_value = value
            self.encrypted_value = None

    @classmethod
    def get_setting(cls, key, default=None):
        """Get a setting value by key."""
        try:
            setting = cls.objects.get(key=key, is_active=True)
            return setting.get_value()
        except cls.DoesNotExist:
            logger.warning(f"Encrypted setting key '{key}' not found, using default: {default}")
            return default

    @classmethod
    def set_setting(cls, key, value, is_sensitive=False, description=''):
        """Set a setting value by key."""
        setting, created = cls.objects.get_or_create(
            key=key,
            defaults={
                'is_sensitive': is_sensitive,
                'description': description
            }
        )
        setting.is_sensitive = is_sensitive
        setting.description = description
        setting.set_value(value)
        setting.save()

        action = 'created' if created else 'updated'
        logger.info(f"Encrypted setting {action}: {key} (sensitive: {is_sensitive})")

        return setting


class EncryptedAuditLog(TimeStampedModel):
    """
    Audit log model with encrypted sensitive data.
    Tracks user actions and system events with encrypted details for privacy.
    """

    class ActionType(models.TextChoices):
        CREATE = 'create', _('Create')
        UPDATE = 'update', _('Update')
        DELETE = 'delete', _('Delete')
        LOGIN = 'login', _('Login')
        LOGOUT = 'logout', _('Logout')
        TRANSACTION = 'transaction', _('Transaction')
        BALANCE_CHANGE = 'balance_change', _('Balance Change')
        SYSTEM = 'system', _('System')
        ENCRYPTION = 'encryption', _('Encryption')

    # Basic audit information
    action_type = models.CharField(
        _('Action Type'),
        max_length=20,
        choices=ActionType.choices,
        help_text=_('Type of action performed')
    )

    table_name = models.CharField(
        _('Table Name'),
        max_length=100,
        help_text=_('Database table affected by the action')
    )

    record_id = models.CharField(
        _('Record ID'),
        max_length=100,
        help_text=_('ID of the affected record (supports both integer and UUID)')
    )

    # User information
    user_id = models.CharField(
        _('User ID'),
        max_length=100,
        null=True,
        blank=True,
        help_text=_('ID of the user who performed the action (supports both integer and UUID)')
    )

    # Encrypted sensitive audit data
    encrypted_old_values = EncryptedTextField(
        _('Old Values'),
        blank=True,
        null=True,
        help_text=_('Encrypted JSON of old field values')
    )

    encrypted_new_values = EncryptedTextField(
        _('New Values'),
        blank=True,
        null=True,
        help_text=_('Encrypted JSON of new field values')
    )

    # Network information (encrypted for privacy)
    encrypted_ip_address = EncryptedCharField(
        _('IP Address'),
        max_length=45,  # IPv6 compatible
        blank=True,
        null=True,
        help_text=_('Encrypted IP address of the user')
    )

    encrypted_user_agent = EncryptedTextField(
        _('User Agent'),
        blank=True,
        null=True,
        help_text=_('Encrypted browser user agent string')
    )

    # Non-sensitive metadata
    success = models.BooleanField(
        _('Success'),
        default=True,
        help_text=_('Whether the action was successful')
    )

    error_message = models.TextField(
        _('Error Message'),
        blank=True,
        help_text=_('Error message if action failed')
    )

    class Meta:
        app_label = 'core'
        verbose_name = _('Encrypted Audit Log')
        verbose_name_plural = _('Encrypted Audit Logs')
        db_table = 'core_encrypted_audit_log'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['action_type', 'created_at']),
            models.Index(fields=['table_name', 'record_id']),
            models.Index(fields=['user_id', 'created_at']),
            models.Index(fields=['success']),
        ]

    def __str__(self):
        return f"{self.action_type} on {self.table_name}[{self.record_id}] at {self.created_at}"

    @classmethod
    def log_action(cls, action_type, table_name, record_id, user_id=None,
                   old_values=None, new_values=None, ip_address=None,
                   user_agent=None, success=True, error_message=''):
        """Create an encrypted audit log entry."""
        import json
        from decimal import Decimal
        from datetime import datetime, date
        import uuid

        class AuditJSONEncoder(json.JSONEncoder):
            """Custom JSON encoder for audit logging that handles UUID, Decimal, and datetime objects."""
            def default(self, obj):
                if isinstance(obj, uuid.UUID):
                    return str(obj)
                elif isinstance(obj, Decimal):
                    return float(obj)
                elif isinstance(obj, (datetime, date)):
                    return obj.isoformat()
                return super().default(obj)

        try:
            log_entry = cls.objects.create(
                action_type=action_type,
                table_name=table_name,
                record_id=str(record_id) if record_id is not None else '0',
                user_id=str(user_id) if user_id is not None else None,
                encrypted_old_values=json.dumps(old_values, cls=AuditJSONEncoder, ensure_ascii=False) if old_values else None,
                encrypted_new_values=json.dumps(new_values, cls=AuditJSONEncoder, ensure_ascii=False) if new_values else None,
                encrypted_ip_address=ip_address,
                encrypted_user_agent=user_agent,
                success=success,
                error_message=error_message
            )

            logger.debug(f"Encrypted audit log created: {log_entry.pk}")
            return log_entry

        except Exception as e:
            logger.error(f"Failed to create encrypted audit log: {str(e)}")
            # Don't raise exception to avoid breaking the main operation
            return None
