# Transaction Issues Debug Guide

## 🔍 **Issues Identified**

Based on the console errors, there are two main issues:

1. **Transaction Approval Failing** (400 Bad Request)
   - URL: `POST /api/v1/transactions/transactions/{id}/approve/`
   - Likely cause: Transaction not in correct status for approval

2. **Document Upload Failing** (400 Bad Request)
   - URL: `POST /api/v1/transactions/documents/bulk_upload/`
   - Error: "The submitted data was not a file. Check the encoding type on the form."

## ✅ **Fixes Applied**

### **1. Enhanced Transaction Approval Debugging**
- **Added**: Detailed logging for approval attempts
- **Added**: Current transaction status in error messages
- **Added**: Allowed statuses information in error response
- **Result**: Better error messages to identify why approval fails

### **2. Enhanced Document Upload Debugging**
- **Added**: Comprehensive logging for file upload requests
- **Added**: File validation debugging in serializer
- **Added**: Request data structure logging
- **Result**: Can now identify exactly what's wrong with file uploads

## 🧪 **Testing Instructions**

### **Test Transaction Approval**
1. Navigate to a transaction detail page
2. Try to approve the transaction
3. **Check server logs** for detailed error messages:
   ```
   INFO Transaction approval attempt: TXN-001 (Status: completed) by admin
   WARNING Transaction TXN-001 cannot be approved - current status: completed
   ```

### **Test Document Upload**
1. Create a new transaction with documents
2. **Check browser console** for upload errors
3. **Check server logs** for detailed file information:
   ```
   INFO Bulk document upload request from admin
   INFO Request data keys: ['transaction_id', 'document_type']
   INFO Request FILES keys: ['files']
   INFO Files found: 2
   INFO File 0: document.pdf (1024 bytes, application/pdf)
   ```

## 🔧 **Common Issues & Solutions**

### **Transaction Approval Issues**

#### **Issue**: "Transaction cannot be approved in its current state"
**Cause**: Transaction is not in DRAFT or PENDING status
**Solution**: Check transaction status - only DRAFT and PENDING transactions can be approved

**Allowed statuses for approval:**
- `draft` - Draft transactions
- `pending` - Pending approval transactions

**Not allowed:**
- `approved` - Already approved
- `completed` - Already completed
- `cancelled` - Cancelled transactions
- `rejected` - Rejected transactions

#### **Issue**: "You do not have permission to approve transactions"
**Cause**: User doesn't have approval permissions
**Solution**: Check user permissions - user needs `can_approve_transactions()` permission

### **Document Upload Issues**

#### **Issue**: "The submitted data was not a file"
**Possible Causes:**
1. **Form encoding issue** - Form not using `multipart/form-data`
2. **File field naming** - Files not properly named in FormData
3. **Empty file list** - No files actually selected
4. **CSRF token issue** - Missing or invalid CSRF token

**Debug Steps:**
1. Check if files are actually selected in the form
2. Verify FormData contains files with correct field names
3. Check server logs for file validation details
4. Verify CSRF token is included in headers

#### **Issue**: File validation errors
**Common Causes:**
1. **File too large** - Individual files > 10MB or total > 50MB
2. **Invalid file type** - File extension not in allowed list
3. **Missing transaction** - Transaction ID not found or no access

**Allowed file types:**
- Documents: `pdf`, `doc`, `docx`, `txt`
- Images: `jpg`, `jpeg`, `png`, `gif`
- Spreadsheets: `xls`, `xlsx`, `csv`

## 📋 **Debug Commands**

### **Check Transaction Status**
```javascript
// In browser console on transaction page
console.log('Transaction ID:', window.location.pathname.split('/')[2]);

// Check transaction status via API
fetch(`/api/v1/transactions/transactions/${transactionId}/`, {
    headers: {'Authorization': 'Bearer ' + localStorage.getItem('arena_access_token')}
})
.then(r => r.json())
.then(data => console.log('Transaction status:', data.status));
```

### **Check File Upload Data**
```javascript
// In browser console during upload
const formData = new FormData();
formData.append('transaction_id', 'your-transaction-id');
formData.append('document_type', 'receipt');
formData.append('files', document.getElementById('document_files').files[0]);

console.log('FormData entries:');
for (let [key, value] of formData.entries()) {
    console.log(key, value);
}
```

## 🚨 **Server Log Monitoring**

Watch for these log messages to debug issues:

### **Transaction Approval Logs**
```
INFO Transaction approval attempt: TXN-001 (Status: pending) by admin
INFO Transaction approved: TXN-001 by admin
```

### **Document Upload Logs**
```
INFO Bulk document upload request from admin
INFO Request data keys: ['transaction_id', 'document_type']
INFO Files found: 1
INFO File 0: receipt.pdf (2048 bytes, application/pdf)
INFO Validating 1 files
INFO Document created: Receipt: receipt for transaction TXN-001
```

## 🔄 **Next Steps**

1. **Test transaction approval** with the enhanced error messages
2. **Test document upload** with the detailed logging
3. **Check server logs** for specific error details
4. **Report findings** - what specific errors are shown in logs

The enhanced debugging should now provide clear information about what's causing both the transaction approval and document upload failures.
